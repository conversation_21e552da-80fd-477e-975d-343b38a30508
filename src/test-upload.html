<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload组件删除按钮测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .image-item {
            position: relative;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .image-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
            display: block;
        }
        
        .delete-button {
            position: absolute;
            top: 8px;
            right: 8px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.8);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .delete-button:hover {
            background: rgba(220, 38, 38, 0.9);
            border-color: white;
            transform: scale(1.15);
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
        }
        
        .delete-button-enhanced {
            background: rgba(0, 0, 0, 0.8) !important;
            border: 2px solid rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(8px);
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(0, 0, 0, 0.2) inset;
            width: 26px !important;
            height: 26px !important;
        }
        
        .delete-button-enhanced:hover {
            background: rgba(220, 38, 38, 0.95) !important;
            border-color: white !important;
            transform: scale(1.2);
            box-shadow: 0 5px 15px rgba(220, 38, 38, 0.5), 0 0 0 3px rgba(255, 255, 255, 0.9);
        }
        
        .instructions {
            background: #f0f8ff;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #1890ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Upload组件删除按钮样式测试</h1>
        
        <div class="instructions">
            <strong>测试说明：</strong>
            <p>这个页面用于测试删除按钮在不同背景图片上的可见性。请检查白色背景图片上的删除按钮是否清晰可见。</p>
        </div>
        
        <div class="test-section">
            <div class="test-title">原始样式（问题演示）</div>
            <div class="image-grid">
                <div class="image-item">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMDAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaaguiJsuiwg+WbvueJhzwvdGV4dD4KPC9zdmc+" alt="黑色调图片">
                    <div class="delete-button">×</div>
                </div>
                <div class="image-item">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPueZveiJsuiwg+WbvueJhzwvdGV4dD4KPC9zdmc+" alt="白色调图片">
                    <div class="delete-button">×</div>
                </div>
            </div>
            <p style="color: #666; margin-top: 10px;">注意：白色背景图片上的删除按钮几乎看不见</p>
        </div>
        
        <div class="test-section">
            <div class="test-title">优化后样式（解决方案）</div>
            <div class="image-grid">
                <div class="image-item">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMDAwIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaaguiJsuiwg+WbvueJhzwvdGV4dD4KPC9zdmc+" alt="黑色调图片">
                    <div class="delete-button delete-button-enhanced">×</div>
                </div>
                <div class="image-item">
                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZmZmIi8+CiAgPHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0iIzMzMyIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPueZveaaguiJsuiwg+WbvueJhzwvdGV4dD4KPC9zdmc+" alt="白色调图片">
                    <div class="delete-button delete-button-enhanced">×</div>
                </div>
            </div>
            <p style="color: #28a745; margin-top: 10px;">✓ 现在两种背景上的删除按钮都清晰可见</p>
        </div>
        
        <div class="test-section">
            <div class="test-title">CSS选择器测试</div>
            <p>请在浏览器开发者工具中检查Semi UI Upload组件的实际CSS类名：</p>
            <ul>
                <li><code>.semi-upload-file-card .semi-upload-file-card-remove</code></li>
                <li><code>.semi-upload-picture-card .semi-upload-picture-card-remove</code></li>
                <li><code>.semi-upload [class*="remove"]</code></li>
            </ul>
        </div>
    </div>
    
    <script>
        // 添加点击事件来测试交互
        document.querySelectorAll('.delete-button').forEach(button => {
            button.addEventListener('click', function() {
                alert('删除按钮被点击！');
            });
        });
    </script>
</body>
</html>
