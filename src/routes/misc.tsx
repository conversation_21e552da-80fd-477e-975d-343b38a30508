import { MessagePage } from "pages/basicInfo";
import EnergyXunqiaoModule from "pages/energyManagement/energyXunqiaoModule";
import EnvironmentXunqiaoModule from "pages/environmentalProtection/environmentXunqiaoModule";
import { CustomizedXunqiaoTopologyPage } from "pages/fireFighter";
import FireFighterXunqiaoModule from "pages/fireFighter/fireFighterXunqiaoModule";
import { AlarmPage } from "pages/majorHazard";
import { PersonnelLocationUrlPage } from "pages/personnelLocation";
import {
  AIRecognitionRoutes,
  EnergyManagementRoutes,
  EnvironmentManagementRoutes,
  FireFighterRoutes,
  MessageRoute,
  PersonnelLocationRoutes,
} from "utils/routerConstants";
import { ChildrenMap } from "./types";
import { generateLoader } from "./utils";

export const PersonnelLocationMap: ChildrenMap[] = generateLoader(
  [
    {
      path: PersonnelLocationRoutes.PERSONNEL_LOCATION, // "/personnel_location",
      element: <PersonnelLocationUrlPage />,
      name: "人员定位管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
  ],
  500
);

export const AIMap: ChildrenMap[] = generateLoader(
  [
    {
      path: AIRecognitionRoutes.VIDEO_AI, // "/video_ai",
      // element: <AlarmPage filter={{ type: 5 }} />,
      element: <AlarmPage />,
      name: "视频监控AI分析",
      meta: [
        {
          action: "confirm",
          name: "核实",
        },
        {
          action: "refuse",
          name: "误报",
        },
        {
          action: "confirmBatch",
          name: "批量核实",
        },
        {
          action: "refuseBatch",
          name: "批量误报",
        },
      ],
    },
  ],
  600
);

export const FireFighterModuleMap: ChildrenMap[] = generateLoader(
  [
    {
      path: FireFighterRoutes.CUSTOMIZE_XUNQIAO_TOPOLOGY,
      element: <CustomizedXunqiaoTopologyPage />,
      name: "管网图",
    },
    {
      path: FireFighterRoutes.CUSTOMIZE_XUNQIAO_CHART,
      element: <FireFighterXunqiaoModule />,
      name: "消防图表",
    },
  ],
  600
);

export const EnergyManagementMap: ChildrenMap[] = generateLoader(
  [
    {
      // path: EnergyManagementRoutes.DUMMY, // 旧的占位符路径
      // 使用一个基础路径，例如 '/energy-management/*'
      // React Router v6 使用 '/*' 来表示匹配该路径及其所有子路径
      // 具体路径应与您在路由配置中为能源管理模块设定的父路径一致
      // path: EnergyManagementRoutes.ROOT + "/*", // 例如 "/energy-management/*"
      path: EnergyManagementRoutes.CUSTOMIZE_XUNQIAO_CHART,
      element: <EnergyXunqiaoModule />, // 使用新的包装组件
      name: "能源管理",
      // meta 和其他属性根据需要保留或调整
    },
  ],
  600 // 这个数字参数的意义根据 generateLoader 的实现来定
);

export const EnvironmentManagementMap: ChildrenMap[] = generateLoader(
  [
    // 新增环保动态模块的路由
    {
      path: EnvironmentManagementRoutes.CUSTOMIZE_XUNQIAO_CHART,
      element: <EnvironmentXunqiaoModule />,
      name: "环保动态",
      // 如果需要 loader，可以参照其他模块添加，例如：
      // loader: () => redirectToDraftIfNeeded(RoleKeyPath.EnvironmentalProtection), // RoleKeyPath.EnvironmentalProtection 可能需要您在 config/index.ts 中定义
    },
  ],
  600
);

export const MessageMap: ChildrenMap[] = generateLoader(
  [
    {
      path: MessageRoute, // "/message",
      element: <MessagePage />,
      name: "消息中心",
    },
  ],
  2000
);
