import {
  AllocationPage,
  AreaPage,
  AwarenessPage,
  BbCheckPage,
  BbCheckTaskPage,
  BbRecordPage,
  BbStatPage,
  BbTaskPage,
  ChecklistPage,
  CheckRecordPage,
  DangerPage,
  DoubleGuardCcCategoryPage,
  DoubleGuardCcItemPage,
  DoubleGuardCcPlanPage,
  DoubleGuardCcTaskPage,
  DoubleGuardGcCheckTaskPage,
  DoubleGuardGcCheckTaskRecordPage,
  DoubleGuardGcTypeConfigPage,
  DoubleGuardIndexPage,
  EffectNewPage,
  EffectOldPage,
  EmergencyPage,
  EventPage,
  IdentificationPage,
  IncentivePage,
  MeasurePage,
  MobilePage,
  ObjectPage,
  SafetyPage,
  SnapPage,
  TaskListPage,
  UnitPage,
} from "pages/doubleGuard";
import { DoubleGuardRoutes } from "utils/routerConstants";
import { ChildrenMap } from "./types";
import { generateLoader } from "./utils";

export const RiskMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.DASHBOARD,
      element: <DoubleGuardIndexPage />,
      name: "双重预防机制",
    },
    {
      path: DoubleGuardRoutes.RISK_AREA, // "/riskArea",
      element: <AreaPage />,
      name: "风险分区管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "action1",
          name: "两单三卡",
        },
        {
          action: "action2",
          name: "简易模式复评",
        },
        {
          action: "action3",
          name: "计算模式复评",
        },
        {
          action: "action4",
          name: "评估历史",
        },
        {
          action: "action5",
          name: "重新绘制",
        },
        {
          action: "action6",
          name: "查询评估历史",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      index: true,
      element: <ObjectPage />,
      name: "风险分析对象",
      path: DoubleGuardRoutes.RISK_OBJECT, // "/riskObject",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "allowReport",
          name: "允许上报",
        },
        {
          action: "batchAllowReport",
          name: "批量允许上报",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.RISK_UNIT, // "/riskUnit",
      element: <UnitPage />,
      name: "风险分析单元",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "action1",
          name: "生成二维码",
        },
        {
          action: "export",
          name: "导出告知卡",
        },
        {
          action: "isstop",
          name: "是否停用",
        },
        {
          action: "history",
          name: "状态历史",
        },
        {
          action: "allowReport",
          name: "允许上报",
        },
        {
          action: "batchAllowReport",
          name: "批量允许上报",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.RISK_EVENT, // "/riskEvent",
      element: <EventPage />,
      name: "风险分析事件",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "action1",
          name: "风险评估",
        },
        {
          action: "history",
          name: "评估历史",
        },
        {
          action: "historyAll",
          name: "查询评估历史",
        },
        {
          action: "ls",
          name: "设置LS等级区间",
        },
        {
          action: "lec",
          name: "设置LEC等级区间",
        },
        {
          action: "mes",
          name: "设置MES等级区间",
        },
        {
          action: "allowReport",
          name: "允许上报",
        },
        {
          action: "batchAllowReport",
          name: "批量允许上报",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.RISK_CONTROL_MEASURE, // "/riskControlMeasure",
      element: <MeasurePage />,
      name: "风险管控措施",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "allowReport",
          name: "允许上报",
        },
        {
          action: "batchAllowReport",
          name: "批量允许上报",
        },
      ],
    },
  ],
  100
);

export const ManageCardMap: ChildrenMap[] = generateLoader(
  [
    {
      index: true,
      path: DoubleGuardRoutes.AWARENESS_CARD, // "/awareness",
      element: <AwarenessPage />,
      name: "风险应知卡",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      element: <EmergencyPage />,
      name: "应急处置卡",
      path: DoubleGuardRoutes.EMERGENCY_CARD, // "/emergency",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.SAFETY_CARD, // "/safety",
      element: <SafetyPage />,
      name: "安全承诺卡",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.IDENTIFICATION_CHECKLIST, // "/identification",
      element: <IdentificationPage />,
      name: "风险辨识清单",
    },
    {
      path: DoubleGuardRoutes.CONTROL_CHECKLIST, // "/checklist",
      element: <ChecklistPage />,
      name: "风险管控清单",
    },
  ],
  200
);

export const BbTaskMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.BB_TEMPLATE, // "/bb_template",
      element: <BbTaskPage />,
      name: "包保责任模板",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.BB_CHECK, // "/bb_check",
      element: <BbCheckPage />,
      name: "包保排查计划",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.BB_TASK, // "/bb_task",
      element: <BbCheckTaskPage />,
      name: "包保排查任务",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.BB_RECORD, // "/bb_record",
      element: <BbRecordPage />,
      name: "包保排查记录",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.BB_STAT, // "/bb_stat",
      element: <BbStatPage />,
      name: "履职统计",
    },
  ],
  300
);

export const IncentiveMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.INCENTIVE, // "/incentive",
      element: <IncentivePage />,
      name: "额外奖惩管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.EFFECT_OLD, // "/effect_old",
      element: <EffectOldPage />,
      name: "机制运行效果(旧)",
    },
    {
      path: DoubleGuardRoutes.EFFECT_NEW, // "/effect_new",
      element: <EffectNewPage />,
      name: "机制运行效果(新)",
    },
  ],
  500
);

export const CheckPlanMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.IMEI, // "/imei",
      element: <MobilePage />,
      name: "IMEI管理",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.ALLOCATION, // "/allocation",
      element: <AllocationPage />,
      name: "排查计划",
      meta: [
        {
          action: "action1",
          name: "批量分配",
        },
        {
          action: "edit",
          name: "编辑",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.TASK_LIST, // "/task_list",
      element: <TaskListPage />,
      name: "排查任务",
      meta: [
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: DoubleGuardRoutes.CHECK_RECORD, // "/check_record",
      element: <CheckRecordPage />,
      name: "排查记录",
    },
    {
      path: DoubleGuardRoutes.DANGER, // "/danger",
      element: <DangerPage />,
      name: "隐患治理",
      meta: [
        {
          action: "create1",
          name: "新增即查即改隐患",
        },
        {
          action: "create2",
          name: "新增限期整改隐患",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "allowUpload",
          name: "允许上报",
        },
        {
          action: "batchAllowUpload",
          name: "批量允许上报",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  400
);

export const SnapMap: ChildrenMap[] = generateLoader(
  [
    {
      path: DoubleGuardRoutes.SNAP, // "/snap",
      element: <SnapPage />,
      name: "随手拍",
      meta: [
        {
          action: "examine",
          name: "审核",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "export",
          name: "导出",
        },
      ],
    },
  ],
  600
);

export const EnterpriseSelfInspectionMap: ChildrenMap[] = generateLoader([
  {
    path: DoubleGuardRoutes.ENTERPRISE_SELF_INSPECTION_CATEGORY,
    element: <DoubleGuardCcCategoryPage />,
    name: "检查类别",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: DoubleGuardRoutes.ENTERPRISE_SELF_INSPECTION_ITEM,
    element: <DoubleGuardCcItemPage />,
    name: "排查库",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: DoubleGuardRoutes.ENTERPRISE_SELF_INSPECTION_PLAN,
    element: <DoubleGuardCcPlanPage />,
    name: "排查计划",
    meta: [
      {
        action: "create",
        name: "新增",
      },
      {
        action: "edit",
        name: "编辑",
      },
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
  {
    path: DoubleGuardRoutes.ENTERPRISE_SELF_INSPECTION_TASK,
    element: <DoubleGuardCcTaskPage />,
    name: "排查任务",
    meta: [
      {
        action: "remove",
        name: "删除",
      },
      {
        action: "removes",
        name: "批量删除",
      },
    ],
  },
]);

export const GovSupervisionMap: ChildrenMap[] = generateLoader(
  [
    {
      element: <DoubleGuardGcTypeConfigPage />,
      name: "专项检查配置",
      path: DoubleGuardRoutes.GOV_SUPERVISION_SETTINGS,
      meta: [
        {
          action: "edit",
          name: "编辑设置",
        },
      ],
    },
    {
      element: <DoubleGuardGcCheckTaskPage />,
      name: "专项任务下发",
      path: DoubleGuardRoutes.GOV_SUPERVISION_CHECK_TASK,
      meta: [
        {
          action: "edit",
          name: "编辑下发",
        },
      ],
    },
    {
      element: <DoubleGuardGcCheckTaskRecordPage />,
      name: "专项检查记录",
      path: DoubleGuardRoutes.GOV_SUPERVISION_CHECK_RECORD,
      meta: [
        {
          action: "dispatch",
          name: "任务分解",
        },
        {
          action: "finish",
          name: "任务完成",
        },
        {
          action: "danger",
          name: "隐患录入",
        },
      ],
    },
  ],
  1100
);
