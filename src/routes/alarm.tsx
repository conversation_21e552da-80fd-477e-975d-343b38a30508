import { AlarmIndexPage } from "pages/alarm/alarmIndexPage";
import AlarmCountAnalysisPage from "pages/alarm/analysis/alarmCountAnalysisPage";
import AlarmDurationAnalysisPage from "pages/alarm/analysis/alarmDurationAnalysisPage";
import { AlarmKpiAnalysisPage } from "pages/alarm/analysis/alarmKpiAnalysisPage";
import AlarmPriorityAnalysisPage from "pages/alarm/analysis/alarmPriorityAnalysisPage";
import AlarmProcessAnalysisPage from "pages/alarm/analysis/alarmProcessAnalysisPage";
import AlarmTimingAnalysisPage from "pages/alarm/analysis/alarmTimingAnalysisPage";
import AlarmTypeAnalysisPage from "pages/alarm/analysis/alarmTypeAnalysisPage";
import { DicPage } from "pages/basicInfo";
import { SensorAlarmPage, SensorPage } from "pages/majorHazard";
import { AlarmPushConfigPage } from "pages/system";
import { AlarmManagementRoutes } from "utils/routerConstants";
import { ChildrenMap } from "./types";
import { generateLoader } from "./utils";

export const AlarmSettingsMap: ChildrenMap[] = generateLoader(
  [
    {
      path: AlarmManagementRoutes.ALARM_DASHBOARD,
      element: <AlarmIndexPage />,
      name: "报警管理",
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_SENSOR, // "/sensor",
      element: <SensorPage />,
      name: "监测指标",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "linechart",
          name: "历史波动图",
        },
        {
          action: "resume",
          name: "启用",
        },
        {
          action: "stop",
          name: "停用",
        },
        {
          action: "batchResume",
          name: "批量启用",
        },
        {
          action: "batchStop",
          name: "批量停用",
        },
        {
          action: "import",
          name: "导入",
        },
      ],
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_RULES,
      element: <AlarmPushConfigPage />,
      name: "报警规则配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "edit",
          name: "编辑",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_MONITORTYPE,
      element: <DicPage itemId={4} />,
      name: "监控类型配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_REASON,
      element: <DicPage itemId={5} />,
      name: "报警原因配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
    {
      path: AlarmManagementRoutes.ALARM_SETTINGS_MEASURE,
      element: <DicPage itemId={6} />,
      name: "报警处置措施配置",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
      ],
    },
  ],
  2000
);

export const AlarmProcessMap: ChildrenMap[] = generateLoader(
  [
    {
      path: AlarmManagementRoutes.ALARM_PROCESS_SENSOR_ALARM,
      element: <SensorAlarmPage />,
      name: "实时报警",
      meta: [
        {
          action: "create",
          name: "新增",
        },
        {
          action: "remove",
          name: "删除",
        },
        {
          action: "removes",
          name: "批量删除",
        },
        {
          action: "alarmProcess",
          name: "处理",
        },
        {
          action: "batchAlarmProcess",
          name: "批量处理",
        },
        {
          action: "linechart",
          name: "历史波动图",
        },
      ],
    },
  ],
  2000
);

export const AlarmAnalysisMap: ChildrenMap[] = generateLoader(
  [
    {
      path: AlarmManagementRoutes.ALARM_ANALYSIS_ALARM_COUNT,
      element: <AlarmCountAnalysisPage />,
      name: "报警次数分析",
    },
    {
      path: AlarmManagementRoutes.ALARM_ANALYSIS_ALARM_DURATION,
      element: <AlarmDurationAnalysisPage />,
      name: "报警时长分析",
    },
    {
      path: AlarmManagementRoutes.ALARM_ANALYSIS_ALARM_TYPE,
      element: <AlarmTypeAnalysisPage />,
      name: "报警类型分析",
    },
    {
      path: AlarmManagementRoutes.ALARM_ANALYSIS_ALARM_PRIORITY,
      element: <AlarmPriorityAnalysisPage />,
      name: "报警优先级分析",
    },
    {
      name: "报警处置分析",
      element: <AlarmProcessAnalysisPage />,
      path: AlarmManagementRoutes.ALARM_ANALYSIS_ALARM_PROCESS,
    },
    {
      name: "报警时序分析",
      element: <AlarmTimingAnalysisPage />,
      path: AlarmManagementRoutes.ALARM_ANALYSIS_ALARM_TIMING,
    },
    {
      name: "KPI分析",
      element: <AlarmKpiAnalysisPage />,
      path: AlarmManagementRoutes.ALARM_ANALYSIS_ALARM_KPI,
    },
  ],
  2000
);
