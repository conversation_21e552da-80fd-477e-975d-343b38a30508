import { del, get, post } from "@api";

// 新增作业票
export const createJobSlice = async (params) => {
  const base_url = "/special_work/job_slice";
  const res = await post(base_url, params);
  return res;
};

export const getJobSliceInfo = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}`;
  return await get(base_url);
};

export const getJobSliceAppointment = async (id: number | null) => {
  const base_url = `/special_work/job_slice/${id}/appointment`;
  return await get(base_url);
};

export const getJobSlice = async () => {
  const base_url = `/special_work/job_slice/template`;
  return await get(base_url);
};

export const getHighWorkHeightRule = async () => {
  const base_url = `/special_work/high_work_height_rule`;
  return await get(base_url);
};

export const getJobSliceCode = async (id) => {
  const base_url = `/special_work/job_slice/category/${id}/code`;
  return await get(base_url);
};

export const getJobSlicePrint = async (id) => {
  const base_url = `/special_work/job_slice/${id}/print`;
  return await get(base_url);
};

export const getJobSliceList = async (params) => {
  const base_url = "/special_work/job_slice/search";
  const res = await post(base_url, params);
  return res;
};

// 删除单个项目
export const delJobSlice = async (id: number) => {
  const res = await del(`/special_work/job_slice/${id}`);
  return res;
};

// 批量删除
export const delJobSlices = async (ids) => {
  const res = await del(`/special_work/job_slice`, ids);
  return res;
};

export const getSafetyMeasureResult = async (id: number) => {
  // const base_url =`/special_work/job_slice/${id}/gas_analysis`
  const base_url = `/special_work/job_slice/${id}/safety_measure_result`;
  return await get(base_url);
};

export const getSafetyMeasureNumber = async (
  id: number,
  safey_measure_number: number
) => {
  const base_url = `/special_work/job_slice/${id}/safety_measure_result/${safey_measure_number}`;
  return await get(base_url);
};

export const getGasAnalysis = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}/gas_analysis`;
  // const base_url = `/special_work/job_slice/${id}/safety_measure_result`;
  return await get(base_url);
};

export const getAcceptRecord = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}/accept_record`;
  return await get(base_url);
};

export const getJobSliceProgress = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}/progress`;
  return await get(base_url);
};

export const getOnsiteRecord = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}/onsite_record`;
  return await get(base_url);
};
export const getSafetyDisclosureResult = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}/safety_disclosure_result`;
  return await get(base_url);
};

export const getApproveRecord = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}/approve`;
  return await get(base_url);
};

//v1/special_work/job_slice/{id}/

export const getRelatedJobSliceRecord = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}/related_job_slice`;
  return await get(base_url);
};

export const getJobRecord = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}/job_record`;
  return await get(base_url);
};

export const getLocationServiceProvider = async () => {
  const base_url = `/special_work/job_slice/location_service`;
  return await get(base_url);
};

export const postJobInspectionRecord = async ({ id, params }) => {
  const base_url = `/special_work/job_slice/${id}/job_inspection_record`;
  return await post(base_url, params);
};

export const getJobInspectionRecord = async (id: number) => {
  const base_url = `/special_work/job_slice/${id}/job_inspection_record`;
  return await get(base_url);
};
