import { post } from "./request";

/**
 * 蓝凌OA登录请求参数
 */
export interface LandrayLoginRequest {
  uuid: string;
}

/**
 * 蓝凌OA登录响应数据
 */
export interface LandrayLoginResponse {
  code: number;
  message: string;
  data: {
    authToken: string;
    expireTime: string;
    refreshToken: string;
    employee: {
      id: number;
      name: string;
      employeeId: string;
    };
  };
}

/**
 * 蓝凌OA单点登录API调用
 * @param params 包含UUID的登录参数
 * @returns 登录响应结果
 */
export const postLandrayLogin = async (
  params: LandrayLoginRequest
): Promise<LandrayLoginResponse> => {
  const base_url = "/system/lanling_login";
  const res = await post(base_url, params, true);
  return res as LandrayLoginResponse;
};
