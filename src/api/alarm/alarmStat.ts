import { get, post } from "api";

// 报警管理首页基础数据查询
export const getAlarmDashStat = async () => {
  const base_url = "/alarm_management/basic_info";
  return await get(base_url, undefined);
};

// 报警数量趋势统计
export const getAlarmTrendStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
}) => {
  const base_url = "/alarm_management/alarm_stat";
  return await post(base_url, params, undefined);
};

// 监测类型统计
export const getMonitorTypeStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
}) => {
  const base_url = "/alarm_management/monitor_type_stat";
  return await post(base_url, params, undefined);
};

// 报警优先级统计
export const getPriorityStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
}) => {
  const base_url = "/alarm_management/priority_stat";
  return await post(base_url, params, undefined);
};

// 区域报警统计
export const getAreaStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
  topN?: number;
}) => {
  const base_url = "/alarm_management/area_stat";
  return await post(base_url, params, undefined);
};

// 设备报警统计
export const getDeviceStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
  topN?: number;
}) => {
  const base_url = "/alarm_management/equipment_stat";
  return await post(base_url, params, undefined);
};

// 部门报警统计
export const getDepartmentStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
  topN?: number;
}) => {
  const base_url = "/alarm_management/department_stat";
  return await post(base_url, params, undefined);
};

// 区域报警时长统计
export const getAreaDurationStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
  topN?: number;
}) => {
  const base_url = "/alarm_management/area_time_stat";
  return await post(base_url, params, undefined);
};

// 设备报警时长统计
export const getDeviceDurationStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
  topN?: number;
}) => {
  const base_url = "/alarm_management/equipment_time_stat";
  return await post(base_url, params, undefined);
};

// 部门报警时长统计
export const getDepartmentDurationStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
  topN?: number;
}) => {
  const base_url = "/alarm_management/department_time_stat";
  return await post(base_url, params, undefined);
};

// 报警处置统计
export const getProcessStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
}) => {
  const base_url = "/alarm_management/process_stat";
  return await post(base_url, params, undefined);
};

// 报警原因统计
export const getReasonStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
}) => {
  const base_url = "/alarm_management/alarm_reason_stat";
  return await post(base_url, params, undefined);
};

// 报警措施统计
export const getMeasureStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
}) => {
  const base_url = "/alarm_management/alarm_measure_stat";
  return await post(base_url, params, undefined);
};

// 报警时序统计
export const getTimingStat = async (params: {
  areaId: number;
  beginTime: string;
  endTime: string;
}) => {
  const base_url = "/alarm_management/alarm_temporal_analysis";
  return await post(base_url, params, undefined);
};

// 报警部门KPI统计
export const getDepartmentKpiStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
}) => {
  const base_url = "/alarm_management/department_kpi_stat";
  return await post(base_url, params, undefined);
};

// 区域报警KPI统计
export const getAreaKpiStat = async (params: {
  areaId: number;
  beginDate: string;
  endDate: string;
}) => {
  const base_url = "/alarm_management/area_kpi_stat";
  return await post(base_url, params, undefined);
};

export default {};
