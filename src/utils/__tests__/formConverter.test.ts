import { describe, expect, it, vi } from "vitest";
import { convertForm } from "../formConverter";

// Mock config
vi.mock("config", () => ({
  base_url: "http://localhost:3000",
}));

describe("formConverter", () => {
  describe("convertForm", () => {
    describe("基础功能测试", () => {
      it("应该处理空表单数据", () => {
        const result = convertForm({}, []);
        expect(result).toEqual([]);
      });

      it("应该处理空元素配置", () => {
        const form = { test: "value" };
        const result = convertForm(form, []);
        expect(result).toEqual([]);
      });

      it("应该处理undefined元素配置", () => {
        const form = { test: "value" };
        const result = convertForm(form);
        expect(result).toEqual([]);
      });
    });

    describe("employeePicker类型处理", () => {
      it("应该处理employeePicker的数组值", () => {
        const form = {
          employees: [
            { id: "1", name: "张三" },
            { id: "2", name: "李四" },
          ],
        };
        const elements = [
          {
            business: "employees",
            compType: "employeePicker",
            itemId: "emp-picker-1",
            formData: { formName: "员工选择" },
          },
        ];

        const result = convertForm(form, elements);
        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({
          business: "employees",
          compType: "employeePicker",
          itemId: "emp-picker-1",
          formData: {
            formName: "员工选择",
            actualValue: [
              { id: "1", name: "张三" },
              { id: "2", name: "李四" },
            ],
          },
        });
      });

      it("应该处理employeePicker的字符串值", () => {
        const form = {
          employee: '{"id":"1","name":"张三"}',
        };
        const elements = [
          {
            business: "employee",
            compType: "employeePicker",
            itemId: "emp-picker-2",
            formData: { formName: "员工选择" },
          },
        ];

        const result = convertForm(form, elements);
        expect(result).toHaveLength(1);
        expect(result[0].formData.actualValue).toEqual([{ id: "1", name: "张三" }]);
      });
    });

    describe("其他类型处理", () => {
      it("应该处理普通input类型", () => {
        const form = {
          username: "张三",
        };
        const elements = [
          {
            business: "username",
            compType: "input",
            itemId: "username-id",
            formData: { formName: "用户名" },
          },
        ];

        const result = convertForm(form, elements);
        expect(result).toHaveLength(1);
        expect(result[0]).toEqual({
          business: "username",
          compType: "input",
          itemId: "username-id",
          formData: {
            formName: "用户名",
            actualValue: "张三",
          },
        });
      });
    });

    describe("边界情况测试", () => {
      it("应该过滤掉没有itemId的结果", () => {
        const form = {
          test: "value",
        };
        const elements = [
          {
            business: "test",
            compType: "input",
            // 没有itemId
            formData: { formName: "测试" },
          },
        ];

        const result = convertForm(form, elements);
        expect(result).toHaveLength(0);
      });
    });
  });
});
