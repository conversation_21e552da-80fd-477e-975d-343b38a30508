import copy from "copy-to-clipboard";

export interface CopyToClipboardOptions {
  successMessage?: string;
  errorMessage?: string;
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}

/**
 * 复制文本到剪贴板的通用函数
 * 支持多种降级方案，确保在各种环境下都能正常工作
 * @param text 要复制的文本
 * @param options 配置选项
 * @returns Promise<boolean> 复制是否成功
 */
export const copyToClipboard = async (
  text: string,
  options: CopyToClipboardOptions = {}
): Promise<boolean> => {
  const {
    successMessage = "已复制到剪贴板",
    errorMessage = "复制失败，请手动复制",
    onSuccess,
    onError,
  } = options;

  try {
    // 优先使用现代 Clipboard API (需要 HTTPS 或 localhost)
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      onSuccess?.();
      return true;
    }

    // 降级方案：使用 copy-to-clipboard 库
    const success = copy(text);
    if (success) {
      onSuccess?.();
      return true;
    }

    // 最后的降级方案：使用传统的 execCommand
    return fallbackCopyTextToClipboard(text, onSuccess, onError);
  } catch (err) {
    // 如果所有方案都失败，使用最后的降级方案
    return fallbackCopyTextToClipboard(text, onSuccess, onError);
  }
};

/**
 * 传统的复制方案，使用 document.execCommand
 * @param text 要复制的文本
 * @param onSuccess 成功回调
 * @param onError 失败回调
 * @returns boolean 复制是否成功
 */
const fallbackCopyTextToClipboard = (
  text: string,
  onSuccess?: () => void,
  onError?: (error: Error) => void
): boolean => {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  textArea.style.position = "fixed";
  textArea.style.left = "-999999px";
  textArea.style.top = "-999999px";
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();

  try {
    const successful = document.execCommand("copy");
    if (successful) {
      onSuccess?.();
      return true;
    } else {
      onError?.(new Error("execCommand failed"));
      return false;
    }
  } catch (err) {
    onError?.(err as Error);
    return false;
  } finally {
    document.body.removeChild(textArea);
  }
};
