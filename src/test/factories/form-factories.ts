export const createFormItem = (overrides = {}) => ({
  itemId: "test-item-1",
  compType: "input",
  compName: "测试输入框",
  formData: {
    formName: "测试字段",
    placeHolder: "请输入测试内容",
    isReq: "required",
  },
  ...overrides,
});

export const createFormTemplate = (overrides = {}) => ({
  formName: "测试表单模板",
  formItems: [
    createFormItem(),
    createFormItem({
      itemId: "test-item-2",
      compType: "selector",
      compName: "测试选择器",
      formData: {
        formName: "测试选择",
        placeHolder: "请选择",
        candidateList: [
          { id: 1, label: "选项1" },
          { id: 2, label: "选项2" },
        ],
      },
    }),
  ],
  ...overrides,
});

export const createMockApiResponse = (data: any, success = true) => ({
  code: success ? 200 : 500,
  message: success ? "success" : "error",
  data: success ? data : null,
});

// 创建特定类型的表单项
export const createInputFormItem = (overrides = {}) =>
  createFormItem({
    compType: "input",
    compName: "输入框",
    formData: {
      formName: "姓名",
      placeHolder: "请输入姓名",
      isReq: "required",
    },
    ...overrides,
  });

export const createSelectFormItem = (overrides = {}) =>
  createFormItem({
    compType: "selector",
    compName: "下拉选择",
    formData: {
      formName: "部门",
      placeHolder: "请选择部门",
      candidateList: [
        { id: 1, label: "技术部" },
        { id: 2, label: "人事部" },
      ],
    },
    ...overrides,
  });

export const createRadioFormItem = (overrides = {}) =>
  createFormItem({
    compType: "radio",
    compName: "单选",
    formData: {
      formName: "性别",
      candidateList: [
        { id: 1, label: "男" },
        { id: 2, label: "女" },
      ],
    },
    ...overrides,
  });

export const createCheckboxFormItem = (overrides = {}) =>
  createFormItem({
    compType: "checkbox",
    compName: "多选",
    formData: {
      formName: "技能",
      candidateList: [
        { option: "JavaScript" },
        { option: "React" },
        { option: "TypeScript" },
      ],
    },
    ...overrides,
  });

export const createDatePickerFormItem = (overrides = {}) =>
  createFormItem({
    compType: "datePicker",
    compName: "日期选择",
    formData: {
      formName: "开始日期",
      placeHolder: "请选择日期",
    },
    ...overrides,
  });

export const createEmployeePickerFormItem = (overrides = {}) =>
  createFormItem({
    compType: "employeePicker",
    compName: "人员选择器",
    formData: {
      formName: "负责人",
      placeHolder: "请选择负责人",
      serviceRange: [2, 3],
    },
    ...overrides,
  });

export const createTableFormItem = (overrides = {}) =>
  createFormItem({
    compType: "table",
    compName: "表格",
    formData: {
      formName: "人员列表",
    },
    children: [
      createFormItem({
        itemId: "table-name-1",
        compType: "input",
        compName: "姓名",
        formData: { formName: "姓名" },
      }),
      createFormItem({
        itemId: "table-role-1",
        compType: "selector",
        compName: "角色",
        formData: { formName: "角色" },
      }),
    ],
    ...overrides,
  });
