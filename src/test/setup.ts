import "@testing-library/jest-dom";
import { vi } from "vitest";

// Mock window.matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock Canvas
const mockCanvas = {
  getContext: vi.fn(() => ({
    fillStyle: "",
    fillRect: vi.fn(),
    clearRect: vi.fn(),
    getImageData: vi.fn(() => ({ data: new Array(4) })),
    putImageData: vi.fn(),
    createImageData: vi.fn(() => ({ data: new Array(4) })),
    setTransform: vi.fn(),
    drawImage: vi.fn(),
    save: vi.fn(),
    fillText: vi.fn(),
    restore: vi.fn(),
    beginPath: vi.fn(),
    moveTo: vi.fn(),
    lineTo: vi.fn(),
    closePath: vi.fn(),
    stroke: vi.fn(),
    translate: vi.fn(),
    scale: vi.fn(),
    rotate: vi.fn(),
    arc: vi.fn(),
    fill: vi.fn(),
    measureText: vi.fn(() => ({ width: 0 })),
    transform: vi.fn(),
    rect: vi.fn(),
    clip: vi.fn(),
  })),
  toDataURL: vi.fn(() => "data:image/png;base64,test"),
  width: 100,
  height: 100,
};

Object.defineProperty(window.HTMLCanvasElement.prototype, "getContext", {
  value: mockCanvas.getContext,
});

Object.defineProperty(window.HTMLCanvasElement.prototype, "toDataURL", {
  value: mockCanvas.toDataURL,
});

// Mock console methods in tests
global.console = {
  ...console,
  error: vi.fn(),
  warn: vi.fn(),
};

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock jotai/utils
vi.mock("jotai/utils", async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useResetAtom: vi.fn(() => vi.fn()),
    atomWithReset: vi.fn((initialValue) => ({
      init: initialValue,
      read: vi.fn(),
      write: vi.fn(),
    })),
    RESET: Symbol("RESET"),
  };
});

// Mock components module
vi.mock("components", () => ({
  SAFETY_ANALYSIS_JOBSTEP: [
    {
      id: 1,
      name: "作业前",
      color: "green",
    },
    {
      id: 2,
      name: "作业中",
      color: "red",
    },
    {
      id: 3,
      name: "作业后",
      color: "red",
    },
    {
      id: 4,
      name: "其他",
      color: "red",
    },
  ],
  MockComponent: vi.fn(({ children, ...props }: any) => {
    const React = require("react");
    return React.createElement(
      "div",
      { "data-testid": "mock-component", ...props },
      children
    );
  }),
  DetailComponent: vi.fn(({ children, ...props }: any) => {
    const React = require("react");
    return React.createElement(
      "div",
      { "data-testid": "detail-component", ...props },
      children
    );
  }),
  // 添加其他常用的组件Mock
  JobCategorySelect: vi.fn(({ children, ...props }: any) => {
    const React = require("react");
    return React.createElement(
      "select",
      { "data-testid": "job-category-select", ...props },
      children
    );
  }),
  RestSelect: vi.fn(({ children, ...props }: any) => {
    const React = require("react");
    return React.createElement(
      "select",
      { "data-testid": "rest-select", ...props },
      children
    );
  }),
  EmployeePicker: vi.fn(({ children, ...props }: any) => {
    const React = require("react");
    return React.createElement(
      "div",
      { "data-testid": "employee-picker", ...props },
      children
    );
  }),
}));
