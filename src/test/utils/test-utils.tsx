import { Form } from "@douyinfe/semi-ui";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, RenderOptions } from "@testing-library/react";
import React, { ReactElement } from "react";

// 创建测试用的 QueryClient
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

// 自定义渲染器，包含必要的 Provider
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
  queryClient?: QueryClient;
  withForm?: boolean;
}

const customRender = (ui: ReactElement, options: CustomRenderOptions = {}) => {
  const {
    queryClient = createTestQueryClient(),
    withForm = false,
    ...renderOptions
  } = options;

  const Wrapper = ({ children }: { children: React.ReactNode }) => {
    let content = children;

    if (withForm) {
      content = <Form>{children}</Form>;
    }

    return (
      <QueryClientProvider client={queryClient}>{content}</QueryClientProvider>
    );
  };

  return render(ui, { wrapper: Wrapper, ...renderOptions });
};

// 重新导出所有测试工具
export * from "@testing-library/react";
export { customRender as render };
