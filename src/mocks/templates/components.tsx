// Mock for components module
import React from 'react';

// Mock SAFETY_ANALYSIS_JOBSTEP enum
export const SAFETY_ANALYSIS_JOBSTEP = {
  STEP_1: 'step_1',
  STEP_2: 'step_2', 
  STEP_3: 'step_3',
  STEP_4: 'step_4',
  STEP_5: 'step_5',
} as const;

// Mock other common components exports
export const MockComponent = ({ children, ...props }: any) => (
  <div data-testid="mock-component" {...props}>
    {children}
  </div>
);

// Mock detail components
export const DetailComponent = ({ children, ...props }: any) => (
  <div data-testid="detail-component" {...props}>
    {children}
  </div>
);

// Mock render components
export const RenderComponent = ({ children, ...props }: any) => (
  <div data-testid="render-component" {...props}>
    {children}
  </div>
);

// Mock side components
export const SideComponent = ({ children, ...props }: any) => (
  <div data-testid="side-component" {...props}>
    {children}
  </div>
);

// Mock tab components
export const TabComponent = ({ children, ...props }: any) => (
  <div data-testid="tab-component" {...props}>
    {children}
  </div>
);

// Default export
export default {
  SAFETY_ANALYSIS_JOBSTEP,
  MockComponent,
  DetailComponent,
  RenderComponent,
  SideComponent,
  TabComponent,
};
