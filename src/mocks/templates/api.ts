/**
 * API Mock 模板库
 * 提供标准化的 API Mock 配置，支持 MSW、fetch、axios 等
 *
 * 使用方式:
 * import { createApiMock, createMSWHandlers } from '@/mocks/templates/api';
 */

import { vi } from "vitest";

// 基础 API 响应类型
export interface ApiResponse<T = any> {
  code: number;
  message?: string;
  data?: T;
  success?: boolean;
}

// 分页响应类型
export interface PaginatedResponse<T = any> {
  code: number;
  data: {
    results: T[];
    total: number;
    page: number;
    pageSize: number;
    hasMore: boolean;
  };
}

// 创建标准 API 响应
export const createApiResponse = <T>(
  data: T,
  code = 200,
  message = "success"
): ApiResponse<T> => ({
  code,
  message,
  data,
  success: code === 200,
});

// 创建分页响应
export const createPaginatedResponse = <T>(
  items: T[],
  page = 1,
  pageSize = 10,
  total?: number
): PaginatedResponse<T> => ({
  code: 200,
  data: {
    results: items.slice((page - 1) * pageSize, page * pageSize),
    total: total || items.length,
    page,
    pageSize,
    hasMore: page * pageSize < (total || items.length),
  },
});

// Fetch Mock 模板
export const createFetchMock = (responses: Record<string, any> = {}) => {
  const mockFetch = vi.fn((url: string, options?: RequestInit) => {
    const method = options?.method || "GET";
    const key = `${method} ${url}`;

    const response = responses[key] ||
      responses[url] || { code: 404, message: "Not Found" };

    return Promise.resolve({
      ok: response.code === 200,
      status: response.code,
      json: () => Promise.resolve(response),
      text: () => Promise.resolve(JSON.stringify(response)),
      headers: new Headers(),
    });
  });

  global.fetch = mockFetch;
  return mockFetch;
};

// Axios Mock 模板
export const createAxiosMock = (responses: Record<string, any> = {}) => ({
  get: vi.fn((url: string) => {
    const response = responses[`GET ${url}`] || responses[url];
    return Promise.resolve({ data: response });
  }),

  post: vi.fn((url: string, data?: any) => {
    const response = responses[`POST ${url}`] || responses[url];
    return Promise.resolve({ data: response });
  }),

  put: vi.fn((url: string, data?: any) => {
    const response = responses[`PUT ${url}`] || responses[url];
    return Promise.resolve({ data: response });
  }),

  delete: vi.fn((url: string) => {
    const response = responses[`DELETE ${url}`] || responses[url];
    return Promise.resolve({ data: response });
  }),

  patch: vi.fn((url: string, data?: any) => {
    const response = responses[`PATCH ${url}`] || responses[url];
    return Promise.resolve({ data: response });
  }),

  create: vi.fn(() => createAxiosMock(responses)),

  defaults: {
    baseURL: "",
    headers: {},
  },

  interceptors: {
    request: { use: vi.fn(), eject: vi.fn() },
    response: { use: vi.fn(), eject: vi.fn() },
  },
});

// MSW Handlers 创建器 (需要安装 msw)
export const createMSWHandlers = (baseURL = "/api") => {
  // 注意：这里需要实际的 MSW 库，这里提供模板结构
  const handlers = [
    // GET 请求示例
    // rest.get(`${baseURL}/users`, (req, res, ctx) => {
    //   return res(ctx.json(createApiResponse([
    //     { id: '1', name: '张三' },
    //     { id: '2', name: '李四' }
    //   ])));
    // }),
    // POST 请求示例
    // rest.post(`${baseURL}/users`, (req, res, ctx) => {
    //   return res(ctx.json(createApiResponse({ id: '3', name: '新用户' })));
    // }),
  ];

  return handlers;
};

// 项目特定的 API Mock
export const createProjectApiMock = () => ({
  // 用户相关 API
  getUserList: vi.fn(() =>
    Promise.resolve(
      createApiResponse([
        { id: "1", name: "张三", email: "<EMAIL>" },
        { id: "2", name: "李四", email: "<EMAIL>" },
      ])
    )
  ),

  getUserById: vi.fn((id: string) =>
    Promise.resolve(
      createApiResponse({
        id,
        name: `用户${id}`,
        email: `user${id}@example.com`,
      })
    )
  ),

  createUser: vi.fn((userData: any) =>
    Promise.resolve(
      createApiResponse({
        id: Math.random().toString(36).substr(2, 9),
        ...userData,
      })
    )
  ),

  updateUser: vi.fn((id: string, userData: any) =>
    Promise.resolve(
      createApiResponse({
        id,
        ...userData,
      })
    )
  ),

  deleteUser: vi.fn((id: string) => Promise.resolve(createApiResponse(null))),

  // 承包商相关 API
  getContractorList: vi.fn(() =>
    Promise.resolve(
      createPaginatedResponse([
        { id: "1", name: "承包商A", status: 1 },
        { id: "2", name: "承包商B", status: 2 },
      ])
    )
  ),

  getContractor: vi.fn((id: string) =>
    Promise.resolve(
      createApiResponse({
        id,
        name: `承包商${id}`,
        status: 1,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
    )
  ),

  // 培训相关 API
  getTrainingList: vi.fn(() =>
    Promise.resolve(
      createApiResponse([
        { id: "1", title: "安全培训", status: "active" },
        { id: "2", title: "技能培训", status: "pending" },
      ])
    )
  ),

  joinTraining: vi.fn((trainingId: string) =>
    Promise.resolve(
      createApiResponse({
        trainingId,
        joinedAt: new Date().toISOString(),
      })
    )
  ),

  // 作业票相关 API
  getTicketList: vi.fn(() =>
    Promise.resolve(
      createPaginatedResponse([
        { id: "1", title: "高空作业票", status: "pending" },
        { id: "2", title: "动火作业票", status: "approved" },
      ])
    )
  ),

  createTicket: vi.fn((ticketData: any) =>
    Promise.resolve(
      createApiResponse({
        id: Math.random().toString(36).substr(2, 9),
        ...ticketData,
        createdAt: new Date().toISOString(),
      })
    )
  ),

  // 风险措施相关 API
  getRiskMeasureList: vi.fn(() =>
    Promise.resolve(
      createApiResponse({
        results: [
          { id: "risk-1", name: "风险措施1", accidentType: "type-1" },
          { id: "risk-2", name: "风险措施2", accidentType: "type-2" },
        ],
      })
    )
  ),

  // 新增缺失的API函数
  getSensorListFromEquipment: vi.fn(() =>
    Promise.resolve(
      createApiResponse({
        results: [
          {
            id: "sensor-1",
            name: "温度传感器",
            type: "temperature",
            status: "normal",
          },
          {
            id: "sensor-2",
            name: "压力传感器",
            type: "pressure",
            status: "alarm",
          },
        ],
      })
    )
  ),

  updateFormTemplate: vi.fn(() =>
    Promise.resolve(
      createApiResponse({
        id: "template-1",
        message: "模板更新成功",
      })
    )
  ),

  getCurrentEmployee: vi.fn(() =>
    Promise.resolve(
      createApiResponse({
        id: "emp-1",
        name: "张三",
        department: "技术部",
        position: "工程师",
      })
    )
  ),

  getCurrentVisitor: vi.fn(() =>
    Promise.resolve(
      createApiResponse({
        id: "visitor-1",
        name: "李四",
        company: "外部公司",
        purpose: "参观",
      })
    )
  ),

  getCurrentContractorEmployee: vi.fn(() =>
    Promise.resolve(
      createApiResponse({
        id: "contractor-emp-1",
        name: "王五",
        company: "承包商公司",
        position: "技术员",
      })
    )
  ),

  getCurrentCar: vi.fn(() =>
    Promise.resolve(
      createApiResponse({
        id: "car-1",
        plateNumber: "京A12345",
        model: "轿车",
        driver: "司机姓名",
      })
    )
  ),
});

// API 错误模拟
export const createApiErrorMock = () => ({
  networkError: () => Promise.reject(new Error("Network Error")),

  serverError: () =>
    Promise.resolve(createApiResponse(null, 500, "Internal Server Error")),

  unauthorizedError: () =>
    Promise.resolve(createApiResponse(null, 401, "Unauthorized")),

  forbiddenError: () =>
    Promise.resolve(createApiResponse(null, 403, "Forbidden")),

  notFoundError: () =>
    Promise.resolve(createApiResponse(null, 404, "Not Found")),

  validationError: (errors: Record<string, string>) =>
    Promise.resolve(createApiResponse(errors, 422, "Validation Error")),
});

// API Mock 工具函数
export const createApiMockUtils = () => ({
  // 延迟响应
  delay: (ms: number) => new Promise((resolve) => setTimeout(resolve, ms)),

  // 随机失败
  randomFail: (successRate = 0.8) => Math.random() < successRate,

  // 创建分页数据
  paginate: <T>(items: T[], page: number, pageSize: number) => ({
    results: items.slice((page - 1) * pageSize, page * pageSize),
    total: items.length,
    page,
    pageSize,
    hasMore: page * pageSize < items.length,
  }),

  // 模拟搜索
  search: <T>(items: T[], query: string, fields: (keyof T)[]) => {
    if (!query) return items;

    return items.filter((item) =>
      fields.some((field) =>
        String(item[field]).toLowerCase().includes(query.toLowerCase())
      )
    );
  },

  // 模拟排序
  sort: <T>(items: T[], field: keyof T, order: "asc" | "desc" = "asc") => {
    return [...items].sort((a, b) => {
      const aVal = a[field];
      const bVal = b[field];

      if (aVal < bVal) return order === "asc" ? -1 : 1;
      if (aVal > bVal) return order === "asc" ? 1 : -1;
      return 0;
    });
  },
});

// 完整的 API Mock 套件
export const createApiMockSuite = (config: {
  baseURL?: string;
  responses?: Record<string, any>;
  enableErrors?: boolean;
  enableDelay?: boolean;
  delayMs?: number;
}) => {
  const {
    baseURL = "/api",
    responses = {},
    enableErrors = false,
    enableDelay = false,
    delayMs = 100,
  } = config;

  const utils = createApiMockUtils();
  const projectApi = createProjectApiMock();
  const errorApi = createApiErrorMock();

  // 包装 API 函数以添加延迟和错误处理
  const wrapApiFunction = (fn: Function) => {
    return async (...args: any[]) => {
      if (enableDelay) {
        await utils.delay(delayMs);
      }

      if (enableErrors && !utils.randomFail()) {
        throw new Error("Simulated API Error");
      }

      return fn(...args);
    };
  };

  // 包装所有 API 函数
  const wrappedApi = Object.keys(projectApi).reduce((acc, key) => {
    acc[key] = wrapApiFunction(projectApi[key]);
    return acc;
  }, {} as any);

  return {
    api: wrappedApi,
    errors: errorApi,
    utils,
    fetch: createFetchMock(responses),
    axios: createAxiosMock(responses),
  };
};
