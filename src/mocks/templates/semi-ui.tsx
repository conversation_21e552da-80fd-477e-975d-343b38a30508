/**
 * Semi UI 组件 Mock 模板库
 * 提供标准化的 Semi UI 组件 Mock 配置
 *
 * 使用方式:
 * import { createSemiUIMock } from '@/mocks/templates/semi-ui';
 *
 * vi.mock('@douyinfe/semi-ui', createSemiUIMock);
 */

import { vi } from "vitest";

// 基础表单组件 Mock
export const createFormMocks = () => ({
  Form: {
    Input: ({ field, onClick, onFocus, onChange, ...props }: any) => (
      <input
        data-testid={`form-input${field ? `-${field}` : ""}`}
        data-field={field}
        onClick={onClick}
        onFocus={onFocus}
        onChange={onChange}
        {...props}
      />
    ),

    Select: ({ field, children, onChange, ...props }: any) => (
      <select
        data-testid={`form-select${field ? `-${field}` : ""}`}
        data-field={field}
        onChange={onChange}
        {...props}
      >
        {children}
      </select>
    ),

    TextArea: ({ field, onChange, ...props }: any) => (
      <textarea
        data-testid={`form-textarea${field ? `-${field}` : ""}`}
        data-field={field}
        onChange={onChange}
        {...props}
      />
    ),

    TagInput: ({ field, onRemove, onFocus, onChange, ...props }: any) => (
      <div
        data-testid={`form-tag-input${field ? `-${field}` : ""}`}
        data-field={field}
      >
        <input onFocus={onFocus} onChange={onChange} />
        <button
          data-testid="tag-remove-button"
          onClick={() => onRemove && onRemove("test-value", 0)}
        >
          Remove
        </button>
      </div>
    ),

    DatePicker: ({ field, onChange, ...props }: any) => (
      <input
        type="date"
        data-testid={`form-datepicker${field ? `-${field}` : ""}`}
        data-field={field}
        onChange={onChange}
        {...props}
      />
    ),

    TimePicker: ({ field, onChange, ...props }: any) => (
      <input
        type="time"
        data-testid={`form-timepicker${field ? `-${field}` : ""}`}
        data-field={field}
        onChange={onChange}
        {...props}
      />
    ),

    Switch: ({ field, onChange, ...props }: any) => (
      <input
        type="checkbox"
        data-testid={`form-switch${field ? `-${field}` : ""}`}
        data-field={field}
        onChange={onChange}
        {...props}
      />
    ),

    RadioGroup: ({ field, onChange, children, ...props }: any) => (
      <div
        data-testid={`form-radio-group${field ? `-${field}` : ""}`}
        data-field={field}
      >
        {children}
      </div>
    ),

    CheckboxGroup: ({ field, onChange, children, ...props }: any) => (
      <div
        data-testid={`form-checkbox-group${field ? `-${field}` : ""}`}
        data-field={field}
      >
        {children}
      </div>
    ),
  },
});

// 反馈组件 Mock
export const createFeedbackMocks = () => ({
  Toast: {
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
    open: vi.fn(),
    close: vi.fn(),
    destroyAll: vi.fn(),
  },

  Modal: {
    confirm: vi.fn(),
    info: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    destroyAll: vi.fn(),
  },

  Notification: {
    open: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
    info: vi.fn(),
    warning: vi.fn(),
    close: vi.fn(),
    destroyAll: vi.fn(),
  },
});

// 导航组件 Mock
export const createNavigationMocks = () => ({
  Breadcrumb: ({ children, ...props }: any) => (
    <nav data-testid="breadcrumb" {...props}>
      {children}
    </nav>
  ),

  Menu: ({ children, onClick, ...props }: any) => (
    <ul data-testid="menu" onClick={onClick} {...props}>
      {children}
    </ul>
  ),

  Pagination: ({ onChange, current, total, ...props }: any) => (
    <div data-testid="pagination" data-current={current} data-total={total}>
      <button onClick={() => onChange && onChange(current - 1)}>
        Previous
      </button>
      <span>{current}</span>
      <button onClick={() => onChange && onChange(current + 1)}>Next</button>
    </div>
  ),

  Steps: ({ children, current, ...props }: any) => (
    <div data-testid="steps" data-current={current} {...props}>
      {children}
    </div>
  ),

  Tabs: Object.assign(
    ({ children, activeKey, onChange, ...props }: any) => (
      <div data-testid="tabs" data-active-key={activeKey} {...props}>
        {children}
      </div>
    ),
    {
      TabPane: ({ children, tab, key, ...props }: any) => (
        <div data-testid="tab-pane" data-tab={tab} data-key={key} {...props}>
          {children}
        </div>
      ),
    }
  ),
});

// 数据展示组件 Mock
export const createDataDisplayMocks = () => ({
  Table: ({ columns, dataSource, onRow, ...props }: any) => (
    <table data-testid="table" {...props}>
      <thead>
        <tr>
          {columns?.map((col: any, index: number) => (
            <th key={index}>{col.title}</th>
          ))}
        </tr>
      </thead>
      <tbody>
        {dataSource?.map((row: any, index: number) => (
          <tr key={index} onClick={() => onRow && onRow(row)}>
            {columns?.map((col: any, colIndex: number) => (
              <td key={colIndex}>{row[col.dataIndex]}</td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  ),

  List: ({ dataSource, renderItem, ...props }: any) => (
    <ul data-testid="list" {...props}>
      {dataSource?.map((item: any, index: number) => (
        <li key={index}>{renderItem ? renderItem(item, index) : item}</li>
      ))}
    </ul>
  ),

  Card: ({ title, children, extra, ...props }: any) => (
    <div data-testid="card" {...props}>
      {title && <div data-testid="card-title">{title}</div>}
      {extra && <div data-testid="card-extra">{extra}</div>}
      <div data-testid="card-body">{children}</div>
    </div>
  ),

  Descriptions: ({ data, ...props }: any) => (
    <dl data-testid="descriptions" {...props}>
      {data?.map((item: any, index: number) => (
        <div key={index}>
          <dt>{item.key}</dt>
          <dd>{item.value}</dd>
        </div>
      ))}
    </dl>
  ),
});

// 通用组件 Mock
export const createGeneralMocks = () => ({
  Button: ({ children, onClick, loading, disabled, ...props }: any) => (
    <button
      data-testid="button"
      onClick={onClick}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? "Loading..." : children}
    </button>
  ),

  Badge: ({ children, count, position, type, ...props }: any) => (
    <div
      data-testid="badge"
      data-count={count}
      data-position={position}
      data-type={type}
      {...props}
    >
      {children}
      {count && <span data-testid="badge-count">{count}</span>}
    </div>
  ),

  Icon: ({ type, ...props }: any) => (
    <span data-testid="icon" data-type={type} {...props} />
  ),

  Tooltip: ({ content, children, ...props }: any) => (
    <div data-testid="tooltip" title={content} {...props}>
      {children}
    </div>
  ),

  Popover: ({ content, children, ...props }: any) => (
    <div data-testid="popover" {...props}>
      {children}
      <div data-testid="popover-content">{content}</div>
    </div>
  ),

  Dropdown: ({ menu, children, ...props }: any) => (
    <div data-testid="dropdown" {...props}>
      {children}
    </div>
  ),

  Spin: ({ spinning, children, ...props }: any) => (
    <div data-testid="spin" data-spinning={spinning} {...props}>
      {spinning && <div data-testid="spin-indicator">Loading...</div>}
      {children}
    </div>
  ),

  Progress: ({ percent, ...props }: any) => (
    <div data-testid="progress" data-percent={percent} {...props}>
      <div style={{ width: `${percent}%` }}></div>
    </div>
  ),

  Row: ({ children, gutter, ...props }: any) => (
    <div data-testid="row" data-gutter={gutter} {...props}>
      {children}
    </div>
  ),

  Col: ({ children, span, ...props }: any) => (
    <div data-testid="col" data-span={span} {...props}>
      {children}
    </div>
  ),

  Tag: ({ children, color, type, ...props }: any) => (
    <span data-testid="tag" data-color={color} data-type={type} {...props}>
      {children}
    </span>
  ),

  Typography: Object.assign(
    ({ children, ...props }: any) => (
      <div data-testid="typography" {...props}>
        {children}
      </div>
    ),
    {
      Title: ({ children, level, ...props }: any) => {
        const Tag = `h${level || 1}` as keyof JSX.IntrinsicElements;
        return (
          <Tag data-testid="typography-title" data-level={level} {...props}>
            {children}
          </Tag>
        );
      },
      Text: ({ children, ...props }: any) => (
        <span data-testid="typography-text" {...props}>
          {children}
        </span>
      ),
      Paragraph: ({ children, ...props }: any) => (
        <p data-testid="typography-paragraph" {...props}>
          {children}
        </p>
      ),
    }
  ),

  SideSheet: ({ children, visible, onCancel, title, ...props }: any) => (
    <div
      data-testid="side-sheet"
      data-visible={visible}
      style={{ display: visible ? "block" : "none" }}
      {...props}
    >
      <div data-testid="side-sheet-header">
        <h3>{title}</h3>
        <button onClick={onCancel}>×</button>
      </div>
      <div data-testid="side-sheet-content">{children}</div>
    </div>
  ),
});

// Form Hooks Mock
export const createFormHooksMocks = () => ({
  useFormState: vi.fn(() => ({
    values: { form: { level: "1", isUpgrade: 1 } },
    errors: {},
    touched: {},
  })),

  useFormApi: vi.fn(() => ({
    getValue: vi.fn((field) => {
      if (field === "form.level") return "1";
      if (field === "form.isUpgrade") return 1;
      return "";
    }),
    setValue: vi.fn(),
    setError: vi.fn(),
    scrollToField: vi.fn(),
    getValues: vi.fn(() => ({ form: { level: "1", isUpgrade: 1 } })),
    validate: vi.fn(() => Promise.resolve({})),
    reset: vi.fn(),
    submit: vi.fn(),
  })),
});

// 完整的 Semi UI Mock
export const createSemiUIMock = async (importOriginal?: () => Promise<any>) => {
  const actual = importOriginal ? await importOriginal() : {};

  return {
    ...actual,
    ...createFormMocks(),
    ...createFeedbackMocks(),
    ...createNavigationMocks(),
    ...createDataDisplayMocks(),
    ...createGeneralMocks(),
    ...createFormHooksMocks(),
  };
};

// 轻量级 Mock (只包含常用组件)
export const createLightSemiUIMock = () => ({
  ...createFormMocks(),
  ...createFeedbackMocks(),
  Button: ({ children, onClick, loading, disabled, ...props }: any) => (
    <button
      data-testid="button"
      onClick={onClick}
      disabled={disabled || loading}
      {...props}
    >
      {loading ? "Loading..." : children}
    </button>
  ),
  ...createFormHooksMocks(),
});

// 自定义 Mock 构建器
export const createCustomSemiUIMock = (components: string[]) => {
  const allMocks = {
    ...createFormMocks(),
    ...createFeedbackMocks(),
    ...createNavigationMocks(),
    ...createDataDisplayMocks(),
    ...createGeneralMocks(),
    ...createFormHooksMocks(),
  };

  const customMock: any = {};

  components.forEach((component) => {
    if (allMocks[component]) {
      customMock[component] = allMocks[component];
    }
  });

  return customMock;
};

// 额外的组件Mock（用于特定测试场景）
export const JsonExportModal = ({
  onSave = () => {},
  children,
  ...props
}: any) => (
  <div data-testid="json-export-modal" {...props}>
    <div>JSON Export Modal</div>
    {children}
    <button onClick={onSave}>Save</button>
  </div>
);

// Dispose组件相关Mock
export const Dispose = ({
  onClose = () => {},
  data,
  visible,
  onChangeCb = () => {},
  ...props
}: any) => (
  <div
    data-testid="dispose-component"
    style={{ display: visible ? "block" : "none" }}
    {...props}
  >
    <div>Dispose Component</div>
    <div>Data: {JSON.stringify(data)}</div>
    <button onClick={onClose}>Close</button>
    <button onClick={onChangeCb}>Change</button>
  </div>
);

export const RenderDisposeFormUsingFormApi = ({
  disposeData,
  ...props
}: any) => (
  <div data-testid="render-dispose-form" {...props}>
    <div>Dispose Form for {disposeData?.compType || "unknown"}</div>
    <div>Component Name: {disposeData?.compName || "N/A"}</div>
    <div>Item ID: {disposeData?.itemId || "N/A"}</div>
  </div>
);

export const ComponentUsingFormApi = ({ data, disposeData, ...props }: any) => (
  <div data-testid="component-using-form-api" {...props}>
    <div>Component Using Form API</div>
    <div>Dispose Type: {disposeData?.compType || "unknown"}</div>
  </div>
);

// 原子状态Mock
export const disposeFormAtom = {
  init: { visible: false, data: null },
};

// 枚举常量Mock
export const SAFETY_ANALYSIS_JOBSTEP = [
  {
    id: 1,
    name: "作业前",
    color: "green",
  },
  {
    id: 2,
    name: "作业中",
    color: "red",
  },
  {
    id: 3,
    name: "作业后",
    color: "red",
  },
  {
    id: 4,
    name: "其他",
    color: "red",
  },
];
