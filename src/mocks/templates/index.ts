/**
 * Mock 模板库统一导出
 * 提供所有 Mock 模板的便捷访问入口
 *
 * 使用方式:
 * import { SemiUI, StateManagement, API } from '@/mocks/templates';
 */

// Semi UI Mock 模板
export * as SemiUI from "./semi-ui";

// 状态管理 Mock 模板
export * as StateManagement from "./state-management";

// API Mock 模板
export * as API from "./api";

// 常用 Mock 组合
export const CommonMocks = {
  // 基础测试环境
  basic: () => ({
    semiUI: createLightSemiUIMock(),
    jotai: createJotaiMock(),
    api: createProjectApiMock(),
  }),

  // 完整测试环境
  full: () => ({
    semiUI: createSemiUIMock(),
    jotai: createJotaiMock(),
    reactQuery: createReactQueryMock(),
    api: createApiMockSuite({ enableDelay: false }),
  }),

  // 性能测试环境
  performance: () => ({
    semiUI: createLightSemiUIMock(),
    jotai: createJotaiMock(),
    api: createApiMockSuite({ enableDelay: true, delayMs: 50 }),
  }),

  // 错误测试环境
  error: () => ({
    semiUI: createSemiUIMock(),
    jotai: createJotaiMock(),
    api: createApiMockSuite({ enableErrors: true }),
  }),
};

// 快速设置函数
export const setupMocks = {
  // 设置基础 Mock
  basic: () => {
    const mocks = CommonMocks.basic();
    vi.mock("@douyinfe/semi-ui", () => mocks.semiUI);
    vi.mock("jotai", () => mocks.jotai);
    vi.mock("api", () => mocks.api);
  },

  // 设置完整 Mock
  full: () => {
    const mocks = CommonMocks.full();
    vi.mock("@douyinfe/semi-ui", () => mocks.semiUI);
    vi.mock("jotai", () => mocks.jotai);
    vi.mock("@tanstack/react-query", () => mocks.reactQuery);
    vi.mock("api", () => mocks.api.api);
  },

  // 设置自定义 Mock
  custom: (config: {
    semiUI?: any;
    jotai?: any;
    reactQuery?: any;
    api?: any;
  }) => {
    if (config.semiUI) {
      vi.mock("@douyinfe/semi-ui", () => config.semiUI);
    }
    if (config.jotai) {
      vi.mock("jotai", () => config.jotai);
    }
    if (config.reactQuery) {
      vi.mock("@tanstack/react-query", () => config.reactQuery);
    }
    if (config.api) {
      vi.mock("api", () => config.api);
    }
  },
};

// 类型定义
export interface MockConfig {
  semiUI?: "light" | "full" | "custom";
  stateManagement?: "jotai" | "redux" | "zustand";
  api?: "basic" | "full" | "error";
  customComponents?: string[];
  initialState?: Record<string, any>;
  apiResponses?: Record<string, any>;
}

// 智能 Mock 配置器
export const configureMocks = (config: MockConfig) => {
  const mocks: any = {};

  // 配置 Semi UI Mock
  switch (config.semiUI) {
    case "light":
      mocks.semiUI = createLightSemiUIMock();
      break;
    case "full":
      mocks.semiUI = createSemiUIMock();
      break;
    case "custom":
      mocks.semiUI = createCustomSemiUIMock(config.customComponents || []);
      break;
    default:
      mocks.semiUI = createLightSemiUIMock();
  }

  // 配置状态管理 Mock
  switch (config.stateManagement) {
    case "jotai":
      mocks.jotai = createJotaiMock(config.initialState);
      break;
    case "redux":
      mocks.redux = createReduxMock(config.initialState);
      break;
    case "zustand":
      mocks.zustand = createZustandMock(config.initialState);
      break;
    default:
      mocks.jotai = createJotaiMock(config.initialState);
  }

  // 配置 API Mock
  switch (config.api) {
    case "basic":
      mocks.api = createProjectApiMock();
      break;
    case "full":
      mocks.api = createApiMockSuite({
        responses: config.apiResponses,
        enableDelay: false,
      });
      break;
    case "error":
      mocks.api = createApiMockSuite({
        enableErrors: true,
        responses: config.apiResponses,
      });
      break;
    default:
      mocks.api = createProjectApiMock();
  }

  return mocks;
};

// 测试工具函数
export const TestUtils = {
  // 等待异步操作
  waitForAsync: (ms = 0) => new Promise((resolve) => setTimeout(resolve, ms)),

  // 模拟用户输入
  simulateUserInput: async (element: HTMLElement, value: string) => {
    fireEvent.focus(element);
    fireEvent.change(element, { target: { value } });
    fireEvent.blur(element);
  },

  // 模拟表单提交
  simulateFormSubmit: async (form: HTMLElement) => {
    fireEvent.submit(form);
    await TestUtils.waitForAsync(100);
  },

  // 验证 Mock 调用
  expectMockCalled: (mockFn: any, times = 1) => {
    expect(mockFn).toHaveBeenCalledTimes(times);
  },

  // 重置所有 Mock
  resetAllMocks: () => {
    vi.clearAllMocks();
  },

  // 恢复所有 Mock
  restoreAllMocks: () => {
    vi.restoreAllMocks();
  },
};

// 默认导出
export default {
  CommonMocks,
  setupMocks,
  configureMocks,
  TestUtils,
};
