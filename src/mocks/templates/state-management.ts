/**
 * 状态管理 Mock 模板库
 * 支持 Jotai、Redux、Zustand 等主流状态管理库
 * 
 * 使用方式:
 * import { createJotaiMock, createReduxMock } from '@/mocks/templates/state-management';
 */

import { vi } from 'vitest';

// Jotai Mock 模板
export const createJotaiMock = (initialValues: Record<string, any> = {}) => ({
  useAtom: vi.fn((atom) => {
    const atomKey = atom?.toString() || 'default';
    const value = initialValues[atomKey] || atom?.init || null;
    const setter = vi.fn();
    return [value, setter];
  }),
  
  useAtomValue: vi.fn((atom) => {
    const atomKey = atom?.toString() || 'default';
    return initialValues[atomKey] || atom?.init || null;
  }),
  
  useSetAtom: vi.fn(() => vi.fn()),
  
  atom: vi.fn((initialValue) => ({
    init: initialValue,
    toString: () => `atom_${Math.random().toString(36).substr(2, 9)}`,
  })),
  
  atomWithStorage: vi.fn((key, initialValue) => ({
    init: initialValue,
    key,
    toString: () => `storage_atom_${key}`,
  })),
  
  atomWithReset: vi.fn((initialValue) => ({
    init: initialValue,
    toString: () => `reset_atom_${Math.random().toString(36).substr(2, 9)}`,
  })),
  
  useResetAtom: vi.fn(() => vi.fn()),
});

// 预定义的 Jotai 原子 Mock
export const createAtomMocks = (atoms: Record<string, any>) => {
  const mockAtoms: Record<string, any> = {};
  
  Object.entries(atoms).forEach(([name, initialValue]) => {
    mockAtoms[name] = {
      init: initialValue,
      toString: () => `atom_${name}`,
    };
  });
  
  return mockAtoms;
};

// Redux Mock 模板
export const createReduxMock = (initialState: any = {}) => ({
  useSelector: vi.fn((selector) => {
    if (typeof selector === 'function') {
      return selector(initialState);
    }
    return initialState;
  }),
  
  useDispatch: vi.fn(() => vi.fn()),
  
  useStore: vi.fn(() => ({
    getState: () => initialState,
    dispatch: vi.fn(),
    subscribe: vi.fn(),
  })),
  
  connect: vi.fn(() => (component: any) => component),
  
  Provider: ({ children }: any) => children,
});

// Redux Toolkit Mock 模板
export const createReduxToolkitMock = (initialState: any = {}) => ({
  ...createReduxMock(initialState),
  
  createSlice: vi.fn((config) => ({
    name: config.name,
    reducer: vi.fn(),
    actions: Object.keys(config.reducers || {}).reduce((acc, key) => {
      acc[key] = vi.fn();
      return acc;
    }, {} as any),
  })),
  
  configureStore: vi.fn(() => ({
    getState: () => initialState,
    dispatch: vi.fn(),
    subscribe: vi.fn(),
  })),
  
  createAsyncThunk: vi.fn((type, payloadCreator) => {
    const thunk = vi.fn();
    thunk.pending = { type: `${type}/pending` };
    thunk.fulfilled = { type: `${type}/fulfilled` };
    thunk.rejected = { type: `${type}/rejected` };
    return thunk;
  }),
});

// Zustand Mock 模板
export const createZustandMock = (initialState: any = {}) => ({
  create: vi.fn(() => () => initialState),
  
  subscribeWithSelector: vi.fn(),
  
  persist: vi.fn((config) => config),
  
  devtools: vi.fn((config) => config),
});

// React Query Mock 模板
export const createReactQueryMock = (defaultData: any = null) => ({
  useQuery: vi.fn(() => ({
    data: defaultData,
    isLoading: false,
    isError: false,
    error: null,
    refetch: vi.fn(),
    isFetching: false,
    isSuccess: true,
  })),
  
  useMutation: vi.fn(() => ({
    mutate: vi.fn(),
    mutateAsync: vi.fn(),
    isLoading: false,
    isError: false,
    error: null,
    data: null,
    reset: vi.fn(),
  })),
  
  useInfiniteQuery: vi.fn(() => ({
    data: { pages: [defaultData], pageParams: [undefined] },
    fetchNextPage: vi.fn(),
    hasNextPage: false,
    isFetchingNextPage: false,
    isLoading: false,
    isError: false,
    error: null,
  })),
  
  QueryClient: vi.fn(() => ({
    invalidateQueries: vi.fn(),
    setQueryData: vi.fn(),
    getQueryData: vi.fn(),
    removeQueries: vi.fn(),
    clear: vi.fn(),
  })),
  
  QueryClientProvider: ({ children }: any) => children,
  
  useQueryClient: vi.fn(() => ({
    invalidateQueries: vi.fn(),
    setQueryData: vi.fn(),
    getQueryData: vi.fn(),
    removeQueries: vi.fn(),
    clear: vi.fn(),
  })),
});

// SWR Mock 模板
export const createSWRMock = (defaultData: any = null) => ({
  useSWR: vi.fn(() => ({
    data: defaultData,
    error: null,
    isLoading: false,
    isValidating: false,
    mutate: vi.fn(),
  })),
  
  useSWRConfig: vi.fn(() => ({
    mutate: vi.fn(),
    cache: new Map(),
  })),
  
  SWRConfig: ({ children }: any) => children,
});

// 状态管理工具函数
export const createStateTestUtils = () => ({
  // 创建测试用的状态更新函数
  createMockSetter: (initialValue: any) => {
    let value = initialValue;
    const setter = vi.fn((newValue) => {
      value = typeof newValue === 'function' ? newValue(value) : newValue;
    });
    setter.getValue = () => value;
    return setter;
  },
  
  // 创建测试用的异步状态
  createAsyncState: (data: any, loading = false, error: any = null) => ({
    data,
    loading,
    error,
    refetch: vi.fn(),
    mutate: vi.fn(),
  }),
  
  // 创建分页状态
  createPaginationState: (data: any[], page = 1, pageSize = 10) => ({
    data: data.slice((page - 1) * pageSize, page * pageSize),
    total: data.length,
    page,
    pageSize,
    hasMore: page * pageSize < data.length,
    loadMore: vi.fn(),
    setPage: vi.fn(),
  }),
});

// 组合 Mock 创建器
export const createStateMockBundle = (config: {
  jotai?: Record<string, any>;
  redux?: any;
  reactQuery?: any;
  zustand?: any;
}) => {
  const mocks: any = {};
  
  if (config.jotai) {
    mocks.jotai = createJotaiMock(config.jotai);
  }
  
  if (config.redux) {
    mocks['react-redux'] = createReduxMock(config.redux);
  }
  
  if (config.reactQuery) {
    mocks['@tanstack/react-query'] = createReactQueryMock(config.reactQuery);
  }
  
  if (config.zustand) {
    mocks.zustand = createZustandMock(config.zustand);
  }
  
  return mocks;
};

// 常用状态模式 Mock
export const createCommonStateMocks = () => ({
  // 用户状态
  userState: createJotaiMock({
    currentUser: { id: '1', name: 'Test User', email: '<EMAIL>' },
    isAuthenticated: true,
  }),
  
  // UI 状态
  uiState: createJotaiMock({
    loading: false,
    sidebarOpen: false,
    theme: 'light',
    language: 'zh-CN',
  }),
  
  // 表单状态
  formState: createJotaiMock({
    isDirty: false,
    isSubmitting: false,
    errors: {},
    values: {},
  }),
  
  // 模态框状态
  modalState: createJotaiMock({
    visible: false,
    type: '',
    data: null,
  }),
  
  // 列表状态
  listState: createJotaiMock({
    items: [],
    loading: false,
    page: 1,
    total: 0,
    filters: {},
  }),
});

// 状态测试辅助函数
export const createStateTestHelpers = () => ({
  // 模拟状态变化
  simulateStateChange: (mockSetter: any, newValue: any) => {
    mockSetter(newValue);
    expect(mockSetter).toHaveBeenCalledWith(newValue);
  },
  
  // 验证状态更新
  expectStateUpdate: (mockSetter: any, expectedValue: any) => {
    expect(mockSetter).toHaveBeenCalledWith(expectedValue);
  },
  
  // 重置所有状态 Mock
  resetAllStateMocks: (mocks: Record<string, any>) => {
    Object.values(mocks).forEach(mock => {
      if (vi.isMockFunction(mock)) {
        mock.mockClear();
      }
    });
  },
});
