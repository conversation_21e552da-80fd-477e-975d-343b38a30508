import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  JOB_REPORT_STATUS_MAP,
  JOB_SLICE_STATUS,
  UNIT_CATEGORY_MAP,
  VideoModal,
} from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { formatDate } from "utils";

export const jobSliceConfigModalAtom = atom(false);

export const jobSliceInfoAtom = atomWithReset({
  id: "",
  show: false,
});

export const jobSliceEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const jobSliceFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
  departmentId: null,
});

// 查询条件
export const jobSliceFnAtom = atom({
  refetch: () => {},
});

const jobSliceColumns = [
  {
    title: "作业类型",
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text?.name}>
        <span className="block truncate">{text?.name}</span>
      </Tooltip>
    ),
    renderText: (text) => text?.name,
  },
  {
    title: "申请人",
    dataIndex: "applyPerson",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text?.name}>
        <span className="block truncate">{text?.name}</span>
      </Tooltip>
    ),
    renderText: (text) => text?.name,
  },
  {
    title: "申请部门",
    dataIndex: "applyDepartment",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text?.name}>
        <span className="block truncate">{text?.name}</span>
      </Tooltip>
    ),
    renderText: (text) => text?.name,
  },
  {
    title: "申请时间",
    dataIndex: "applyTime",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (text) => (
      <Tooltip content={dayjs(text).format("YYYY/MM/DD HH:mm")}>
        <p>{dayjs(text).format("YYYY/MM/DD HH:mm") ?? ""}</p>
      </Tooltip>
    ),
    renderText: (text) => formatDate(text),
  },
  {
    title: "作业单位类型",
    dataIndex: "unitCategory",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(UNIT_CATEGORY_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(UNIT_CATEGORY_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "关联监控",
    dataIndex: "monitor",
    isShow: true,
    ellipsis: true,
    width: 150,
    render: (item, record) => {
      return <VideoModal data={item} />;
    },
    // render: (text) => <Tooltip content={text?.name}><span className="block truncate">{text?.name}</span></Tooltip>,
    renderText: (text) => text?.name,
  },
  {
    title: "计划开始时间",
    dataIndex: "planBeginTime",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (text) => (
      <Tooltip content={dayjs(text).format("YYYY/MM/DD HH:mm")}>
        <p>{dayjs(text).format("YYYY/MM/DD HH:mm") ?? ""}</p>
      </Tooltip>
    ),
    renderText: (text) => formatDate(text),
  },
  {
    title: "计划结束时间",
    dataIndex: "planEndTime",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (text) => (
      <Tooltip content={dayjs(text).format("YYYY/MM/DD HH:mm")}>
        <p>{dayjs(text).format("YYYY/MM/DD HH:mm")}</p>
      </Tooltip>
    ),
    renderText: (text) => formatDate(text),
  },
  {
    title: "作业区域",
    dataIndex: "workArea",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text?.name}>
        <span className="block truncate">{text?.name}</span>
      </Tooltip>
    ),
    renderText: (text) => text?.name,
  },
  {
    title: "作业内容",
    dataIndex: "workContent",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "作业状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    width: 150,
    render: (text: string) => {
      const i = find(propEq(text, "id"))(JOB_SLICE_STATUS);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
    renderText: (text: string) => {
      const i = find(propEq(text, "id"))(JOB_SLICE_STATUS);
      return i?.name ?? "-";
    },
  },
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: false,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
    renderText: (text) => text,
  },
  {
    title: "作业上报批次",
    dataIndex: "reportBatchId",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
    renderText: (text) => text,
  },
  {
    title: "作业上报状态",
    dataIndex: "reportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "作业上报结果",
    dataIndex: "reportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "作业上报时间",
    dataIndex: "reportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
  {
    title: "报备上报批次",
    dataIndex: "backupReportBatchId",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
    renderText: (text) => text,
  },
  {
    title: "报备上报状态",
    dataIndex: "backupReportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "报备上报结果",
    dataIndex: "backupReportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "报备上报时间",
    dataIndex: "backupReportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
  {
    title: "流转上报状态",
    dataIndex: "progressReportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "流转上报结果",
    dataIndex: "progressReportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "流转上报时间",
    dataIndex: "progressReportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
  // -------------- 新增：电子作业票上报相关列 --------------
  {
    title: "电子作业票上报批次",
    dataIndex: "ticketFileReportBatchId",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
    renderText: (text) => text,
  },
  {
    title: "电子作业票上报状态",
    dataIndex: "ticketFileReportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "电子作业票上报结果",
    dataIndex: "ticketFileReportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "电子作业票上报时间",
    dataIndex: "ticketFileReportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
  {
    title: "安全措施上报状态",
    dataIndex: "measureReportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "安全措施上报结果",
    dataIndex: "measureReportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "安全措施上报时间",
    dataIndex: "measureReportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
  // -------------- 新增：气体分析上报相关列 --------------
  {
    title: "气体分析上报状态",
    dataIndex: "gasReportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "气体分析上报结果",
    dataIndex: "gasReportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "气体分析上报时间",
    dataIndex: "gasReportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
  // -------------- 新增：视频上报相关列 --------------
  {
    title: "视频上报批次",
    dataIndex: "videoReportBatchId",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
    renderText: (text) => text,
  },
  {
    title: "视频上报状态",
    dataIndex: "videoReportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "视频上报结果",
    dataIndex: "videoReportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "视频上报时间",
    dataIndex: "videoReportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
  // -------------- 新增：关联作业票上报相关列 --------------
  {
    title: "关联作业票上报状态",
    dataIndex: "relateReportStatus",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "关联作业票上报结果",
    dataIndex: "relateReportResult",
    isShow: false,
    ellipsis: true,
    width: 150,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "关联作业票上报时间",
    dataIndex: "relateReportTime",
    isShow: false,
    ellipsis: true,
    width: 200,
    render: (item) => {
      const content = formatDate(item);
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (text) => formatDate(text),
  },
];

export const jobSliceColumnsAtom = atom(jobSliceColumns);

const jobSliceContractorColumns = [
  {
    title: "作业类型",
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text?.name}>
        <span className="block truncate">{text?.name}</span>
      </Tooltip>
    ),
    renderText: (text) => text?.name,
  },
  {
    title: "申请人",
    dataIndex: "applyPerson",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text?.name}>
        <span className="block truncate">{text?.name}</span>
      </Tooltip>
    ),
    renderText: (text) => text?.name,
  },
  {
    title: "申请部门",
    dataIndex: "applyDepartment",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text?.name}>
        <span className="block truncate">{text?.name}</span>
      </Tooltip>
    ),
    renderText: (text) => text?.name,
  },
  {
    title: "申请时间",
    dataIndex: "applyTime",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (text) => (
      <Tooltip content={dayjs(text).format("YYYY/MM/DD HH:mm")}>
        <p>{dayjs(text).format("YYYY/MM/DD HH:mm") ?? ""}</p>
      </Tooltip>
    ),
    renderText: (text) => formatDate(text),
  },
  {
    title: "作业单位类型",
    dataIndex: "unitCategory",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(UNIT_CATEGORY_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item, record, index) => {
      const i = find(propEq(item, "id"))(UNIT_CATEGORY_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: "关联监控",
    dataIndex: "monitor",
    isShow: true,
    ellipsis: true,
    width: 150,
    render: (item, record) => {
      return <VideoModal data={item} />;
    },
    // render: (text) => <Tooltip content={text?.name}><span className="block truncate">{text?.name}</span></Tooltip>,
    renderText: (text) => text?.name,
  },
  {
    title: "计划开始时间",
    dataIndex: "planBeginTime",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (text) => (
      <Tooltip content={dayjs(text).format("YYYY/MM/DD HH:mm")}>
        <p>{dayjs(text).format("YYYY/MM/DD HH:mm") ?? ""}</p>
      </Tooltip>
    ),
    renderText: (text) => formatDate(text),
  },
  {
    title: "计划结束时间",
    dataIndex: "planEndTime",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (text) => (
      <Tooltip content={dayjs(text).format("YYYY/MM/DD HH:mm")}>
        <p>{dayjs(text).format("YYYY/MM/DD HH:mm")}</p>
      </Tooltip>
    ),
    renderText: (text) => formatDate(text),
  },
  {
    title: "作业区域",
    dataIndex: "workArea",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text?.name}>
        <span className="block truncate">{text?.name}</span>
      </Tooltip>
    ),
    renderText: (text) => text?.name,
  },
  {
    title: "作业内容",
    dataIndex: "workContent",
    isShow: true,
    ellipsis: true,
    width: 100,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "作业状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    width: 150,
    render: (text: string) => {
      const i = find(propEq(text, "id"))(JOB_SLICE_STATUS);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name}
        </Tag>
      );
    },
    renderText: (text: string) => {
      const i = find(propEq(text, "id"))(JOB_SLICE_STATUS);
      return i?.name ?? "-";
    },
  },
  {
    title: "承包商",
    dataIndex: "contractor",
    isShow: false,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
    renderText: (text) => text,
  },
];

const jobSliceContractorColumns2 = jobSliceColumns.map((item) => {
  if (item.dataIndex === "contractor") {
    return {
      ...item,
      isShow: true,
    };
  }
  return item;
});

console.debug(jobSliceColumns);
console.debug(jobSliceContractorColumns);
console.debug(jobSliceContractorColumns2);
console.debug(jobSliceContractorColumns === jobSliceContractorColumns2);

export const jobSliceColumnsInContractorAtom = atom(jobSliceContractorColumns);
