import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { SAFETY_ANALYSIS_JOBSTEP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const safetyAnalysisConfigModalAtom = atom(false);

export const safetyAnalysisEditModalAtom = atom({
  id: "",
  show: false,
});

export const safetyAnalysisMoreModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const safetyAnalysisDrawerModalAtom = atomWithReset({
  id: "",
  riskLevel: undefined,
  show: false,
});

// 查询条件
export const safetyAnalysisFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const safetyAnalysisFnAtom = atom({
  refetch: () => {},
});

export const safetyAnalysisColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="作业类型">作业类型</Tooltip>,
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name}>
        <span className="block truncate">{item?.name}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="工作步骤">工作步骤</Tooltip>,
    dataIndex: "jobStep",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(SAFETY_ANALYSIS_JOBSTEP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="危险因素">危险因素</Tooltip>,
    dataIndex: "riskFactor",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="危险后果">危险后果</Tooltip>,
    dataIndex: "riskResult",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="控制措施">控制措施</Tooltip>,
    dataIndex: "controlMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p className="truncate">{text ?? ""}</p>
      </Tooltip>
    ),
  },
]);

export const safetyAnalysisExportAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: "作业类型",
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => item?.name ?? "-",
  },
  {
    title: "工作步骤",
    dataIndex: "jobStep",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => {
      const i = find(propEq(item, "id"))(SAFETY_ANALYSIS_JOBSTEP);
      return i?.name ?? "-";
    },
  },
  {
    title: "危险因素",
    dataIndex: "riskFactor",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "危险后果",
    dataIndex: "riskResult",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p>{text ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "控制措施",
    dataIndex: "controlMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text ?? ""}>
        <p className="truncate">{text ?? ""}</p>
      </Tooltip>
    ),
  },
]);
