import { Tooltip } from "@douyinfe/semi-ui";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { operationLevelOptions } from "pages/ticket";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const gasIntervalFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const gasIntervalFnAtom = atom({
  refetch: () => {},
});

export const gasIntervalEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const gasIntervalConfigModalAtom = atom(false);

export const gasIntervalColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "作业类型",
    dataIndex: "category",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "作业级别",
    dataIndex: "level",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      if (record.category?.name !== "动火作业") {
        return null;
      }
      const i = find(propEq(item, "id"))(operationLevelOptions);
      return <Tooltip content={i?.label ?? "-"}>{i?.label ?? "-"}</Tooltip>;
    },
  },
  {
    title: "作业前有效期(分钟)",
    dataIndex: "beforeJobInterval",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "作业中有效期(分钟)",
    dataIndex: "startedJobInterval",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "作业暂停有效期(分钟)",
    dataIndex: "pausedJobInterval",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "到期提醒推送时间(分钟）",
    dataIndex: "advanceNoticeTime",
    isShow: true,
    ellipsis: true,
  },
]);

export const gasIntervalAtoms: CommonAtoms = {
  entity: "GasInterval",
  filter: gasIntervalFilterAtom,
  Fn: gasIntervalFnAtom,
  editModal: gasIntervalEditModalAtom,
  configModal: gasIntervalConfigModalAtom,
  columns: gasIntervalColumnsAtom,
};
