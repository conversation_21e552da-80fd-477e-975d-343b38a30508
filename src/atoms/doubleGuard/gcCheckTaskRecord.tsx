import { DANGER_CHECK_TYPE } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const doubleGuardGcCheckTaskRecordFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const doubleGuardGcCheckTaskRecordFnAtom = atom({
  refetch: () => {},
});

export const doubleGuardGcCheckTaskRecordEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardGcCheckTaskRecordDispatchModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardGcCheckTaskRecordConfigModalAtom = atom(false);

const doubleGuardGcCheckTaskRecordShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 72,
  },
  // user-defined code here
  {
    title: "任务名称",
    dataIndex: "taskName",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查类型",
    dataIndex: "checkType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(DANGER_CHECK_TYPE);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "重大危险源对象",
    dataIndex: "majorHazardList",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "开始时间",
    dataIndex: "taskStartTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "结束时间",
    dataIndex: "taskEndTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "完成时间",
    dataIndex: "checkTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "任务类型",
    dataIndex: "taskTypeName",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "任务描述",
    dataIndex: "taskContent",
    isShow: true,
    ellipsis: true,
  },
];

const doubleGuardGcCheckTaskRecordExtendColumns = [
  // user-defined code here
];

export const doubleGuardGcCheckTaskRecordShowColumnsAtom = atom(
  doubleGuardGcCheckTaskRecordShowColumns
);

export const doubleGuardGcCheckTaskRecordColumnsAtom = atom([
  ...doubleGuardGcCheckTaskRecordShowColumns,
  ...doubleGuardGcCheckTaskRecordExtendColumns,
]);

/*export const doubleGuardGcCheckTaskRecordColumnsAtom = atom(
  (get) => get(doubleGuardGcCheckTaskRecordShowColumnsAtom).concat(get(doubleGuardGcCheckTaskRecordExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(doubleGuardGcCheckTaskRecordShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(doubleGuardGcCheckTaskRecordExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const doubleGuardGcCheckTaskRecordAtoms: CommonAtoms = {
  entity: "DoubleGuardGcCheckTaskRecord",
  filter: doubleGuardGcCheckTaskRecordFilterAtom,
  Fn: doubleGuardGcCheckTaskRecordFnAtom,
  editModal: doubleGuardGcCheckTaskRecordEditModalAtom,
  configModal: doubleGuardGcCheckTaskRecordConfigModalAtom,
  columns: doubleGuardGcCheckTaskRecordColumnsAtom,
};
