import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { DANGER_CHECK_TYPE, IS_ISNOT_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const doubleGuardGcTypeConfigFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const doubleGuardGcTypeConfigFnAtom = atom({
  refetch: () => {},
});

export const doubleGuardGcTypeConfigEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardGcTypeConfigConfigModalAtom = atom(false);

const doubleGuardGcTypeConfigShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "任务类型",
    dataIndex: "taskTypeName",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "检查类型列表",
    dataIndex: "checkTypeList",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>
                {find(propEq(item, "id"))(DANGER_CHECK_TYPE)?.name}
              </span>
            ) : (
              <span key={index}>
                {find(propEq(item, "id"))(DANGER_CHECK_TYPE)?.name},
              </span>
            )
          )
        : null;
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "是否按重大危险源分解",
    dataIndex: "needSplitMajorHazard",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const doubleGuardGcTypeConfigExtendColumns = [
  // user-defined code here
];

export const doubleGuardGcTypeConfigShowColumnsAtom = atom(
  doubleGuardGcTypeConfigShowColumns
);

export const doubleGuardGcTypeConfigColumnsAtom = atom([
  ...doubleGuardGcTypeConfigShowColumns,
  ...doubleGuardGcTypeConfigExtendColumns,
]);

/*export const doubleGuardGcTypeConfigColumnsAtom = atom(
  (get) => get(doubleGuardGcTypeConfigShowColumnsAtom).concat(get(doubleGuardGcTypeConfigExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(doubleGuardGcTypeConfigShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(doubleGuardGcTypeConfigExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const doubleGuardGcTypeConfigAtoms: CommonAtoms = {
  entity: "DoubleGuardGcTypeConfig",
  filter: doubleGuardGcTypeConfigFilterAtom,
  Fn: doubleGuardGcTypeConfigFnAtom,
  editModal: doubleGuardGcTypeConfigEditModalAtom,
  configModal: doubleGuardGcTypeConfigConfigModalAtom,
  columns: doubleGuardGcTypeConfigColumnsAtom,
};
