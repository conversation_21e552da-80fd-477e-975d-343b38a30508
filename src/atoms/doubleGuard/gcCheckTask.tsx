import { DANGER_CHECK_TYPE, DG_GC_STATUS_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const doubleGuardGcCheckTaskFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const doubleGuardGcCheckTaskFnAtom = atom({
  refetch: () => {},
});

export const doubleGuardGcCheckTaskEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardGcCheckTaskConfigModalAtom = atom(false);

const doubleGuardGcCheckTaskShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
    fixed: "left",
  },
  // user-defined code here
  {
    title: "任务名称",
    dataIndex: "taskName",
    isShow: true,
    ellipsis: true,
    fixed: "left",
  },
  {
    title: "设置检查类型",
    dataIndex: "checkTypeList",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>
                {find(propEq(item, "id"))(DANGER_CHECK_TYPE)?.name}
              </span>
            ) : (
              <span key={index}>
                {find(propEq(item, "id"))(DANGER_CHECK_TYPE)?.name},
              </span>
            )
          )
        : null;
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "开始时间",
    dataIndex: "taskStartTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "结束时间",
    dataIndex: "taskEndTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "任务描述",
    dataIndex: "taskContent",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "重大危险源对象",
    dataIndex: "majorHazardList",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "下发任务状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(DG_GC_STATUS_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const doubleGuardGcCheckTaskExtendColumns = [
  // user-defined code here
];

export const doubleGuardGcCheckTaskShowColumnsAtom = atom(
  doubleGuardGcCheckTaskShowColumns
);

export const doubleGuardGcCheckTaskColumnsAtom = atom([
  ...doubleGuardGcCheckTaskShowColumns,
  ...doubleGuardGcCheckTaskExtendColumns,
]);

/*export const doubleGuardGcCheckTaskColumnsAtom = atom(
  (get) => get(doubleGuardGcCheckTaskShowColumnsAtom).concat(get(doubleGuardGcCheckTaskExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(doubleGuardGcCheckTaskShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(doubleGuardGcCheckTaskExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const doubleGuardGcCheckTaskAtoms: CommonAtoms = {
  entity: "DoubleGuardGcCheckTask",
  filter: doubleGuardGcCheckTaskFilterAtom,
  Fn: doubleGuardGcCheckTaskFnAtom,
  editModal: doubleGuardGcCheckTaskEditModalAtom,
  configModal: doubleGuardGcCheckTaskConfigModalAtom,
  columns: doubleGuardGcCheckTaskColumnsAtom,
};
