import {
  ALLOW_ALLOWNOT_MAP,
  INTELLIGENTINSPECTION_PERSON_TYPE_MAP,
  TIME_UNIT_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const doubleGuardCcPlanFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const doubleGuardCcPlanFnAtom = atom({
  refetch: () => {},
});

export const doubleGuardCcPlanEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const doubleGuardCcPlanConfigModalAtom = atom(false);

const doubleGuardCcPlanShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 48,
  },
  // user-defined code here
  {
    title: "巡检计划名称",
    dataIndex: "name",
    isShow: true,
  },
  {
    title: "巡检人员类型",
    dataIndex: "checkType",
    isShow: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(INTELLIGENTINSPECTION_PERSON_TYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "持续时间",
    dataIndex: "durationTime",
    isShow: true,
    render: (text, record) => {
      const i = find(propEq(record?.durationTimeUnit, "id"))(TIME_UNIT_MAP);
      if (record.durationTime === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.durationTime}
          {i?.id === 4 ? "个" : ""}
          {/* 小时 */}
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: "允许逾期提交",
    dataIndex: "allowOvertimeSubmit",
    isShow: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(ALLOW_ALLOWNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "允许批量提交",
    dataIndex: "allowBatchSubmit",
    isShow: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(ALLOW_ALLOWNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "排查项个数",
    dataIndex: "itemNum",
    isShow: true,
  },
  {
    title: "计划个数",
    dataIndex: "detailNum",
    isShow: true,
  },
];

const doubleGuardCcPlanExtendColumns = [
  // user-defined code here
];

export const doubleGuardCcPlanShowColumnsAtom = atom(
  doubleGuardCcPlanShowColumns
);

export const doubleGuardCcPlanColumnsAtom = atom([
  ...doubleGuardCcPlanShowColumns,
  ...doubleGuardCcPlanExtendColumns,
]);

/*export const doubleGuardCcPlanColumnsAtom = atom(
  (get) => get(doubleGuardCcPlanShowColumnsAtom).concat(get(doubleGuardCcPlanExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(doubleGuardCcPlanShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(doubleGuardCcPlanExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const doubleGuardCcPlanAtoms: CommonAtoms = {
  entity: "DoubleGuardCcPlan",
  filter: doubleGuardCcPlanFilterAtom,
  Fn: doubleGuardCcPlanFnAtom,
  editModal: doubleGuardCcPlanEditModalAtom,
  configModal: doubleGuardCcPlanConfigModalAtom,
  columns: doubleGuardCcPlanColumnsAtom,
};
