import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  CONTROL_LEVEL_RENDER_MAP,
  EVALUATION_TYPE_MAP,
  RISK_LEVEL_MAP,
} from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const historyConfigModalAtom = atom(false);

export const historyListModalAtom = atomWithReset({
  id: null,
  show: false,
});

// 查询条件
export const historyFilterAtom = atomWithReset<HistoryParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 选中项
export const historySelectAtom = atom<string | null>(null);

// 全部数据存储
export const historyDataAtom = atom<any[]>([]);

// 刷新
export const historyFnAtom = atom({
  refetch: () => {},
});

export const historyColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="事件名称">事件名称</Tooltip>,
    dataIndex: "riskEvent",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险对象">关联风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险单元">关联风险单元</Tooltip>,
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="评估方法">评估方法</Tooltip>,
    dataIndex: "evaluationType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(EVALUATION_TYPE_MAP);
      return <p>{index?.name ?? ""}</p>;
    },
  },
  {
    title: <Tooltip content="风险等级">风险等级</Tooltip>,
    dataIndex: "riskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={index?.name ?? "-"}>
          <Tag color={index?.color} type="light">
            {index?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="管控等级">管控等级</Tooltip>,
    dataIndex: "controlLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(CONTROL_LEVEL_RENDER_MAP);
      return (
        <Tooltip content={index?.name ?? "-"}>
          <Tag color={index?.color} type="light">
            {index?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="分析结果值">分析结果值</Tooltip>,
    dataIndex: "riskScore",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="上次复评日期">上次复评日期</Tooltip>,
    dataIndex: "reviewDatetime",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{dayjs(item).format("YYYY-MM-DD") ?? ""}</p>,
  },
  {
    title: <Tooltip content="下次复评日期">下次复评日期</Tooltip>,
    dataIndex: "nextReviewDatetime",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{dayjs(item).format("YYYY-MM-DD") ?? ""}</p>,
  },
]);
