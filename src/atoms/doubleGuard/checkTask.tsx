import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  CHECK_CYCLE_UNIT_MAP,
  CLASSIFY1_MAP,
  CLASSIFY2_MAP,
  CONTROLTYPE_MAP,
  DANGER_REPORT_STATUS_MAP,
  PLAN_CHECK_TYPE_MAP,
  TASK_STATUS_MAP,
} from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { find, propEq, whereEq } from "ramda";

export const checkTaskConfigModalAtom = atom(false);

// 查询条件
export const checkTaskFilterAtom = atom({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
});

export const checkTaskColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="排查计划id">排查计划id</Tooltip>,
    dataIndex: "checkPlanId",
    isShow: true,
    width: 120,
  },
  {
    title: <Tooltip content="关联风险对象">关联风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险单元">关联风险单元</Tooltip>,
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险事件">关联风险事件</Tooltip>,
    dataIndex: "riskEvent",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="责任部门">责任部门</Tooltip>,
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="管控方式">管控方式</Tooltip>,
    dataIndex: "controlType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(CONTROLTYPE_MAP);
      return <p>{i?.name ?? ""}</p>;
    },
  },
  {
    title: <Tooltip content="一级分类">一级分类</Tooltip>,
    dataIndex: "classify1",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(CLASSIFY1_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="二级分类">二级分类</Tooltip>,
    dataIndex: "classify2",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return (
          <Tag color="grey" type="light" className="min-w-[fit-content]">
            未分类
          </Tag>
        );
      }
      const k = item?.split("-");
      const i = find(
        whereEq({
          pid: parseInt(k[0]),
          id: parseInt(k[1]),
        })
      )(CLASSIFY2_MAP);

      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="三级分类">三级分类</Tooltip>,
    dataIndex: "classify3",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return null;
      }
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {item}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="管控措施">管控措施</Tooltip>,
    dataIndex: "controlMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="隐患排查内容">隐患排查内容</Tooltip>,
    dataIndex: "troubleShootContent",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="排查类型">排查类型</Tooltip>,
    dataIndex: "checkType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return null;
      }
      const i = find(propEq(item, "id"))(PLAN_CHECK_TYPE_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="通知人员列表">通知人员列表</Tooltip>,
    dataIndex: "informPerson",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item || !item?.length) {
        return null;
      }
      return (
        <Tooltip content={item.map((o) => o.name).toString()}>
          <div className="flex gap-1">
            {(item ?? []).map((o) => (
              <Tag color="grey" type="light" className="min-w-[fit-content]">
                {o.name}
              </Tag>
            ))}
          </div>
        </Tooltip>
      );
    },
  },
  {
    // title: <Tooltip content="排查周期">排查周期</Tooltip>,
    dataIndex: "checkCycle",
    // isShow: true,
    // ellipsis: true,
    align: "center",
    // }, {
    title: (
      <Tooltip
        content="
    排查周期"
      >
        任务排查周期
      </Tooltip>
    ),
    // dataIndex: 'checkCycleUnit',
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.checkCycleUnit, "id")
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.checkCycle === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.checkCycle}
          {i?.id !== 2 ? "个" : ""}
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="持续时间">持续时间</Tooltip>,
    dataIndex: "duration",
    align: "center",
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      const i = find(
        propEq(record?.durationUnit, "id")
      )(CHECK_CYCLE_UNIT_MAP);
      if (record.duration === undefined) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {record.duration}
          {i?.id !== 2 ? "个" : ""}
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="任务开始时间">任务开始时间</Tooltip>,
    dataIndex: "workBeginTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return null;
      }
      return <p>{dayjs(item).format("YYYY-MM-DD HH:mm")}</p>;
    },
  },
  {
    title: <Tooltip content="任务结束时间">任务结束时间</Tooltip>,
    dataIndex: "workEndTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return null;
      }
      return <p>{dayjs(item).format("YYYY-MM-DD HH:mm")}</p>;
    },
  },
  /* {
    title: (
      <Tooltip content="是否是包保责任排查任务">是否是包保责任排查任务</Tooltip>
    ),
    dataIndex: "isBb",
    isShow: true,
    ellipsis: true,
    align: "center",
    render: (item) => {
      if (item == 1) {
        return (
          <Tag color="red" type="light">
            是
          </Tag>
        );
      }
      return (
        <Tag color="green" type="light">
          否
        </Tag>
      );
    },
  }, */
  {
    title: <Tooltip content="状态">状态</Tooltip>,
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(TASK_STATUS_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="是否逾期">是否逾期</Tooltip>,
    dataIndex: "isOvertime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (item == 1) {
        return (
          <Tag color="red" type="light">
            是
          </Tag>
        );
      }
      return (
        <Tag color="green" type="light">
          否
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "uploadStatus",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
]);
