import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  ALLOW_REPORT_STATUS_MAP,
  CLASSIFY1_MAP,
  CLASSIFY2_MAP,
  CONTROLTYPE_MAP,
  DANGER_REPORT_STATUS_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq, whereEq } from "ramda";
// measure
export const measureConfigModalAtom = atom(false);

export const measureEditModalAtom = atom({
  item: null,
  show: false,
});

// 风险事件
export const measureEvaluationModalAtom = atom({
  id: null,
  show: false,
});

// 查询条件
export const measureFilterAtom = atomWithReset<any>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 选中项
export const measureSelectAtom = atomWithReset<string | null>(null);

// 全部数据存储
export const measureDataAtom = atom<any[]>([]);

// 刷新
export const measureFnAtom = atom({
  refetch: () => {},
});

export const measureColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="关联风险对象">关联风险对象</Tooltip>,
    dataIndex: "riskObject",
    isShow: true,
    ellipsis: true,
    width: 180,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险单元">关联风险单元</Tooltip>,
    dataIndex: "riskUnit",
    isShow: true,
    ellipsis: true,
    width: 180,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="关联风险事件">关联风险事件</Tooltip>,
    dataIndex: "riskEvent",
    isShow: true,
    ellipsis: true,
    width: 180,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="管控方式">管控方式</Tooltip>,
    dataIndex: "controlType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(CONTROLTYPE_MAP);
      return <p>{i?.name ?? ""}</p>;
    },
  },
  {
    title: <Tooltip content="一级分类">一级分类</Tooltip>,
    dataIndex: "classify1",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(CLASSIFY1_MAP);
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="二级分类">二级分类</Tooltip>,
    dataIndex: "classify2",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return (
          <Tag color="grey" type="light" className="min-w-[fit-content]">
            未分类
          </Tag>
        );
      }
      const k = item?.split("-");
      const i = find(
        whereEq({
          pid: parseInt(k[0]),
          id: parseInt(k[1]),
        })
      )(CLASSIFY2_MAP);

      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {i?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="三级分类">三级分类</Tooltip>,
    dataIndex: "classify3",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) return null;
      return (
        <Tag color="grey" type="light" className="min-w-[fit-content]">
          {item ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="管控措施">管控措施</Tooltip>,
    dataIndex: "controlMeasure",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="隐患排查内容">隐患排查内容</Tooltip>,
    dataIndex: "troubleShootContent",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="是否上报">是否上报</Tooltip>,
    dataIndex: "allowUpload",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(ALLOW_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "uploadStatus",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(DANGER_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
]);
