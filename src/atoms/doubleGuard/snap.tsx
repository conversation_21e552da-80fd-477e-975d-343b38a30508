import { Image, ImagePreview, Tag, Tooltip } from "@douyinfe/semi-ui";
import { SnapParams } from "api";
import { SNAP_STATUS_MAP } from "components";
import { base_url } from "config";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const snapConfigModalAtom = atom(false);

export enum SnapType {
  Immediately,
  Delayed,
  Normal,
}

export const snapEditModalAtom = atomWithReset({
  id: "",
  show: false,
  type: SnapType.Delayed,
});

// 查询条件
export const snapFilterAtom = atomWithReset<SnapParams>({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
});

// 查询条件
export const snapFnAtom = atom({
  refetch: () => {},
});

export const snapColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // {
  //   title: <Tooltip content="名称">名称</Tooltip>,
  //   dataIndex: 'name',
  //   isShow: true,
  //   ellipsis: true,
  //   align: 'center',
  // },
  {
    title: <Tooltip content="内容">内容</Tooltip>,
    dataIndex: "content",
    isShow: true,
    ellipsis: true,
    // width: 200,
    render: (t) => {
      return (
        <Tooltip content={t}>
          <p>{t}</p>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="图片">图片</Tooltip>,
    dataIndex: "pictures",
    isShow: true,
    align: "center",
    ellipsis: true,
    render: (record) => {
      if (!record) {
        return null;
      }
      let list;
      try {
        list = JSON.parse(record);
        if (list && !Array.isArray(list) && list.values) {
          // 处理脏数据
          list = list.values;
        }
        if (!Array.isArray(list)) {
          list = [];
        }
      } catch (e) {
        list = [];
      }

      return (
        <ImagePreview>
          {list.map((o) => (
            <Image width={30} height={30} src={`${base_url}${o}`} />
          ))}
        </ImagePreview>
      );
    },
  },
  {
    title: <Tooltip content="位置">位置</Tooltip>,
    dataIndex: "area.name",
    isShow: true,
    ellipsis: true,
    // width: 200,
    render: (t) => {
      return (
        <Tooltip content={t}>
          <p>{t}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "录音备注",
    dataIndex: "soundNote",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const url = base_url + item;
      return (
        <Tooltip content={item.split("/").pop()}>
          <a href={url} target="_blank">
            {" "}
            {item.split("/").pop()}
          </a>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="上报人">上报人</Tooltip>,
    align: "center",
    dataIndex: "reporter",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      return <p>{record?.name}</p>;
    },
  },
  {
    title: <Tooltip content="上报时间">上报时间</Tooltip>,
    align: "center",
    dataIndex: "reportAt",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      return <p>{dayjs(record).format("YYYY-MM-DD HH:mm")}</p>;
    },
  },
  {
    title: <Tooltip content="状态">状态</Tooltip>,
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    align: "center",
    render: (item) => {
      const i = find(propEq(item, "id"))(SNAP_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="审核人">审核人</Tooltip>,
    align: "center",
    dataIndex: "auditor",
    isShow: true,
    ellipsis: true,
    render: (record) => {
      return <p>{record?.name}</p>;
      /* return (
       *   <div>
       *     {
       *       (record ?? []).map(o => (
       *         <p key={o.id}>{o.name}</p>
       *       ))
       *     }
       *   </div>
       * ) */
    },
  },
  {
    title: <Tooltip content="审核日期">审核日期</Tooltip>,
    dataIndex: "auditAt",
    align: "center",
    isShow: true,
    ellipsis: true,
    render: (text, record) => {
      if (record.status === 1) {
        return null;
      }
      return <p>{dayjs(text).format("YYYY-MM-DD HH:mm")}</p>;
    },
  },
]);

export const snapExportColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    header: "ID",
  },
  // {
  //   title: <Tooltip content="名称">名称</Tooltip>,
  //   dataIndex: 'name',
  //   isShow: true,
  //   ellipsis: true,
  //   align: 'center',
  // },
  {
    title: <Tooltip content="内容">内容</Tooltip>,
    dataIndex: "content",
    header: "内容",
    renderText: (item, record) => item,
  },
  {
    title: <Tooltip content="位置">位置</Tooltip>,
    dataIndex: "area",
    header: "位置",
    renderText: (item, record) => item?.name,
  },
  {
    title: <Tooltip content="上报人">上报人</Tooltip>,
    dataIndex: "reporter",
    header: "上报人",
    renderText: (item, record) => item?.name,
  },
  {
    title: <Tooltip content="上报时间">上报时间</Tooltip>,
    dataIndex: "reportAt",
    header: "上报时间",
    renderText: (item, record) => dayjs(item).format("YYYY-MM-DD HH:mm"),
  },
  {
    title: <Tooltip content="状态">状态</Tooltip>,
    dataIndex: "status",
    header: "状态",
    renderText: (item, record) => {
      const i = find(propEq(item ? item : 1, "id"))(SNAP_STATUS_MAP);
      return i?.name;
    },
  },
  {
    title: <Tooltip content="审核人">审核人</Tooltip>,
    dataIndex: "auditor",
    header: "审核人",
    renderText: (item, record) => item?.name,
  },
  {
    title: <Tooltip content="审核日期">审核日期</Tooltip>,
    dataIndex: "auditAt",
    header: "审核日期",
    renderText: (item, record) => {
      if (record.status === 1) {
        return null;
      }
      return dayjs(item).format("YYYY-MM-DD HH:mm");
    },
  },
]);
