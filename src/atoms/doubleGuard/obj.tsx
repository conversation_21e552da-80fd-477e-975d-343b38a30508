import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  ALLOW_REPORT_STATUS_MAP,
  IS_MAJOR_HAZARD_MAP,
  RISK_LEVEL_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const objectConfigModalAtom = atom(false);

export const objectEditModalAtom = atom({
  id: "",
  show: false,
});

// 查询条件
export const objectFilterAtom = atomWithReset<ObjectParams>({
  pageNumber: 1,
  pageSize: 10,
  query: "",
  filter: {},
  departmentId: null,
});

// 查询条件
export const objectFnAtom = atom({
  refetch: () => {},
});

const STATUS_MAP = {
  1: (
    <Tag color="green" type="light">
      在职
    </Tag>
  ),
  2: (
    <Tag color="grey" type="light" className="min-w-[fit-content]">
      离职
    </Tag>
  ),
  3: (
    <Tag color="yellow" type="light">
      退休
    </Tag>
  ),
};

export const objectShowColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="对象名称">对象名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="风险对象编码">风险对象编码</Tooltip>,
    dataIndex: "hazardCode",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
]);

export const objectColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="对象名称">对象名称</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    fixed: true,
    width: 180,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="重大危险源">重大危险源</Tooltip>,
    dataIndex: "isMajorHazard",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item, "id"))(IS_MAJOR_HAZARD_MAP);
      return (
        <Tooltip content={index?.name ?? "-"}>
          <Tag color={index?.color ?? "-"} type="light">
            {index?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="风险对象编码">风险对象编码</Tooltip>,
    dataIndex: "hazardCode",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="风险分区">风险分区</Tooltip>,
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="固有风险等级">固有风险等级</Tooltip>,
    dataIndex: "riskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={index?.name ?? "-"}>
          <Tag color={index?.color ?? "-"} type="light">
            {index?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="剩余风险等级">剩余风险等级</Tooltip>,
    dataIndex: "remainRiskLevel",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const index = find(propEq(item || 5, "id"))(RISK_LEVEL_MAP);
      return (
        <Tooltip content={index?.name ?? "-"}>
          <Tag color={index?.color ?? "-"} type="light">
            {index?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: <Tooltip content="责任人">责任人</Tooltip>,
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="责任部门">责任部门</Tooltip>,
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="是否上报">是否上报</Tooltip>,
    dataIndex: "allowUpload",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(ALLOW_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
]);
