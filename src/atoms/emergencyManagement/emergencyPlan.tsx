import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { EMERGENCYPLAN_LEVEL_MAP, EMERGENCYPLAN_TYPE_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const emergencyPlanFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const emergencyPlanFnAtom = atom({
  refetch: () => {},
});

export const emergencyPlanEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const emergencyPlanConfigModalAtom = atom(false);

export const emergencyPlanColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: "预案名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "适用部门",
    dataIndex: "targetDepartment",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "预案类型",
    dataIndex: "type",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(EMERGENCYPLAN_TYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "预案级别",
    dataIndex: "level",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(EMERGENCYPLAN_LEVEL_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "危险源关联",
    dataIndex: "majorHazard",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "发布实施日期",
    dataIndex: "implementationTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
]);

export const emergencyPlanAtoms: CommonAtoms = {
  entity: "EmergencyPlan",
  filter: emergencyPlanFilterAtom,
  Fn: emergencyPlanFnAtom,
  editModal: emergencyPlanEditModalAtom,
  configModal: emergencyPlanConfigModalAtom,
  columns: emergencyPlanColumnsAtom,
};
