import { Space, Tag } from "@douyinfe/semi-ui";
import { ALARM_LEVEL_MAP, PUSHCONFIG_PUSHMETHOD_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const alarmPushConfigFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const alarmPushConfigFnAtom = atom({
  refetch: () => {},
});

export const alarmPushConfigEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const alarmPushConfigConfigModalAtom = atom(false);

const alarmPushConfigShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "报警推送名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "推送方式",
    dataIndex: "pushMethodList",
    isShow: true,
    ellipsis: true,
    render: (item: number[]) => {
      if (!Array.isArray(item)) {
        return null;
      }
      return (
        <Space wrap>
          {item.map((id) => {
            const method = find(propEq(id, "id"))(PUSHCONFIG_PUSHMETHOD_MAP);
            return method ? <Tag key={id}>{method?.name ?? "-"}</Tag> : null;
          })}
        </Space>
      );
    },
  },
  {
    title: "首次推送距触发间隔时间(秒)",
    dataIndex: "firstPushInterval",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "持续推送间隔时间(秒)",
    dataIndex: "continuousPushInterval",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "延迟推送间隔时间(秒)",
    dataIndex: "delayPushInterval",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "分级报警",
    dataIndex: "alarmLevel",
    isShow: true,
    ellipsis: true,
    render: (item: number) => {
      const level = find(propEq(item, "id"))(ALARM_LEVEL_MAP);
      return level ? (
        <Tag key={item}>{level?.name ?? "-"}</Tag>
      ) : (
        <Tag key={item}>-</Tag>
      );
    },
  },
];

const alarmPushConfigExtendColumns = [
  // user-defined code here
];

export const alarmPushConfigColumnsAtom = atom([
  ...alarmPushConfigShowColumns,
  ...alarmPushConfigExtendColumns,
]);

export const alarmPushConfigAtoms: CommonAtoms = {
  entity: "AlarmPushConfig",
  filter: alarmPushConfigFilterAtom,
  Fn: alarmPushConfigFnAtom,
  editModal: alarmPushConfigEditModalAtom,
  configModal: alarmPushConfigConfigModalAtom,
  columns: alarmPushConfigColumnsAtom,
};
