import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { NOTICEPUSHCONFIG_CONDITIONTYPE_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const noticePushConfigFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const noticePushConfigFnAtom = atom({
  refetch: () => {},
});

export const noticePushConfigEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const noticePushConfigConfigModalAtom = atom(false);

const noticePushConfigShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "通知推送名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "触发事件",
    dataIndex: "conditionType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(NOTICEPUSHCONFIG_CONDITIONTYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "推送提前时间",
    dataIndex: "advanceTime",
    isShow: true,
    ellipsis: true,
  },
];

const noticePushConfigExtendColumns = [
  // user-defined code here
];

export const noticePushConfigColumnsAtom = atom([
  ...noticePushConfigShowColumns,
  ...noticePushConfigExtendColumns,
]);

export const noticePushConfigAtoms: CommonAtoms = {
  entity: "NoticePushConfig",
  filter: noticePushConfigFilterAtom,
  Fn: noticePushConfigFnAtom,
  editModal: noticePushConfigEditModalAtom,
  configModal: noticePushConfigConfigModalAtom,
  columns: noticePushConfigColumnsAtom,
};
