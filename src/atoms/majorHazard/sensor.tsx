import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  ALLOW_REPORT_STATUS_MAP,
  IS_ISNOT_ACTIVE_MAP,
  IS_ISNOT_DISPLAY_MAP,
  REPORT_STATUS_MAP,
  SENSOR_ALERTPRIORITY_MAP,
  SENSOR_STATUS_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const sensorFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const sensorFnAtom = atom({
  refetch: () => {},
});

export const sensorEditModalAtom = atomWithReset({
  id: "",
  show: false,
  equipment: null,
});

export const sensorCodeStateModalAtom = atomWithReset({
  name: "",
  show: false,
});

export const sensorConfigModalAtom = atom(false);

const shouldRenderDash = (record) => {
  return record.isActive === 2;
};

const sensorExtendColumns = [
  {
    title: "监测值",
    dataIndex: "sampleValue",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      if (shouldRenderDash(record)) {
        return "-";
      }
      return item;
    },
  },
  {
    title: "采集时间",
    dataIndex: "sampleTime",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      if (shouldRenderDash(record)) {
        return "-";
      }
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  /* {
    title: "实时状态",
    dataIndex: "sampleStatus",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const res = record.sampleValue > record.highHighAlarm ? '高高报' :
        record.sampleValue > record.highAlarm ? '高报' :
          record.sampleValue < record.lowLowAlarm ? '低低报' :
            record.sampleValue < record.lowAlarm ? '低报' :
              '正常'
      return (
        <p>
          {res}
        </p>
      )
    }
  }, */
  {
    title: "实时状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      if (shouldRenderDash(record)) {
        return "-";
      }
      let i = find(propEq(item, "id"))(SENSOR_STATUS_MAP);
      if (item === 6) {
        // 状态码异常
        i = record.stateCodeList?.find(
          (ele) => ele.code === record.sampleValue
        );
        if (!i) {
          i = {
            color: "red",
            name: "状态码不存在",
          };
        } else {
          i.name = "状态码: " + i.state;
          i.color = "yellow";
        }
      }
      return (
        <Tooltip content={i?.name}>
          <Tag color={i?.color} type="light">
            {i?.name}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "运行状态",
    dataIndex: "isActive",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_ACTIVE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否上报",
    dataIndex: "needUpload",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(ALLOW_REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "上报状态",
    dataIndex: "reportStatus",
    isShow: false,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "上报时间",
    dataIndex: "reportTime",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content || "-"}>
          <p>{content || "-"}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "上报失败原因",
    dataIndex: "reportFailReason",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      return (
        <Tooltip content={item || "-"}>
          <p>{item || "-"}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "报警上报状态",
    dataIndex: "reportAlarmStatus",
    isShow: false,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(REPORT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "报警上报时间",
    dataIndex: "reportAlarmTime",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content || "-"}>
          <p>{content || "-"}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "报警上报失败原因",
    dataIndex: "reportAlarmFailReason",
    isShow: false,
    ellipsis: true,
    render: (item) => {
      return (
        <Tooltip content={item || "-"}>
          <p>{item || "-"}</p>
        </Tooltip>
      );
    },
  },
];

const sensorShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
    // fixed: true,
  },
  // user-defined code here
  {
    title: "监测名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    width: 180,
    // fixed: true,
  },
  {
    title: "传感器编号",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "监测类型",
    dataIndex: "monitorTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
  },
  {
    title: "业务类型",
    dataIndex: "sensorBusinessTypeValue",
    isShow: true,
    ellipsis: true,
    render: (r) => <p>{r?.dicValue ?? ""}</p>,
  },
  {
    title: "报警优先级",
    dataIndex: "priority",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(SENSOR_ALERTPRIORITY_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "阈值范围",
    dataIndex: "highAlarm",
    isShow: true,
    // ellipsis: true,
    render: (item, record, index) => {
      return (
        <p>
          高阈值：{item} ~ {record.highHighAlarm}
          <br />
          低阈值：{record.lowAlarm} ~ {record.lowLowAlarm}
        </p>
      );
    },
  },
  {
    title: "可视化页面是否显示报警",
    dataIndex: "isAlarmDisplay",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_DISPLAY_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "所属设备",
    dataIndex: "equipment",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "所属部门",
    dataIndex: "department",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "所属区域",
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
];

/* export const sensorColumnsAtom = atom(
  (get) => get(sensorShowColumnsAtom).concat(get(sensorExtendColumnsAtom))
)
 */
// 创建 sensorColumnsAtom，设置为可写

export const sensorShowColumnsAtom = atom(sensorShowColumns);

export const sensorColumnsAtom = atom([
  ...sensorShowColumns,
  ...sensorExtendColumns,
]);

export const sensorStateCodeColumnsAtom = atom([
  {
    title: "状态码名称",
    dataIndex: "state",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "代码",
    dataIndex: "code",
    isShow: true,
    ellipsis: true,
  },
]);

export const sensorAtoms: CommonAtoms = {
  entity: "Sensor",
  filter: sensorFilterAtom,
  Fn: sensorFnAtom,
  editModal: sensorEditModalAtom,
  configModal: sensorConfigModalAtom,
  columns: sensorColumnsAtom,
};
