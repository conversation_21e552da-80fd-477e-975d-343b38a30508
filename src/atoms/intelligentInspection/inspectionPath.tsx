import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { IS_ISNOT_MAP, IS_ISNOT_STOPCHECK_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const inspectionPathFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const inspectionPathFnAtom = atom({
  refetch: () => {},
});

export const inspectionPathEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const inspectionPathConfigModalAtom = atom(false);

const inspectionPathShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "巡检路径名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否按顺序巡检",
    dataIndex: "isInOrder",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "巡检点数量",
    dataIndex: "placeCount",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否停检",
    dataIndex: "isStopCheck",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_STOPCHECK_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const inspectionPathExtendColumnsAtom = [
  // user-defined code here
];

export const inspectionPathShowColumnsAtom = atom(inspectionPathShowColumns);

export const inspectionPathColumnsAtom = atom([
  ...inspectionPathShowColumns,
  ...inspectionPathExtendColumnsAtom,
]);

export const inspectionPathAtoms: CommonAtoms = {
  entity: "InspectionPath",
  filter: inspectionPathFilterAtom,
  Fn: inspectionPathFnAtom,
  editModal: inspectionPathEditModalAtom,
  configModal: inspectionPathConfigModalAtom,
  columns: inspectionPathColumnsAtom,
};
