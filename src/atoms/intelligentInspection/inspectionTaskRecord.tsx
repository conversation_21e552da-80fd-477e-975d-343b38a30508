import {
  INTELLIGENTINSPECTION_STATUS_MAP,
  INTELLIGENTINSPECTION_TYPE_MAP,
  ISABNORMAL_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const inspectionTaskRecordFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const inspectionTaskRecordFnAtom = atom({
  refetch: () => {},
});

export const inspectionTaskRecordEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const inspectionTaskRecordConfigModalAtom = atom(false);

export const inspectionTaskRecordDetailSideAtom = atomWithReset({
  id: "",
  show: false,
});

const inspectionTaskRecordShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "巡检计划名称",
    dataIndex: "inspectionPlan",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "巡检路径名称",
    dataIndex: "inspectionPath",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "巡检类型",
    dataIndex: "inspectionType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(INTELLIGENTINSPECTION_TYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "巡检人",
    dataIndex: "processPerson",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "巡检时间",
    dataIndex: "processBeginTime",
    width: 200,
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const processBeginTime = formatDate(text) ? formatDate(text) : "";
      const processEndTime = formatDate(record.processEndTime)
        ? formatDate(record.processEndTime)
        : "";
      const content = processBeginTime + " ~ " + processEndTime;
      return (
        <Tooltip content={content}>
          <p>
            开始:{processBeginTime}
            <br />
            结束:{processEndTime}
          </p>
        </Tooltip>
      );
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(INTELLIGENTINSPECTION_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "巡检结果",
    dataIndex: "isAbnormal",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      if (record.status != 2) return null;
      const i = find(propEq(item, "id"))(ISABNORMAL_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "责任部门",
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
];

const inspectionTaskRecordExtendColumns = [
  // user-defined code here
];

export const inspectionTaskRecordColumnsAtom = atom([
  ...inspectionTaskRecordShowColumns,
  ...inspectionTaskRecordExtendColumns,
]);

/*export const inspectionTaskRecordColumnsAtom = atom(
  (get) => get(inspectionTaskRecordShowColumnsAtom).concat(get(inspectionTaskRecordExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(inspectionTaskRecordShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(inspectionTaskRecordExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const inspectionTaskRecordAtoms: CommonAtoms = {
  entity: "InspectionTaskRecord",
  filter: inspectionTaskRecordFilterAtom,
  Fn: inspectionTaskRecordFnAtom,
  editModal: inspectionTaskRecordEditModalAtom,
  configModal: inspectionTaskRecordConfigModalAtom,
  columns: inspectionTaskRecordColumnsAtom,
};
