import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  INTELLIGENTINSPECTION_STATUS_MAP,
  INTELLIGENTINSPECTION_TYPE_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const inspectionTaskFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const inspectionTaskFnAtom = atom({
  refetch: () => {},
});

export const inspectionTaskEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const inspectionTaskConfigModalAtom = atom(false);

const inspectionTaskShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
    fixed: "left",
  },
  // user-defined code here
  {
    title: "巡检计划名称",
    dataIndex: "inspectionPlan",
    isShow: true,
    ellipsis: true,
    fixed: "left",
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "巡检路径名称",
    dataIndex: "inspectionPath",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "巡检类型",
    dataIndex: "inspectionType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(INTELLIGENTINSPECTION_TYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "任务时间范围",
    dataIndex: "taskBeginTime",
    isShow: true,
    ellipsis: true,
    width: 200,
    render: (text, record, index) => {
      const taskBeginTime = formatDate(text) ? formatDate(text) : "";
      const taskEndTime = formatDate(record.taskEndTime)
        ? formatDate(record.taskEndTime)
        : "";
      const content = taskBeginTime + " ~ " + taskEndTime;
      return (
        <Tooltip content={content}>
          <p>
            开始:{taskBeginTime}
            <br />
            结束:{taskEndTime}
          </p>
        </Tooltip>
      );
    },
  },
  {
    title: "通知人员",
    dataIndex: "candidatePerson",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "责任部门",
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(INTELLIGENTINSPECTION_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const inspectionTaskExtendColumns = [
  // user-defined code here
];

export const inspectionTaskColumnsAtom = atom([
  ...inspectionTaskShowColumns,
  ...inspectionTaskExtendColumns,
]);

/*export const inspectionTaskColumnsAtom = atom(
  (get) => get(inspectionTaskShowColumnsAtom).concat(get(inspectionTaskExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(inspectionTaskShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(inspectionTaskExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const inspectionTaskAtoms: CommonAtoms = {
  entity: "InspectionTask",
  filter: inspectionTaskFilterAtom,
  Fn: inspectionTaskFnAtom,
  editModal: inspectionTaskEditModalAtom,
  configModal: inspectionTaskConfigModalAtom,
  columns: inspectionTaskColumnsAtom,
};
