import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  INTELLIGENTINSPECTION_TYPE_MAP,
  IS_ISNOT_STOPCHECK_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const inspectionPlanFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const inspectionPlanFnAtom = atom({
  refetch: () => {},
});

export const inspectionPlanEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const inspectionPlanConfigModalAtom = atom(false);

const inspectionPlanShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "巡检计划名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "巡检路径名称",
    dataIndex: "path",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "巡检类型",
    dataIndex: "inspectionType",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(INTELLIGENTINSPECTION_TYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "default"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "责任部门",
    dataIndex: "liableDepartment",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "是否停检",
    dataIndex: "isStopCheck",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_STOPCHECK_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "default"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const inspectionPlanExtendColumns = [
  // user-defined code here
];

export const inspectionPlanColumnsAtom = atom([
  ...inspectionPlanShowColumns,
  ...inspectionPlanExtendColumns,
]);

export const inspectionPlanAtoms: CommonAtoms = {
  entity: "InspectionPlan",
  filter: inspectionPlanFilterAtom,
  Fn: inspectionPlanFnAtom,
  editModal: inspectionPlanEditModalAtom,
  configModal: inspectionPlanConfigModalAtom,
  columns: inspectionPlanColumnsAtom,
};
