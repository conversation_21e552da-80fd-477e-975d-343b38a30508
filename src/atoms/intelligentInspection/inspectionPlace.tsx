import { Tag, Tooltip } from "@douyinfe/semi-ui";
import {
  ALLOW_ALLOWNOT_MAP,
  INTELLIGENTINSPECTION_SIGNIN_METHCOD_MAP,
  IS_ISNOT_STOPCHECK_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const inspectionPlaceFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const inspectionPlaceFnAtom = atom({
  refetch: () => {},
});

export const inspectionPlaceEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const inspectionPlaceEditStandardModalAtom = atomWithReset({
  standardId: "",
  name: "",
  newMinValue: NaN,
  newMaxValue: NaN,
  show: false,
});

export const inspectionPlaceConfigModalAtom = atom(false);

const inspectionPlaceShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "巡检点名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "所在区域",
    dataIndex: "area",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "签到方式",
    dataIndex: "checkMethod",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(
        INTELLIGENTINSPECTION_SIGNIN_METHCOD_MAP
      );
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "巡检项数量",
    dataIndex: "itemCount",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否停检",
    dataIndex: "isStopCheck",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_STOPCHECK_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否允许批量提交",
    dataIndex: "allowBatchSubmit",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(ALLOW_ALLOWNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const inspectionPlaceExtendColumns = [
  // user-defined code here
];

export const inspectionPlaceShowColumnsAtom = atom(inspectionPlaceShowColumns);

export const inspectionPlaceColumnsAtom = atom([
  ...inspectionPlaceShowColumns,
  ...inspectionPlaceExtendColumns,
]);

export const inspectionPlaceAtoms: CommonAtoms = {
  entity: "InspectionPlace",
  filter: inspectionPlaceFilterAtom,
  Fn: inspectionPlaceFnAtom,
  editModal: inspectionPlaceEditModalAtom,
  configModal: inspectionPlaceConfigModalAtom,
  columns: inspectionPlaceColumnsAtom,
};
