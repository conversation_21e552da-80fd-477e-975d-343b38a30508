import { TRAINING_RECORD_STATUS_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDateDay } from "utils";

export const coporateTrainingRecordFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const coporateTrainingRecordFnAtom = atom({
  refetch: () => {},
});

export const coporateTrainingRecordEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const coporateTrainingRecordConfigModalAtom = atom(false);

const coporateTrainingRecordShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "培训计划名称",
    dataIndex: "plan",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => item?.name ?? "",
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "培训类型",
    dataIndex: "trainingTypeValue",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => item?.dicValue ?? "",
    render: (r) => (
      <Tooltip content={r?.dicValue ?? ""}>
        <p>{r?.dicValue ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "学员名称",
    dataIndex: "person",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => item?.name ?? "",
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "部门/单位",
    dataIndex: "personUnit",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => item?.name ?? "",
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "培训开始时间",
    dataIndex: "trainingBeginTime",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => {
      const content = formatDateDay(item);
      return content;
    },
    render: (item) => {
      const content = formatDateDay(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "培训结束时间",
    dataIndex: "trainingEndTime",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => {
      const content = formatDateDay(item);
      return content;
    },
    render: (item) => {
      const content = formatDateDay(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "学习时长",
    dataIndex: "studyTime",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => {
      const hourContent = item.hour > 0 ? `${item.hour}小时` : "";
      const minuteContent = item.minute > 0 ? `${item.minute}分钟` : "";
      const secondContent = item.second > 0 ? `${item.second}秒` : "";
      const content = `${hourContent}${minuteContent}${secondContent}`;
      return content;
    },
    render: (item, record, index) => {
      const hourContent = item.hour > 0 ? `${item.hour}小时` : "";
      const minuteContent = item.minute > 0 ? `${item.minute}分钟` : "";
      const secondContent = item.second > 0 ? `${item.second}秒` : "";
      const content = `${hourContent}${minuteContent}${secondContent}`;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "是否通过",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => {
      const i = find(propEq(item, "id"))(TRAINING_RECORD_STATUS_MAP);
      return i?.name ?? "";
    },
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(TRAINING_RECORD_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "获得证书类型",
    dataIndex: "passCertificate",
    isShow: true,
    ellipsis: true,
    renderText: (item, record) => item?.certificateTypeValueName ?? "",
    render: (item, record, index) => (
      <Tooltip content={item?.certificateTypeValueName ?? ""}>
        <p>{item?.certificateTypeValueName ?? ""}</p>
      </Tooltip>
    ),
  },
];

const coporateTrainingRecordExtendColumns = [
  // user-defined code here
];

export const coporateTrainingRecordShowColumnsAtom = atom(
  ...coporateTrainingRecordShowColumns
);

export const coporateTrainingRecordColumnsAtom = atom([
  ...coporateTrainingRecordShowColumns,
  ...coporateTrainingRecordExtendColumns,
]);

/*export const coporateTrainingRecordColumnsAtom = atom(
  (get) => get(coporateTrainingRecordShowColumnsAtom).concat(get(coporateTrainingRecordExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(coporateTrainingRecordShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(coporateTrainingRecordExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const coporateTrainingRecordAtoms: CommonAtoms = {
  entity: "CoporateTrainingRecord",
  entityCName: "培训记录",
  filter: coporateTrainingRecordFilterAtom,
  Fn: coporateTrainingRecordFnAtom,
  editModal: coporateTrainingRecordEditModalAtom,
  configModal: coporateTrainingRecordConfigModalAtom,
  columns: coporateTrainingRecordColumnsAtom,
};
