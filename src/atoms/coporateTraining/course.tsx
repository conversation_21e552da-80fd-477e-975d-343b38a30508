import { IS_ISNOT_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const courseFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
  subjectId: null,
});

//TODO: to delete
export const courseFnAtom = atom({
  refetch: () => {},
});

export const courseEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const courseConfigModalAtom = atom(false);

const courseShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "课程名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "知识科目",
    dataIndex: "subject",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  /* {
    title: "科学编号",
    dataIndex: "",
    isShow: true,
    ellipsis: true,
  }, */
  {
    title: "课程讲师",
    dataIndex: "teachers",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const content = text
        ? text.map((item, index, array) =>
            index === array.length - 1 ? (
              <span key={index}>{item.name}</span>
            ) : (
              <span key={index}>{item.name},</span>
            )
          )
        : null;
      if (!content) return null;
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "课程积分(分)",
    dataIndex: "score",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否有考试",
    dataIndex: "hasExam",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "是否允许重考",
    dataIndex: "hasReexam",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

const courseExtendColumns = [
  // user-defined code here
];

export const courseColumnsAtom = atom([
  ...courseShowColumns,
  ...courseExtendColumns,
]);

/*export const courseColumnsAtom = atom(
  (get) => get(courseShowColumnsAtom).concat(get(courseExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(courseShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(courseExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const courseAtoms: CommonAtoms = {
  entity: "Course",
  filter: courseFilterAtom,
  Fn: courseFnAtom,
  editModal: courseEditModalAtom,
  configModal: courseConfigModalAtom,
  columns: courseColumnsAtom,
};
