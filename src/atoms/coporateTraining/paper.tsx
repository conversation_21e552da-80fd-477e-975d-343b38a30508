import { CT_PAPER_GENERATETYPE_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const paperFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
  subjectId: null,
});

//TODO: to delete
export const paperFnAtom = atom({
  refetch: () => {},
});

export const paperEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const paperConfigModalAtom = atom(false);

const paperShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "试卷名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "知识科目",
    dataIndex: "subject",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "出题方式",
    dataIndex: "generationMode",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(CT_PAPER_GENERATETYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "考试时长",
    dataIndex: "durationMinutes",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      return item + "分钟";
    },
  },
  {
    title: "试卷总分",
    dataIndex: "totalScore",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "及格分",
    dataIndex: "passScore",
    isShow: true,
    ellipsis: true,
  },
];

const paperExtendColumns = [
  // user-defined code here
];

export const paperColumnsAtom = atom([
  ...paperShowColumns,
  ...paperExtendColumns,
]);

/*export const paperColumnsAtom = atom(
  (get) => get(paperShowColumnsAtom).concat(get(paperExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(paperShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(paperExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const paperAtoms: CommonAtoms = {
  entity: "Paper",
  filter: paperFilterAtom,
  Fn: paperFnAtom,
  editModal: paperEditModalAtom,
  configModal: paperConfigModalAtom,
  columns: paperColumnsAtom,
};
