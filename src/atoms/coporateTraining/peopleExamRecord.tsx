import { TRAINING_EXAM_STATUS_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const coporateTrainingPeopleExamRecordFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const coporateTrainingPeopleExamRecordFnAtom = atom({
  refetch: () => {},
});

export const coporateTrainingPeopleExamRecordEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const coporateTrainingPeopleExamRecordConfigModalAtom = atom(false);

const coporateTrainingPeopleExamRecordShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "姓名",
    dataIndex: "person",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "部门/单位",
    dataIndex: "personUnit",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "培训计划名称",
    dataIndex: "plan",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "课程名称",
    dataIndex: "course",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "试卷名称",
    dataIndex: "paper",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item) => item?.name ?? "",
  },
  {
    title: "开考时间",
    dataIndex: "examBeginTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item),
  },
  {
    title: "交卷时间",
    dataIndex: "examEndTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item),
  },
  {
    title: "考试得分",
    dataIndex: "score",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "及格分",
    dataIndex: "passScore",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否通过",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(TRAINING_EXAM_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item) => {
      const i = find(propEq(item, "id"))(TRAINING_EXAM_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
];

const coporateTrainingPeopleExamRecordExtendColumns = [
  // user-defined code here
];

export const coporateTrainingPeopleExamRecordShowColumnsAtom = atom(
  ...coporateTrainingPeopleExamRecordShowColumns
);

export const coporateTrainingPeopleExamRecordColumnsAtom = atom([
  ...coporateTrainingPeopleExamRecordShowColumns,
  ...coporateTrainingPeopleExamRecordExtendColumns,
]);

/*export const coporateTrainingPeopleExamRecordColumnsAtom = atom(
  (get) => get(coporateTrainingPeopleExamRecordShowColumnsAtom).concat(get(coporateTrainingPeopleExamRecordExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(coporateTrainingPeopleExamRecordShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(coporateTrainingPeopleExamRecordExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const coporateTrainingPeopleExamRecordAtoms: CommonAtoms = {
  entity: "CoporateTrainingPeopleExamRecord",
  entityCName: "学员考试记录",
  filter: coporateTrainingPeopleExamRecordFilterAtom,
  Fn: coporateTrainingPeopleExamRecordFnAtom,
  editModal: coporateTrainingPeopleExamRecordEditModalAtom,
  configModal: coporateTrainingPeopleExamRecordConfigModalAtom,
  columns: coporateTrainingPeopleExamRecordColumnsAtom,
};
