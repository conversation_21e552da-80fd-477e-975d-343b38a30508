import {
  BASIC_GENDER_MAP,
  CT_TEACHER_TYPE_MAP,
  EDUCATION_MAP,
} from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { Tag, Tooltip } from "tdesign-react";
import { CommonAtoms } from "types";

export const teacherFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const teacherFnAtom = atom({
  refetch: () => {},
});

export const teacherEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const teacherConfigModalAtom = atom(false);

const teacherShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "讲师来源",
    dataIndex: "source",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(CT_TEACHER_TYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "讲师姓名",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "性别",
    dataIndex: "gender",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(BASIC_GENDER_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "学历",
    dataIndex: "education",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(EDUCATION_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "专业",
    dataIndex: "major",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "擅长方向",
    dataIndex: "goodAt",
    isShow: true,
    ellipsis: true,
  },
];

const teacherExtendColumns = [
  // user-defined code here
];

export const teacherColumnsAtom = atom([
  ...teacherShowColumns,
  ...teacherExtendColumns,
]);

/*export const teacherColumnsAtom = atom(
  (get) => get(teacherShowColumnsAtom).concat(get(teacherExtendColumnsAtom)),
  (get, set, newValue: any) => {
    set(teacherShowColumnsAtom, newValue.slice(0, newValue.length / 2)); // 更新 ShowColumnsAtom
    set(teacherExtendColumnsAtom, newValue.slice(newValue.length / 2)); // 更新 ExtendColumnsAtom
  }
)*/

export const teacherAtoms: CommonAtoms = {
  entity: "Teacher",
  filter: teacherFilterAtom,
  Fn: teacherFnAtom,
  editModal: teacherEditModalAtom,
  configModal: teacherConfigModalAtom,
  columns: teacherColumnsAtom,
};
