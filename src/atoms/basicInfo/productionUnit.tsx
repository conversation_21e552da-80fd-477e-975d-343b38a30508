import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { IS_ISNOT_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const productionUnitFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const productionUnitFnAtom = atom({
  refetch: () => {},
});

export const productionUnitEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const productionUnitConfigModalAtom = atom(false);

export const productionUnitColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "生产装置名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "生产装置型号",
    dataIndex: "model",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "是否关键装置",
    dataIndex: "isCritical",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(IS_ISNOT_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "所属重大危险源",
    dataIndex: "majorHazard",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "所属危险工艺",
    dataIndex: "dangerousProcess",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "装置位号",
    dataIndex: "positionNumber",
    isShow: true,
    ellipsis: true,
  },
]);

export const productionUnitAtoms: CommonAtoms = {
  entity: "ProductionUnit",
  filter: productionUnitFilterAtom,
  Fn: productionUnitFnAtom,
  editModal: productionUnitEditModalAtom,
  configModal: productionUnitConfigModalAtom,
  columns: productionUnitColumnsAtom,
};
