import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { FAIL_REASON_MAP, LOGIN_SOURCE_MAP, LOGINTYPE_MAP } from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";

export const loginRecordEditModal = atom({
  id: "",
  show: false,
});

// 查询条件
export const loginRecordFilterAtom = atomWithReset<any>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
});

// 查询条件
export const loginRecordFnAtom = atom({
  refetch: () => {},
});

export const loginRecordColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
  },
  {
    title: <Tooltip content="姓名">姓名</Tooltip>,
    dataIndex: "user.name",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="工号">工号</Tooltip>,
    dataIndex: "user",
    isShow: true,
    ellipsis: true,
    render: (item) => <p>{item.employeeId}</p>,
  },
  {
    title: <Tooltip content="登录结果">登录结果</Tooltip>,
    dataIndex: "isSuccess",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <p>
        {item === 1 ? (
          <Tag color="blue" type="light">
            成功
          </Tag>
        ) : (
          <Tag color="red" type="light">
            失败
          </Tag>
        )}
      </p>
    ),
  },
  {
    title: <Tooltip content="登录方式">登录账号类型</Tooltip>,
    dataIndex: "accountType",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return null;
      }
      const index = find(propEq(item || 1, "id"))(LOGINTYPE_MAP);
      return <Tag type="light">{index?.name ?? "-"}</Tag>;
    },
  },
  {
    title: <Tooltip content="登录来源">登录来源</Tooltip>,
    dataIndex: "source",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return null;
      }
      const index = find(propEq(item || 1, "id"))(LOGIN_SOURCE_MAP);
      return <Tag type="light">{index?.name ?? "-"}</Tag>;
    },
  },
  {
    title: <Tooltip content="登录IP">登录IP</Tooltip>,
    dataIndex: "ip",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="登录UA">登录UA</Tooltip>,
    dataIndex: "ua",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="登录APP版本">登录APP版本</Tooltip>,
    dataIndex: "appVersion",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="失败原因">失败原因</Tooltip>,
    dataIndex: "failReason",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      if (!item) {
        return null;
      }
      const index = find(propEq(item || 1, "id"))(FAIL_REASON_MAP);
      return (
        <Tag color="red" type="light">
          {index?.name ?? "-"}
        </Tag>
      );
    },
  },
  {
    title: <Tooltip content="登录时间">登录时间</Tooltip>,
    dataIndex: "loginTime",
    isShow: true,
    ellipsis: true,
    render: (t) => {
      const d = dayjs(t).format("YYYY-MM-DD HH:mm:ss");
      return <Tooltip content={d}>{d}</Tooltip>;
    },
  },
]);
