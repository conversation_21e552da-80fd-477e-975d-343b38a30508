import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { RIKSMANAGEMENT_STATUS_MAP } from "components";
import dayjs from "dayjs";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, isEmpty, propEq } from "ramda";
import { CommonAtoms } from "types";
import { formatDate } from "utils";

export const riskManagementFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const riskManagementFnAtom = atom({
  refetch: () => {},
});

export const riskManagementEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const riskManagementApproveModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const riskManagementRefModalAtom = atomWithReset({
  show: false,
});

export const riskManagementConfigModalAtom = atom(false);

const riskManagementShowColumns = [
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "负责人",
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: "承诺时间",
    dataIndex: "commitmentTime",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const content = formatDate(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
  },
  {
    title: "承诺内容",
    dataIndex: "commitment",
    isShow: true,
    ellipsis: true,
  },
];

const riskManagementExtendColumns = [
  {
    title: "审批人",
    dataIndex: "approval",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      if (!item?.name || isEmpty(item.name)) return null;
      const res =
        record.status !== RIKSMANAGEMENT_STATUS_MAP[0].id ? (
          <Tooltip content={item?.name ?? ""}>
            {record.status === RIKSMANAGEMENT_STATUS_MAP[1].id ? (
              <Tag color="green">{item?.name ?? ""}</Tag>
            ) : (
              <Tag color="red">{item?.name ?? ""}</Tag>
            )}
          </Tooltip>
        ) : null;
      return res;
    },
  },
  {
    title: "审批时间",
    dataIndex: "approveTime",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      const dateText = text ? dayjs(text).format("YYYY-MM-DD HH:mm:ss") : null;
      if (!dateText) return null;
      const res =
        record.status !== RIKSMANAGEMENT_STATUS_MAP[0].id ? (
          <p>
            {record.status === RIKSMANAGEMENT_STATUS_MAP[1].id ? (
              <Tag color="green">{dateText}</Tag>
            ) : (
              <Tag color="red">{dateText}</Tag>
            )}
          </p>
        ) : null;

      return res;
    },
  },
  {
    title: "审批意见",
    dataIndex: "note",
    isShow: true,
    ellipsis: true,
    render: (text, record, index) => {
      if (!text) return null;
      const res =
        record.status !== RIKSMANAGEMENT_STATUS_MAP[0].id ? (
          record.status === RIKSMANAGEMENT_STATUS_MAP[1].id ? (
            <Tag color="green">{text}</Tag>
          ) : (
            <Tag color="red">{text}</Tag>
          )
        ) : null;
      return res;
    },
  },
  {
    title: "状态",
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(RIKSMANAGEMENT_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
];

export const riskManagementColumnsAtom = atom([
  ...riskManagementShowColumns,
  ...riskManagementExtendColumns,
]);

export const riskManagementAtoms: CommonAtoms = {
  entity: "RiskManagement",
  filter: riskManagementFilterAtom,
  Fn: riskManagementFnAtom,
  editModal: riskManagementEditModalAtom,
  configModal: riskManagementConfigModalAtom,
  columns: riskManagementColumnsAtom,
};
