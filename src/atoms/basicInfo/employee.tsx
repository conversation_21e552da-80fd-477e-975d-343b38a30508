import { Tag, Tooltip } from "@douyinfe/semi-ui";
import type { EmployeeParams } from "api/basicInfo";
import { LOGIN_STATUS_MAP, STATUS_MAP } from "components";
import { JOB_REPORT_STATUS_MAP } from "components/enum/specialWork";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { formatDate, formatDateDay } from "../../utils";

export const employeeEditModal = atom({
  id: "",
  show: false,
});

export enum ActionType {
  Active,
  Reset,
  BatchActive,
}

export const userActiveOrResetModalAtom = atom({
  id: "",
  ids: [],
  type: ActionType,
  show: false,
});

// 查询条件
export const employeeFilterAtom = atomWithReset<EmployeeParams>({
  pageNumber: 1,
  pageSize: 10,
  filter: {},
  departmentId: null,
});

// 查询条件
export const employeeFnAtom = atom({
  refetch: () => {},
});

export const employeePickerColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  {
    title: <Tooltip content="工号">工号</Tooltip>,
    dataIndex: "employeeId",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="姓名">姓名</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="部门">部门</Tooltip>,
    dataIndex: "department",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
  },
  {
    title: <Tooltip content="手机号">手机号</Tooltip>,
    dataIndex: "mobile",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
  },
  {
    title: "邮箱",
    dataIndex: "email",
    isShow: true,
    ellipsis: true,
  },
]);

export const employeeColumnsAtom = atom([
  {
    title: <Tooltip content="ID">ID</Tooltip>,
    dataIndex: "id",
    isShow: true,
    fixed: true,
    width: 80,
    header: "ID",
  },
  {
    title: <Tooltip content="姓名">姓名</Tooltip>,
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
    fixed: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
    header: "姓名",
  },
  {
    title: <Tooltip content="性别">性别</Tooltip>,
    dataIndex: "gender",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <p>
        {item === 1 ? (
          <Tag color="blue" type="light">
            男
          </Tag>
        ) : (
          <Tag color="red" type="light">
            女
          </Tag>
        )}
      </p>
    ),
    header: "性别",
    renderText: (item) => (item === 1 ? "男" : "女"),
  },
  {
    title: <Tooltip content="部门">部门</Tooltip>,
    dataIndex: "department",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    header: "部门",
    renderText: (item) => item.name,
  },
  {
    title: <Tooltip content="岗位">岗位</Tooltip>,
    dataIndex: "position",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    header: "岗位",
    renderText: (item) => item.name,
  },
  {
    title: <Tooltip content="角色">角色</Tooltip>,
    dataIndex: "role",
    isShow: true,
    ellipsis: true,
    render: (item) => (
      <Tooltip content={item?.name ?? ""}>
        <p>{item?.name ?? ""}</p>
      </Tooltip>
    ),
    header: "角色",
    renderText: (item) => item.name,
  },
  {
    title: <Tooltip content="工号">工号</Tooltip>,
    dataIndex: "employeeId",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
    header: "工号",
  },
  {
    title: <Tooltip content="手机号">手机号</Tooltip>,
    dataIndex: "mobile",
    isShow: true,
    ellipsis: true,
    render: (text) => (
      <Tooltip content={text}>
        <span className="block truncate">{text}</span>
      </Tooltip>
    ),
    header: "手机号",
  },
  {
    title: <Tooltip content="人员定位卡">人员定位卡</Tooltip>,
    dataIndex: "locationMac",
    isShow: true,
    ellipsis: true,
    header: "人员定位卡",
  },
  {
    title: <Tooltip content="在职状态">在职状态</Tooltip>,
    dataIndex: "status",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    header: "在职状态",
    renderText: (item) => {
      const i = find(propEq(item, "id"))(STATUS_MAP);
      return i?.name;
    },
  },
  {
    title: <Tooltip content="账户状态">账户状态</Tooltip>,
    dataIndex: "loginStatus",
    isShow: true,
    ellipsis: true,
    render: (item) => {
      const i = find(propEq(item, "id"))(LOGIN_STATUS_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    header: "账户状态",
    renderText: (item) => {
      const i = find(propEq(item ? item : 1, "id"))(LOGIN_STATUS_MAP);
      return i?.name ?? "-";
    },
  },
  {
    title: <Tooltip content="上报状态">上报状态</Tooltip>,
    dataIndex: "reportStatus",
    isShow: false,
    ellipsis: true,
    header: "上报状态",
    render: (item: number) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP) as
        | { id: number; name: string; color: string }
        | undefined;
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color ?? "-"} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
    renderText: (item: number) => {
      const i = find(propEq(item, "id"))(JOB_REPORT_STATUS_MAP) as
        | { id: number; name: string; color: string }
        | undefined;
      return i?.name ?? "-";
    },
  },
  {
    title: <Tooltip content="上报时间">上报时间</Tooltip>,
    dataIndex: "reportTime",
    isShow: false,
    ellipsis: true,
    header: "上报时间",
    render: (item) => {
      const content = formatDateDay(item);
      if (!content) {
        return null;
      }
      return (
        <Tooltip content={content}>
          <p>{content}</p>
        </Tooltip>
      );
    },
    renderText: (item) => formatDate(item) ?? "-",
  },
  {
    title: <Tooltip content="上报结果">上报结果</Tooltip>,
    dataIndex: "reportResult",
    isShow: false,
    ellipsis: true,
    header: "上报结果",
    render: (item, record, index) => (
      <Tooltip content={item ?? ""}>
        <p>{item ?? ""}</p>
      </Tooltip>
    ),
    renderText: (item, record, index) => item ?? "-",
  },
]);
