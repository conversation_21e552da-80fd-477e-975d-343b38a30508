import { Tag, Tooltip } from "@douyinfe/semi-ui";
import { INTERLOCK_RUNNINGTYPE_MAP, INTERLOCK_STATE_MAP } from "components";
import { atom } from "jotai";
import { atomWithReset } from "jotai/utils";
import { find, propEq } from "ramda";
import { CommonAtoms } from "types";

export const interlockFilterAtom = atomWithReset({
  pageNumber: 1,
  pageSize: 10,
});

//TODO: to delete
export const interlockFnAtom = atom({
  refetch: () => {},
});

export const interlockEditModalAtom = atomWithReset({
  id: "",
  show: false,
});

export const interlockConfigModalAtom = atom(false);

export const interlockColumnsAtom = atom([
  {
    title: "ID",
    dataIndex: "id",
    isShow: true,
    width: 80,
  },
  // user-defined code here
  {
    title: "联锁程序名称",
    dataIndex: "name",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "联锁状态",
    dataIndex: "interlockState",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item, "id"))(INTERLOCK_STATE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "投入状态",
    dataIndex: "isRunning",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => {
      const i = find(propEq(item ? item : 1, "id"))(INTERLOCK_RUNNINGTYPE_MAP);
      return (
        <Tooltip content={i?.name ?? "-"}>
          <Tag color={i?.color} type="light">
            {i?.name ?? "-"}
          </Tag>
        </Tooltip>
      );
    },
  },
  {
    title: "负责人",
    dataIndex: "liablePerson",
    isShow: true,
    ellipsis: true,
    render: (item, record, index) => (
      <Tooltip content={item?.name ?? "-"}>
        <p>{item?.name ?? "-"}</p>
      </Tooltip>
    ),
  },
  {
    title: "监控项目",
    dataIndex: "monitorItem",
    isShow: true,
    ellipsis: true,
  },
  {
    title: "设备位号",
    dataIndex: "positionNumber",
    isShow: true,
    ellipsis: true,
  },
]);

export const interlockAtoms: CommonAtoms = {
  entity: "Interlock",
  filter: interlockFilterAtom,
  Fn: interlockFnAtom,
  editModal: interlockEditModalAtom,
  configModal: interlockConfigModalAtom,
  columns: interlockColumnsAtom,
};
