import { <PERSON><PERSON>, Spin, Toast } from "@douyinfe/semi-ui";
import { useAuth, useLocalStorage } from "@reactivers/hooks";
import { useMutation } from "@tanstack/react-query";
import { postLandrayLogin } from "api/sso";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { userInfoNameInLocalStorage } from "utils/constants";
import { LoginRoute } from "utils/routerConstants";
import { extractUUIDFromURL, validateUUID } from "utils/ssoUtils";

// Error types for better error handling
enum SSOErrorType {
  MISSING_UUID = "MISSING_UUID",
  INVALID_UUID = "INVALID_UUID",
  API_ERROR = "API_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
}

interface SSOError {
  type: SSOErrorType;
  message: string;
  details?: any;
}

export const LandrayOASSOCallback = () => {
  console.log("[LandrayOASSOCallback] === 组件初始化 ===");
  console.log("[LandrayOASSOCallback] 初始化时间:", new Date().toISOString());
  console.log("[LandrayOASSOCallback] 当前URL:", window.location.href);

  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [error, setError] = useState<SSOError | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const { login, setToken } = useAuth();
  const { setItemWithKey } = useLocalStorage();

  // Extract UUID from URL
  const currentUrl = window.location.href;
  console.debug("[LandrayOASSOCallback] Current URL:", currentUrl);
  console.debug(
    "[LandrayOASSOCallback] Search params:",
    Object.fromEntries(searchParams.entries())
  );

  const uuid = extractUUIDFromURL(currentUrl);
  console.debug("[LandrayOASSOCallback] Extracted UUID:", uuid);

  const mutation = useMutation({
    mutationFn: postLandrayLogin,
    onSuccess: (res) => {
      console.log("[LandrayOASSOCallback] === API 响应成功 ===");
      console.log("[LandrayOASSOCallback] 响应时间:", new Date().toISOString());
      console.log("[LandrayOASSOCallback] 完整响应:", res);
      console.log("[LandrayOASSOCallback] 响应码:", res?.code);

      if (res?.code === 0) {
        console.log(
          "[LandrayOASSOCallback] === 登录成功，开始处理用户数据 ==="
        );

        const options = {
          content: `登录成功!`,
          duration: 2,
        };
        const temporary = res.data;

        console.debug("[LandrayOASSOCallback] User data to store:", {
          username: temporary.employee.name,
          employeeId: temporary.employee.id,
          hasToken: !!temporary.authToken,
          hasRefreshToken: !!temporary.refreshToken,
          expireTime: temporary.expireTime,
        });

        setItemWithKey(userInfoNameInLocalStorage, {
          username: temporary.employee.name,
          token: temporary.authToken,
          userInfo: temporary.employee,
          refreshToken: temporary.refreshToken,
          expireTime: temporary.expireTime,
        });

        login(temporary);
        setToken(temporary.authToken);
        Toast.success(options);

        console.debug(
          "[LandrayOASSOCallback] Authentication successful, Auth component will handle redirect"
        );

        // 添加详细日志来诊断重定向问题
        console.log("[LandrayOASSOCallback] === 开始重定向诊断 ===");
        console.log(
          "[LandrayOASSOCallback] 当前时间:",
          new Date().toISOString()
        );
        console.log("[LandrayOASSOCallback] 当前URL:", window.location.href);
        console.log(
          "[LandrayOASSOCallback] 当前pathname:",
          window.location.pathname
        );
        console.log("[LandrayOASSOCallback] localStorage内容:");
        console.log(
          "  - userInfo:",
          localStorage.getItem(userInfoNameInLocalStorage)
        );
        console.log("  - vr-last-path:", localStorage.getItem("vr-last-path"));
        console.log("  - 所有localStorage keys:", Object.keys(localStorage));

        setTimeout(() => {
          console.log("[LandrayOASSOCallback] === setTimeout 执行开始 ===");
          console.log("[LandrayOASSOCallback] setTimeout 延迟2秒后执行");
          console.log(
            "[LandrayOASSOCallback] 当前时间:",
            new Date().toISOString()
          );

          // 检查组件是否还存在
          console.log("[LandrayOASSOCallback] 检查DOM状态:");
          console.log("  - document.body存在:", !!document.body);
          console.log("  - 当前页面title:", document.title);
          console.log("  - 当前页面URL:", window.location.href);

          // 检查认证状态
          const currentUserInfo = localStorage.getItem(
            userInfoNameInLocalStorage
          );
          console.log("[LandrayOASSOCallback] 当前用户信息:", currentUserInfo);

          // 计算重定向路径
          const lastPathValue = localStorage.getItem("vr-last-path");
          const redirectPath =
            lastPathValue && lastPathValue !== "/auth/landray/callback"
              ? lastPathValue
              : "/";

          console.log("[LandrayOASSOCallback] 重定向路径计算:");
          console.log("  - lastPathValue:", lastPathValue);
          console.log("  - 计算出的redirectPath:", redirectPath);
          console.log(
            "  - 是否会使用lastPath:",
            lastPathValue && lastPathValue !== "/auth/landray/callback"
          );

          // 暂时不执行跳转，只打印日志
          console.log(
            "[LandrayOASSOCallback] === 本应执行跳转，但暂时跳过 ==="
          );
          console.log("[LandrayOASSOCallback] 本应跳转到:", redirectPath);
          console.log("[LandrayOASSOCallback] === setTimeout 执行结束 ===");

          // TODO: 取消注释下面这行来恢复跳转功能
          // window.location.replace(redirectPath);
        }, 2000); // 给足够时间让用户看到成功提示
      } else {
        console.debug(
          "[LandrayOASSOCallback] Login failed with code:",
          res?.code,
          "message:",
          res?.message
        );

        // Handle API error response - show backend error message
        const apiError: SSOError = {
          type: SSOErrorType.API_ERROR,
          message: res?.message ?? "认证失败，请重试",
          details: { code: res?.code, response: res },
        };
        setError(apiError);
        setIsProcessing(false); // 重置处理状态

        // Also show Toast error for immediate feedback
        Toast.error({
          content: apiError.message,
          duration: 5,
        });
      }
    },
    onError: (error: any) => {
      console.log("[LandrayOASSOCallback] === API 调用失败 ===");
      console.log("[LandrayOASSOCallback] 错误时间:", new Date().toISOString());
      console.log("[LandrayOASSOCallback] 错误对象:", error);
      console.log("[LandrayOASSOCallback] 错误详情:", {
        message: error?.message,
        status: error?.status,
        response: error?.response?.data,
        stack: error?.stack,
      });

      // Handle network error - show generic network error message
      const networkError: SSOError = {
        type: SSOErrorType.NETWORK_ERROR,
        message: "网络错误，请检查网络连接后重试",
        details: error,
      };
      setError(networkError);
      setIsProcessing(false); // 重置处理状态

      // Also show Toast error for immediate feedback
      Toast.error({
        content: networkError.message,
        duration: 5,
      });
    },
  });

  useEffect(() => {
    console.log("[LandrayOASSOCallback] === useEffect 触发 ===");
    console.log(
      "[LandrayOASSOCallback] useEffect 时间:",
      new Date().toISOString()
    );
    console.log("[LandrayOASSOCallback] UUID:", uuid);
    console.log("[LandrayOASSOCallback] isProcessing:", isProcessing);
    console.log(
      "[LandrayOASSOCallback] mutation.isLoading:",
      mutation.isLoading
    );

    // 防止重复处理
    if (isProcessing) {
      console.log("[LandrayOASSOCallback] 已在处理中，跳过此次执行");
      return;
    }

    // Handle missing UUID - show "invalid SSO request" error
    if (!uuid) {
      console.debug("[LandrayOASSOCallback] UUID not found in URL");
      const missingUuidError: SSOError = {
        type: SSOErrorType.MISSING_UUID,
        message: "无效的SSO请求：缺少必要的认证参数",
        details: { url: window.location.href },
      };
      setError(missingUuidError);

      // Show Toast error for immediate feedback
      Toast.error({
        content: missingUuidError.message,
        duration: 5,
      });
      return;
    }

    // Validate UUID format
    const isValidUUID = validateUUID(uuid);
    console.debug(
      "[LandrayOASSOCallback] UUID validation result:",
      isValidUUID
    );

    if (!isValidUUID) {
      console.debug("[LandrayOASSOCallback] UUID format validation failed");
      const invalidUuidError: SSOError = {
        type: SSOErrorType.INVALID_UUID,
        message: "无效的SSO请求：认证参数格式错误",
        details: { uuid },
      };
      setError(invalidUuidError);

      // Show Toast error for immediate feedback
      Toast.error({
        content: invalidUuidError.message,
        duration: 5,
      });
      return;
    }

    console.log("[LandrayOASSOCallback] === 准备调用登录API ===");
    console.log("[LandrayOASSOCallback] UUID:", uuid);
    console.log("[LandrayOASSOCallback] 调用时间:", new Date().toISOString());

    // 设置处理状态，防止重复调用
    setIsProcessing(true);
    console.log("[LandrayOASSOCallback] 设置 isProcessing = true");

    // Call the login API with UUID
    console.log("[LandrayOASSOCallback] 开始调用 mutation.mutate");
    mutation.mutate({ uuid });
  }, [uuid, isProcessing]);

  // Enhanced error handling with different error types
  if (error) {
    console.debug("[LandrayOASSOCallback] Rendering error state:", error);

    // Get error-specific content
    const getErrorContent = (error: SSOError) => {
      switch (error.type) {
        case SSOErrorType.MISSING_UUID:
          return {
            title: "无效的SSO请求",
            description: "缺少必要的认证参数，请从蓝凌OA系统重新访问。",
            showRetry: false,
          };
        case SSOErrorType.INVALID_UUID:
          return {
            title: "无效的SSO请求",
            description: "认证参数格式错误，请从蓝凌OA系统重新访问。",
            showRetry: false,
          };
        case SSOErrorType.API_ERROR:
          return {
            title: "认证失败",
            description: error.message,
            showRetry: true,
          };
        case SSOErrorType.NETWORK_ERROR:
          return {
            title: "网络连接错误",
            description: error.message,
            showRetry: true,
          };
        default:
          return {
            title: "SSO认证失败",
            description: error.message,
            showRetry: true,
          };
      }
    };

    const errorContent = getErrorContent(error);

    return (
      <div className="flex flex-col justify-center items-center w-full h-screen bg-[#ecf5ff]">
        <div className="bg-semi-color-bg-0 rounded-[8px] shadow-md p-8 max-w-md text-center">
          <div className="text-semi-color-danger text-lg font-medium mb-4">
            {errorContent.title}
          </div>
          <div className="text-semi-color-text-1 mb-6">
            {errorContent.description}
          </div>

          <div className="flex flex-col gap-3">
            {/* Always provide option to return to login page */}
            <Button
              theme="solid"
              type="primary"
              onClick={() => {
                console.debug(
                  "[LandrayOASSOCallback] Navigating back to login page"
                );
                navigate(LoginRoute, { replace: true });
              }}
            >
              返回登录页面
            </Button>

            {/* Show retry option for recoverable errors */}
            {errorContent.showRetry && uuid && (
              <Button
                theme="borderless"
                type="secondary"
                onClick={() => {
                  console.debug(
                    "[LandrayOASSOCallback] Retrying SSO authentication"
                  );
                  setError(null);
                  setIsProcessing(false); // 重置处理状态
                  mutation.mutate({ uuid });
                }}
                loading={mutation.isLoading}
              >
                重试认证
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show loading state
  console.log("[LandrayOASSOCallback] === 渲染加载状态 ===");
  console.log("[LandrayOASSOCallback] mutation.isLoading:", mutation.isLoading);
  console.log("[LandrayOASSOCallback] isProcessing:", isProcessing);
  console.log("[LandrayOASSOCallback] error:", error);
  console.log("[LandrayOASSOCallback] 渲染时间:", new Date().toISOString());

  return (
    <div className="flex justify-center items-center w-full h-screen bg-[#ecf5ff]">
      <Spin tip="蓝凌OA认证登录中" size="large">
        <div className="w-20 h-10"></div>
      </Spin>
    </div>
  );
};
