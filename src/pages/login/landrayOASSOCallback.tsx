import { <PERSON><PERSON>, Spin, Toast } from "@douyinfe/semi-ui";
import { useAuth, useLocalStorage } from "@reactivers/hooks";
import { useMutation } from "@tanstack/react-query";
import { postLandrayLogin } from "api/sso";
import { useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { userInfoNameInLocalStorage } from "utils/constants";
import { LoginRoute } from "utils/routerConstants";
import { extractUUIDFromURL, validateUUID } from "utils/ssoUtils";

// Error types for better error handling
enum SSOErrorType {
  MISSING_UUID = "MISSING_UUID",
  INVALID_UUID = "INVALID_UUID",
  API_ERROR = "API_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
}

interface SSOError {
  type: SSOErrorType;
  message: string;
  details?: any;
}

export const LandrayOASSOCallback = () => {
  console.debug("[LandrayOASSOCallback] Component initialized");

  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [error, setError] = useState<SSOError | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  const { login, setToken } = useAuth();
  const { setItemWithKey } = useLocalStorage();

  // Extract UUID from URL
  const currentUrl = window.location.href;
  console.debug("[LandrayOASSOCallback] Current URL:", currentUrl);
  console.debug(
    "[LandrayOASSOCallback] Search params:",
    Object.fromEntries(searchParams.entries())
  );

  const uuid = extractUUIDFromURL(currentUrl);
  console.debug("[LandrayOASSOCallback] Extracted UUID:", uuid);

  const mutation = useMutation({
    mutationFn: postLandrayLogin,
    onSuccess: (res) => {
      console.debug("[LandrayOASSOCallback] API response received:", res);

      if (res?.code === 0) {
        console.debug(
          "[LandrayOASSOCallback] Login successful, processing user data"
        );

        const options = {
          content: `登录成功!`,
          duration: 2,
        };
        const temporary = res.data;

        console.debug("[LandrayOASSOCallback] User data to store:", {
          username: temporary.employee.name,
          employeeId: temporary.employee.id,
          hasToken: !!temporary.authToken,
          hasRefreshToken: !!temporary.refreshToken,
          expireTime: temporary.expireTime,
        });

        setItemWithKey(userInfoNameInLocalStorage, {
          username: temporary.employee.name,
          token: temporary.authToken,
          userInfo: temporary.employee,
          refreshToken: temporary.refreshToken,
          expireTime: temporary.expireTime,
        });

        // 在调用 login 之前，先清除 lastPath 防止 Auth 组件跳转回回调页面
        console.log("[LandrayOASSOCallback] 清除 lastPath 防止跳转回回调页面");
        localStorage.removeItem("vr-last-path");

        login(temporary);
        setToken(temporary.authToken);
        Toast.success(options);

        console.debug(
          "[LandrayOASSOCallback] Authentication successful, Auth component will handle redirect"
        );

        // 由于已经清除了 lastPath，Auth 组件的 onLogin 会跳转到首页
        // 我们不需要手动跳转，让 Auth 组件处理即可
        console.log("[LandrayOASSOCallback] 让 Auth 组件处理重定向到首页");
      } else {
        console.debug(
          "[LandrayOASSOCallback] Login failed with code:",
          res?.code,
          "message:",
          res?.message
        );

        // Handle API error response - show backend error message
        const apiError: SSOError = {
          type: SSOErrorType.API_ERROR,
          message: res?.message ?? "认证失败，请重试",
          details: { code: res?.code, response: res },
        };
        setError(apiError);
        setIsProcessing(false); // 重置处理状态

        // Also show Toast error for immediate feedback
        Toast.error({
          content: apiError.message,
          duration: 5,
        });
      }
    },
    onError: (error: any) => {
      console.error("[LandrayOASSOCallback] API call failed:", error);
      console.debug("[LandrayOASSOCallback] Error details:", {
        message: error?.message,
        status: error?.status,
        response: error?.response?.data,
      });

      // Handle network error - show generic network error message
      const networkError: SSOError = {
        type: SSOErrorType.NETWORK_ERROR,
        message: "网络错误，请检查网络连接后重试",
        details: error,
      };
      setError(networkError);
      setIsProcessing(false); // 重置处理状态

      // Also show Toast error for immediate feedback
      Toast.error({
        content: networkError.message,
        duration: 5,
      });
    },
  });

  useEffect(() => {
    console.debug("[LandrayOASSOCallback] useEffect triggered, UUID:", uuid);

    // 防止重复处理
    if (isProcessing) {
      console.debug("[LandrayOASSOCallback] Already processing, skipping");
      return;
    }

    // Handle missing UUID - show "invalid SSO request" error
    if (!uuid) {
      console.debug("[LandrayOASSOCallback] UUID not found in URL");
      const missingUuidError: SSOError = {
        type: SSOErrorType.MISSING_UUID,
        message: "无效的SSO请求：缺少必要的认证参数",
        details: { url: window.location.href },
      };
      setError(missingUuidError);

      // Show Toast error for immediate feedback
      Toast.error({
        content: missingUuidError.message,
        duration: 5,
      });
      return;
    }

    // Validate UUID format
    const isValidUUID = validateUUID(uuid);
    console.debug(
      "[LandrayOASSOCallback] UUID validation result:",
      isValidUUID
    );

    if (!isValidUUID) {
      console.debug("[LandrayOASSOCallback] UUID format validation failed");
      const invalidUuidError: SSOError = {
        type: SSOErrorType.INVALID_UUID,
        message: "无效的SSO请求：认证参数格式错误",
        details: { uuid },
      };
      setError(invalidUuidError);

      // Show Toast error for immediate feedback
      Toast.error({
        content: invalidUuidError.message,
        duration: 5,
      });
      return;
    }

    console.debug("[LandrayOASSOCallback] Calling login API with UUID:", uuid);
    // 设置处理状态，防止重复调用
    setIsProcessing(true);
    // Call the login API with UUID
    mutation.mutate({ uuid });
  }, [uuid, isProcessing]);

  // Enhanced error handling with different error types
  if (error) {
    console.debug("[LandrayOASSOCallback] Rendering error state:", error);

    // Get error-specific content
    const getErrorContent = (error: SSOError) => {
      switch (error.type) {
        case SSOErrorType.MISSING_UUID:
          return {
            title: "无效的SSO请求",
            description: "缺少必要的认证参数，请从蓝凌OA系统重新访问。",
            showRetry: false,
          };
        case SSOErrorType.INVALID_UUID:
          return {
            title: "无效的SSO请求",
            description: "认证参数格式错误，请从蓝凌OA系统重新访问。",
            showRetry: false,
          };
        case SSOErrorType.API_ERROR:
          return {
            title: "认证失败",
            description: error.message,
            showRetry: true,
          };
        case SSOErrorType.NETWORK_ERROR:
          return {
            title: "网络连接错误",
            description: error.message,
            showRetry: true,
          };
        default:
          return {
            title: "SSO认证失败",
            description: error.message,
            showRetry: true,
          };
      }
    };

    const errorContent = getErrorContent(error);

    return (
      <div className="flex flex-col justify-center items-center w-full h-screen bg-[#ecf5ff]">
        <div className="bg-semi-color-bg-0 rounded-[8px] shadow-md p-8 max-w-md text-center">
          <div className="text-semi-color-danger text-lg font-medium mb-4">
            {errorContent.title}
          </div>
          <div className="text-semi-color-text-1 mb-6">
            {errorContent.description}
          </div>

          <div className="flex flex-col gap-3">
            {/* Always provide option to return to login page */}
            <Button
              theme="solid"
              type="primary"
              onClick={() => {
                console.debug(
                  "[LandrayOASSOCallback] Navigating back to login page"
                );
                navigate(LoginRoute, { replace: true });
              }}
            >
              返回登录页面
            </Button>

            {/* Show retry option for recoverable errors */}
            {errorContent.showRetry && uuid && (
              <Button
                theme="borderless"
                type="secondary"
                onClick={() => {
                  console.debug(
                    "[LandrayOASSOCallback] Retrying SSO authentication"
                  );
                  setError(null);
                  setIsProcessing(false); // 重置处理状态
                  mutation.mutate({ uuid });
                }}
                loading={mutation.isLoading}
              >
                重试认证
              </Button>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Show loading state
  console.log("[LandrayOASSOCallback] === 渲染加载状态 ===");
  console.log("[LandrayOASSOCallback] mutation.isLoading:", mutation.isLoading);
  console.log("[LandrayOASSOCallback] isProcessing:", isProcessing);
  console.log("[LandrayOASSOCallback] error:", error);
  console.log("[LandrayOASSOCallback] 渲染时间:", new Date().toISOString());

  return (
    <div className="flex justify-center items-center w-full h-screen bg-[#ecf5ff]">
      <Spin tip="蓝凌OA认证登录中" size="large">
        <div className="w-20 h-10"></div>
      </Spin>
    </div>
  );
};
