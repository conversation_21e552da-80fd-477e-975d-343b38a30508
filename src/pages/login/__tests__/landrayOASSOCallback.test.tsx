import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen, waitFor } from "@testing-library/react";
import * as ssoApi from "api/sso";
import { BrowserRouter } from "react-router-dom";
import { LandrayOASSOCallbackRoute } from "utils/routerConstants";
import * as ssoUtils from "utils/ssoUtils";
import { vi } from "vitest";
import { LandrayOASSOCallback } from "../landrayOASSOCallback";

// Mock dependencies
vi.mock("@reactivers/hooks", () => ({
  useAuth: () => ({
    login: vi.fn(),
    setToken: vi.fn(),
  }),
  useLocalStorage: () => ({
    setItemWithKey: vi.fn(),
  }),
}));

vi.mock("utils/ssoUtils");
vi.mock("api/sso");

const mockNavigate = vi.fn();
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useSearchParams: () => [new URLSearchParams()],
  };
});

const renderComponent = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return render(
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <LandrayOASSOCallback />
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe("LandrayOASSOCallback Error Handling", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock window.location.href
    Object.defineProperty(window, "location", {
      value: {
        href: `http://localhost:3000${LandrayOASSOCallbackRoute}?uuid=test-uuid`,
      },
      writable: true,
    });
  });

  it("should handle missing UUID error", async () => {
    // Mock extractUUIDFromURL to return null (missing UUID)
    vi.mocked(ssoUtils.extractUUIDFromURL).mockReturnValue(null);

    renderComponent();

    await waitFor(() => {
      expect(screen.getByText("无效的SSO请求")).toBeInTheDocument();
      expect(
        screen.getByText("缺少必要的认证参数，请从蓝凌OA系统重新访问。")
      ).toBeInTheDocument();
      expect(screen.getByText("返回登录页面")).toBeInTheDocument();
      // Should not show retry button for missing UUID
      expect(screen.queryByText("重试认证")).not.toBeInTheDocument();
    });
  });

  it("should handle invalid UUID format error", async () => {
    // Mock extractUUIDFromURL to return invalid UUID
    vi.mocked(ssoUtils.extractUUIDFromURL).mockReturnValue("invalid-uuid");
    vi.mocked(ssoUtils.validateUUID).mockReturnValue(false);

    renderComponent();

    await waitFor(() => {
      expect(screen.getByText("无效的SSO请求")).toBeInTheDocument();
      expect(
        screen.getByText("认证参数格式错误，请从蓝凌OA系统重新访问。")
      ).toBeInTheDocument();
      expect(screen.getByText("返回登录页面")).toBeInTheDocument();
      // Should not show retry button for invalid UUID format
      expect(screen.queryByText("重试认证")).not.toBeInTheDocument();
    });
  });

  it("should show loading state initially with valid UUID", async () => {
    // Mock valid UUID
    vi.mocked(ssoUtils.extractUUIDFromURL).mockReturnValue(
      "550e8400-e29b-41d4-a716-************"
    );
    vi.mocked(ssoUtils.validateUUID).mockReturnValue(true);

    // Mock API to never resolve (simulate loading)
    vi.mocked(ssoApi.postLandrayLogin).mockImplementation(
      () => new Promise(() => {})
    );

    renderComponent();

    expect(screen.getByText("蓝凌OA认证登录中")).toBeInTheDocument();
  });
});
