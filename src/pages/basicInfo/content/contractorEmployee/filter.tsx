import { IconSearch } from "@douyinfe/semi-icons";
import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getContractorList } from "api";
import { contractorEmployeeAtoms } from "atoms/basicInfo";
import {
  BIM_CONTRACTOR_ISBLACK_MAP,
  ContractorSearch,
  JOB_REPORT_STATUS_MAP,
} from "components";
import { useFilterSearch } from "hooks";
import { useMemo } from "react";

export const ContractorEmployeeFilter = () => {
  const atoms = contractorEmployeeAtoms;
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  const entity = atoms.entity;
  const operation = "List";

  const { data: list } = useQuery({
    queryKey: [`getContractorList`],
    queryFn: () => getContractorList,
  });
  const options = useMemo(() => {
    return list?.data?.results ?? [];
  }, [list]);

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          {/* <RestSelect
            options={options}
            field="contractorId"
            placeholder="请选择承包商"
            noLabel
          /> */}
          <Form.Input
            noLabel
            field="query"
            placeholder="请填入姓名/手机号"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <ContractorSearch
            field="contractorId"
            placeholder="请选择承包商"
            noLabel
          />
          <Form.Select
            placeholder="是否黑名单"
            field="isBlack"
            noLabel
            className="w-full"
          >
            {BIM_CONTRACTOR_ISBLACK_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>
          <Form.Select
            placeholder="上报状态"
            field="reportStatus"
            noLabel
            className="w-full"
          >
            {JOB_REPORT_STATUS_MAP.map((item) => (
              <Form.Select.Option key={item.id} value={item.id}>
                {item.name}
              </Form.Select.Option>
            ))}
          </Form.Select>

          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
