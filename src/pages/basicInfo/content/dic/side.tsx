import { IconPlus, IconRefresh, IconSetting } from "@douyinfe/semi-icons";
import {
  Button,
  ButtonGroup,
  Popconfirm,
  SideSheet,
  Switch,
  Table,
  Toast,
  Tooltip,
} from "@douyinfe/semi-ui";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  delDicItemValue,
  delDicItemValues,
  getDicItemValueList,
  updateDicItemValue,
} from "api";
import {
  dicItemValuesEditModal,
  dicModal,
  dicValueColumnsAtom,
  dicValueConfigModalAtom,
  dicValueFilterAtom,
  dicValueFnAtom,
} from "atoms/basicInfo";
import { TableConfig } from "components";
import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { useCallback, useEffect, useMemo, useState } from "react";
import { SideFilter } from "./sideFilter";

interface DicSideProps {
  itemId?: number;
}

export const DicSide = ({ itemId }: DicSideProps) => {
  const queryClient = useQueryClient();
  const [rows, setRows] = useState<number[]>([]);
  const [dicValueFilter, setdicValueFilter] = useAtom(dicValueFilterAtom);
  const [dicValueFn, setdicValueFn] = useAtom(dicValueFnAtom);
  const [dic, setDicModal] = useAtom(dicModal);
  const [showSide, setShowSide] = useAtom(dicItemValuesEditModal);
  const resetSide = useResetAtom(dicItemValuesEditModal);

  const [configModal, setShow] = useAtom(dicValueConfigModalAtom);
  const [_columns, setColumns] = useAtom(dicValueColumnsAtom);

  useEffect(() => {
    if (itemId) {
      console.debug("--------useEffect itemId", itemId);
      setdicValueFilter({
        ...dicValueFilter,
        filter: { ...dicValueFilter.filter, itemId: itemId },
      });
      setShowSide({ id: itemId.toString(), show: true, name: "报警原因配置" });
    }

    return () => {
      if (itemId) {
        resetSide();
      }
    };
  }, [itemId, setdicValueFilter, setShowSide, resetSide]);

  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getDicItemValueValueList", dicValueFilter],
    queryFn: () => getDicItemValueList(dicValueFilter),
  });

  const mutation = useMutation({
    mutationFn: delDicItemValue,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      queryClient.invalidateQueries({ queryKey: ["getDicItemValueValueList"] });
      Toast.success(opts);
    },
  });

  const removes = useMutation({
    mutationFn: delDicItemValues,
    onSuccess: async (res) => {
      if (res?.code !== 0) {
        return;
      }
      let opts = {
        content: `操作成功!`,
        duration: 2,
      };
      Toast.success(opts);
      queryClient.invalidateQueries({ queryKey: ["getDicItemValueValueList"] });
    },
  });

  const changeIsAcitve = useMutation({
    mutationFn: updateDicItemValue,
    onSuccess: async (res) => {
      if (res?.code === 0) {
        let opts = {
          content: `操作成功!`,
          duration: 2,
        };
        Toast.success(opts);
        queryClient.invalidateQueries({
          queryKey: ["getDicItemValueValueList"],
        });
      }
    },
  });

  const dataSource = useMemo(() => data?.data ?? {}, [data?.data]);
  const result = useMemo(() => dataSource?.results ?? [], [dataSource]);

  const handleConfirm = useCallback(
    (record) => {
      mutation.mutate(record.id);
    },
    [mutation]
  );

  const rowSelection = useMemo(
    () => ({
      fixed: true,
      onChange: (selectedRowKeys) => {
        setRows(selectedRowKeys);
      },
    }),
    [setRows]
  );

  const handleOpenSetting = useCallback(() => {
    setShow(true);
  }, [setShow]);

  const handleOpenEdit = useCallback(
    (id?: string) => {
      setDicModal({
        id: id ?? "",
        show: true,
      });
    },
    [setDicModal]
  );

  const handlePageChange = useCallback(
    (currentPage: number) => {
      setdicValueFilter({
        ...dicValueFilter,
        pageNumber: currentPage,
      });
    },
    [dicValueFilter, setdicValueFilter]
  );

  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      setdicValueFilter({
        ...dicValueFilter,
        pageSize: pageSize,
      });
    },
    [dicValueFilter, setdicValueFilter]
  );

  const handleRemoves = useCallback(() => {
    removes.mutate(rows);
    setRows([]);
  }, [removes, rows, setRows]);

  const handleChangeSwitch = (bool, record) => {
    changeIsAcitve.mutate({
      id: record?.id,
      values: {
        ...record,
        isActive: bool ? 1 : 2,
      },
    });
  };

  const columns = useMemo(() => {
    return [
      ..._columns,
      {
        title: <Tooltip content="是否启用">字典是否启用</Tooltip>,
        dataIndex: "isActive",
        isShow: true,
        ellipsis: true,
        render: (t, r) => {
          return (
            <Switch
              checked={Boolean(t == 1)}
              onChange={(checked) => {
                handleChangeSwitch(checked, r);
              }}
            />
          );
        },
      },
      {
      title: <Tooltip content="操作">操作</Tooltip>,
      isShow: true,
      dataIndex: 'operate',
      key: 'operate',
      align: 'center',
      render: (text, record) => (
        <div>
          <ButtonGroup aria-label="操作按钮组">

            <Button onClick={() => { handleOpenEdit(record.id) }}>编辑</Button>
            <Popconfirm
              position="bottomRight"
              title="确定是否要删除该项？"
              content="此修改将不可逆"
              okType="danger"
              okButtonProps={{
                className: "semi-button semi-button-danger semi-button-light"
              }}
              onConfirm={() => { handleConfirm(record) }}
            >
              <Button type="danger" >删除</Button>
            </Popconfirm>
          </ButtonGroup>
        </div>
      ),
    } ,
    ];
  }, [_columns]);

  const pageContent = (
    <>
      <TableConfig
        visible={configModal}
        columns={_columns}
        handleClose={setShow}
        handleSave={setColumns}
      />
      <SideFilter />
      <div className="bg-white shadow px-4 h-fit rounded">
        <div className="flex py-4 justify-between">
          <div className="flex gap-4">
            <button
              className="btn rounded btn-primary btn-sm"
              onClick={() => {
                handleOpenEdit();
              }}
            >
              新增
              <IconPlus size="small" />
            </button>
            <span className="flex text-gray-900 text-opacity-60 text-sm justify-center items-center">
              已选中{rows?.length ?? 0}个
            </span>
            {rows?.length ? (
              <Popconfirm
                title="确定是否要删除该项？"
                content="此修改将不可逆"
                okType="danger"
                okButtonProps={{
                  className: "semi-button semi-button-danger semi-button-light",
                }}
                onConfirm={handleRemoves}
              >
                <button className="btn btn-sm rounded">批量删除</button>
              </Popconfirm>
            ) : null}
          </div>

          <div className="flex gap">
            <div className="tooltip" data-tip="刷新">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={() => {
                  refetch();
                }}
              >
                <IconRefresh />
              </button>
            </div>

            <div className="tooltip" data-tip="设置">
              <button
                className="btn btn-sm btn-ghost rounded no-animation"
                onClick={handleOpenSetting}
              >
                <IconSetting />
              </button>
            </div>
          </div>
        </div>
        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          // scroll={{ x:1200 }}
          columns={columns?.filter?.((o) => o.fixed || o.isShow)}
          dataSource={result}
          rowSelection={rowSelection}
          loading={isLoading}
          onHeaderRow={(columns, index) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={{
            showSizeChanger: true,
            popoverPosition: "topRight",
            currentPage: dataSource?.pageNumber ?? 1,
            pageSize: dataSource?.pageSize ?? 10,
            total: dataSource?.totalCount ?? 0,
            onPageChange: handlePageChange,
            onPageSizeChange: handlePageSizeChange,
            pageSizeOpts: [10, 15, 20, 50],
          }}
        />
      </div>
    </>
  );

  if (itemId) {
    return pageContent;
  }

  return (
    <SideSheet
      size="large"
      title={`字典项-${showSide?.name ?? ""}`}
      visible={showSide?.show}
      onCancel={resetSide}
    >
      {pageContent}
    </SideSheet>
  );
};
