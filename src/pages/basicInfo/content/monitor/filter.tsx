import { IconSearch } from "@douyinfe/semi-icons";
import { Form } from "@douyinfe/semi-ui";
import { useToggle } from "ahooks";
import { monitorFilterAtom } from "atoms/basicInfo";
import { AreaSearch, MONITOR_TYPE_MAP, RestSelect } from "components";
import { VIDEO_TYPE_MAP } from "components/enum/majorHazard";
import { useFilterSearch } from "hooks";

export const MonitorFilter = () => {
  const [state, { toggle }] = useToggle();
  const [handleSearch, handleReset] = useFilterSearch(monitorFilterAtom);

  return (
    <div className="flex flex-col bg-white shadow rounded relative ">
      <div className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 ">
        筛选总览
      </div>

      <div className="p-4 pr-0">
        <Form
          layout="horizontal"
          onSubmit={handleSearch}
          className="grid grid-cols-4 gap-y-4 "
        >
          <Form.Input
            noLabel
            field="query"
            placeholder="请填入摄像头名称"
            className="w-full"
            suffix={<IconSearch />}
            showClear
          />
          <AreaSearch field="areaId" placeholder="请选择摄像头区域" noLabel />
          <RestSelect
            field="type"
            placeholder="请选择摄像头类型"
            options={MONITOR_TYPE_MAP}
            noLabel
          />
          <RestSelect
            field="videoType"
            placeholder="请选择监控区域类型"
            options={VIDEO_TYPE_MAP}
            noLabel
          />

          <div className="flex gap-2">
            <button className="btn rounded btn-primary btn-sm">
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="reset"
              onClick={handleReset}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
