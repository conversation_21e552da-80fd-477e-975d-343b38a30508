import { useState } from "react";
import AlarmDurationStatsTable from "./components/AlarmDurationStatsTable";
import AlarmNumStatsTable from "./components/AlarmNumStatsTable";
import AlarmPriorityPie from "./components/AlarmPriorityPie";
import AlarmTrendChart from "./components/AlarmTrendChart";
import BasicStats from "./components/BasicStats";
import FilterBar, { FilterValue } from "./components/FilterBar";
import MonitorTypePie from "./components/MonitorTypePie";

export function AlarmIndexContent() {
  // 统一管理筛选条件
  const [filter, setFilter] = useState<FilterValue>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });

  // 检查是否有有效的筛选条件来显示表格
  const hasValidFilter =
    filter.areaId != null && filter.beginDate != null && filter.endDate != null;

  return (
    <div className="page-root">
      <BasicStats />
      <FilterBar value={filter} onChange={setFilter} />
      <AlarmTrendChart filter={filter} />
      <div className="grid grid-cols-2 gap-x-5">
        <MonitorTypePie filter={filter} />
        <AlarmPriorityPie filter={filter} />
      </div>

      <div className="grid grid-cols-2 gap-x-5">
        <AlarmNumStatsTable
          filter={filter}
        />
        <AlarmDurationStatsTable
          filter={filter}
        />
      </div>
    </div>
  );
}
