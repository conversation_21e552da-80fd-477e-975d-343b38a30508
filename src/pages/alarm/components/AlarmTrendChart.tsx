import { getAlarmTrendStat } from "api/alarm/alarmStat";
import {
  TrendChart,
  buildSmoothAreaLineOption,
} from "components/chart/TrendChart";
import { formatDate, formatDateDay, formatDateHour } from "utils";
import { FilterValue } from "./FilterBar";

interface AlarmTrendChartProps {
  filter: Pick<FilterValue, "areaId" | "beginDate" | "endDate">;
}

export default function AlarmTrendChart({ filter }: AlarmTrendChartProps) {
  // 自适应时间格式化函数，根据 intervalType 选择合适的格式化方式
  const safeFormatDate = (v: any, data?: any) => {
    const intervalType = data?.data?.intervalType;

    // 根据 intervalType 选择合适的格式化函数
    if (intervalType === 1) {
      // 小时级别数据
      return formatDateHour(v) || "";
    } else if (intervalType === 2) {
      // 天级别数据
      return formatDateDay(v) || "";
    }

    // 默认使用完整的时间格式
    return formatDate(v) || "";
  };

  return (
    <TrendChart
      title="报警数量趋势"
      queryKey={["getAlarmTrendStat"]}
      queryFn={getAlarmTrendStat}
      filter={filter}
      optionBuilder={buildSmoothAreaLineOption({
        xField: "statTime",
        yField: "num",
        xFormatter: safeFormatDate,
        lineColor: "#60B7FF",
        areaColor: "#60B7FF",
        areaTo: "#fff",
      })}
      height={300}
    />
  );
}
