import { DatePicker, InputNumber, Select } from "@douyinfe/semi-ui";
import { useAreaListOptions } from "hooks/useDoubleGuardList";
import { isNil } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { formatRFC3339 } from "utils";

// 筛选状态类型（允许null/undefined）
export interface FilterValue {
  areaId: number | null | undefined;
  beginDate: string | null;
  endDate: string | null;
  topN?: number;
}

// 有效筛选类型（要求非空）
export interface ValidFilter {
  areaId: number;
  beginDate: string;
  endDate: string;
  topN?: number;
}

// 筛选区props类型
export interface FilterBarProps {
  value: FilterValue;
  onChange: (v: FilterValue) => void;
  onInitialized?: (initialValue: FilterValue) => void; // 初始化完成回调
  showTopN?: boolean; // 控制是否显示topN筛选项
  showArea?: boolean; // 控制是否显示区域筛选项
  showTimeLimit?: boolean; // 控制是否显示时间限制选择器
  datePickerType?: "dateTimeRange" | "dateRange";
  defaultDateMode?: "daily" | "monthly"; // 默认日期模式：daily=今天到明天，monthly=当月到下月
  timeLimitDays?: 31 | 366; // 时间限制天数
}

/**
 * 获取今天0点到明天0点的日期范围
 */
function getDailyDateRange(): [string, string] {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const tomorrow = new Date(today);
  tomorrow.setDate(tomorrow.getDate() + 1);

  return [formatRFC3339(today), formatRFC3339(tomorrow)];
}

/**
 * 获取当月第一天0点到下月第一天0点的日期范围
 */
function getMonthlyDateRange(): [string, string] {
  const now = new Date();

  // 当月第一天0点
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1, 0, 0, 0, 0);

  // 下月第一天0点
  const nextMonth = new Date(
    now.getFullYear(),
    now.getMonth() + 1,
    1,
    0,
    0,
    0,
    0
  );

  return [formatRFC3339(firstDay), formatRFC3339(nextMonth)];
}

/**
 * 验证日期范围是否超出限制，如果超出则调整结束日期
 */
function validateAndAdjustDateRange(
  beginDate: string,
  endDate: string,
  limitDays: number
): [string, string] {
  if (!beginDate || !endDate) return [beginDate, endDate];

  const start = new Date(beginDate);
  const end = new Date(endDate);
  const diffDays = Math.ceil(
    (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24)
  );

  if (diffDays > limitDays) {
    // 超出限制，调整结束日期
    const adjustedEnd = new Date(start);
    adjustedEnd.setDate(adjustedEnd.getDate() + limitDays);
    return [beginDate, formatRFC3339(adjustedEnd)];
  }

  return [beginDate, endDate];
}

/**
 * 报警首页筛选区，支持区域、时间范围、时间限制和TopN筛选
 */
export default function FilterBar({
  value,
  onChange,
  onInitialized,
  showTopN = false,
  showArea = true,
  showTimeLimit = false,
  datePickerType = "dateRange",
  defaultDateMode = "daily",
  timeLimitDays = 366,
}: FilterBarProps) {
  console.debug("FilterBar value", value);

  // 时间限制状态
  const [currentTimeLimitDays, setCurrentTimeLimitDays] =
    useState<number>(timeLimitDays);

  const areaOptions = useAreaListOptions().map((item: any) => ({
    label: item.name,
    value: item.id,
  }));

  // 时间限制选项
  const timeLimitOptions = [
    { label: "31天", value: 31 },
    { label: "366天", value: 366 },
  ];

  // 日期选择值
  const dateRange = useMemo(() => {
    if (value.beginDate && value.endDate) {
      return [value.beginDate, value.endDate];
    }
    return [];
  }, [value.beginDate, value.endDate]);

  // 初始化默认值
  useEffect(() => {
    // 只有当beginDate和endDate都为空时才设置默认值
    if (!value.beginDate || !value.endDate) {
      const [beginDate, endDate] =
        defaultDateMode === "monthly"
          ? getMonthlyDateRange()
          : getDailyDateRange();

      const initialValue = {
        ...value,
        beginDate,
        endDate,
      };

      console.debug("FilterBar 设置初始值", { defaultDateMode, initialValue });

      // 更新值
      onChange(initialValue);

      // 通知父组件初始化完成
      onInitialized?.(initialValue);
    }
  }, []); // 只在组件挂载时执行一次

  // 处理日期变化
  const handleDateChange = (dateValues: any) => {
    let begin = "";
    let end = "";

    if (Array.isArray(dateValues)) {
      begin = dateValues[0]
        ? typeof dateValues[0] === "string"
          ? dateValues[0]
          : formatRFC3339(dateValues[0])
        : "";
      end = dateValues[1]
        ? typeof dateValues[1] === "string"
          ? dateValues[1]
          : formatRFC3339(dateValues[1])
        : "";
    }

    // 验证并调整日期范围
    const [adjustedBegin, adjustedEnd] = validateAndAdjustDateRange(
      begin,
      end,
      currentTimeLimitDays
    );

    onChange({
      ...value,
      beginDate: adjustedBegin,
      endDate: adjustedEnd,
    });
  };

  // 处理时间限制变化
  const handleTimeLimitChange = (
    selectedValue: string | number | any[] | Record<string, any> | undefined
  ) => {
    const limitDays = Number(selectedValue);
    if (isNaN(limitDays)) return;

    setCurrentTimeLimitDays(limitDays);

    // 如果当前日期范围超出新的限制，重新验证并调整
    if (value.beginDate && value.endDate) {
      const [adjustedBegin, adjustedEnd] = validateAndAdjustDateRange(
        value.beginDate,
        value.endDate,
        limitDays
      );

      if (adjustedBegin !== value.beginDate || adjustedEnd !== value.endDate) {
        onChange({
          ...value,
          beginDate: adjustedBegin,
          endDate: adjustedEnd,
        });
      }
    }
  };

  return (
    <div className="flex items-center gap-4 mb-6">
      {/* 区域选择 */}
      {showArea && (
        <Select
          value={isNil(value.areaId) ? undefined : Number(value.areaId)}
          onChange={(v) => onChange({ ...value, areaId: Number(v) })}
          style={{ width: 180 }}
          optionList={areaOptions}
          placeholder="选择区域"
          size="large"
        />
      )}

      {/* 时间范围选择 */}
      <DatePicker
        type={datePickerType}
        value={dateRange as [string, string]}
        onChange={handleDateChange}
        size="large"
        style={{ width: 320 }}
        placeholder={["开始日期", "结束日期"]}
      />

      {/* 时间限制选择 */}
      {showTimeLimit && (
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">时间限制</span>
          <Select
            value={currentTimeLimitDays}
            onChange={handleTimeLimitChange}
            style={{ width: 100 }}
            optionList={timeLimitOptions}
            size="large"
          />
        </div>
      )}

      {/* TopN筛选 */}
      {showTopN && (
        <div className="flex items-center gap-2">
          <span className="text-sm text-gray-600">显示前</span>
          <InputNumber
            value={value.topN || 10}
            onChange={(v: number | string | undefined) =>
              onChange({ ...value, topN: Math.max(1, Number(v) || 10) })
            }
            min={1}
            max={1000}
            size="large"
            style={{ width: 80 }}
            placeholder="10"
          />
          <span className="text-sm text-gray-600">条</span>
        </div>
      )}
    </div>
  );
}

/**
 * 使用示例：
 *
 * // 基础用法
 * const [filterValue, setFilterValue] = useState<FilterValue>({
 *   areaId: null,
 *   beginDate: null,
 *   endDate: null,
 * });
 *
 * <FilterBar
 *   value={filterValue}
 *   onChange={setFilterValue}
 * />
 *
 * // 高级用法：启用所有功能
 * const [filterValue, setFilterValue] = useState<FilterValue>({
 *   areaId: null,
 *   beginDate: null,
 *   endDate: null,
 *   topN: 10,
 * });
 *
 * const handleFilterInitialized = (initialValue: FilterValue) => {
 *   console.log("筛选器已初始化:", initialValue);
 *   // 在这里调用API获取数据
 *   fetchDataWithFilter(initialValue);
 * };
 *
 * <FilterBar
 *   value={filterValue}
 *   onChange={setFilterValue}
 *   onInitialized={handleFilterInitialized}
 *   showTopN={true}
 *   showArea={true}
 *   showTimeLimit={true}
 *   datePickerType="dateTimeRange"
 *   defaultDateMode="monthly"  // 使用月度默认值
 *   timeLimitDays={366}       // 允许最大366天的时间范围
 * />
 *
 * // 监听筛选值变化
 * useEffect(() => {
 *   if (filterValue.beginDate && filterValue.endDate) {
 *     fetchDataWithFilter(filterValue);
 *   }
 * }, [filterValue]);
 *
 * // 配置选项说明：
 * // - defaultDateMode: "daily" = 今天0点到明天0点，"monthly" = 当月第一天0点到下月第一天0点
 * // - timeLimitDays: 31 | 366，限制用户选择的时间范围最大天数
 * // - showTimeLimit: 是否显示时间限制选择器
 * // - onInitialized: 组件初始化完成后的回调，适合在此时调用API
 */
