# FilterBar 组件使用说明

## 📋 概述

`FilterBar` 是报警系统的筛选条组件，提供区域选择、时间范围选择、时间限制和TopN筛选功能。支持自动设置默认值和初始化回调，便于父组件在组件加载时自动调用API获取数据。

## 🚀 主要功能

### ✨ 新增功能

1. **自动默认值设置**: 支持"今日"和"当月"两种默认时间范围模式
2. **时间限制控制**: 支持31天和366天的时间范围限制，自动验证和调整
3. **初始化回调**: 组件初始化完成后触发回调，便于父组件调用API

### 📅 默认日期模式

- **daily模式**: 今天的0点到明天的0点
- **monthly模式**: 当月第一天的0点到下个月第一天的0点

### ⏰ 时间限制

- **31天限制**: 适用于短期数据查询
- **366天限制**: 适用于年度数据查询
- **自动调整**: 当用户选择的日期范围超出限制时，自动调整结束日期

## 📦 接口定义

### FilterValue 接口

```typescript
export interface FilterValue {
  areaId: number | null | undefined;
  beginDate: string | null;
  endDate: string | null;
  topN?: number;
}
```

### FilterBarProps 接口

```typescript
export interface FilterBarProps {
  value: FilterValue; // 当前筛选值
  onChange: (v: FilterValue) => void; // 值变化回调
  onInitialized?: (initialValue: FilterValue) => void; // 初始化完成回调
  showTopN?: boolean; // 是否显示TopN筛选
  showArea?: boolean; // 是否显示区域筛选
  showTimeLimit?: boolean; // 是否显示时间限制选择器
  datePickerType?: "dateTimeRange" | "dateRange"; // 日期选择器类型
  defaultDateMode?: "daily" | "monthly"; // 默认日期模式
  timeLimitDays?: 31 | 366; // 时间限制天数
}
```

## 💡 使用示例

### 基础用法

```tsx
import { useState } from "react";
import FilterBar, { FilterValue } from "./components/FilterBar";

function AlarmPage() {
  const [filterValue, setFilterValue] = useState<FilterValue>({
    areaId: null,
    beginDate: null,
    endDate: null,
  });

  return <FilterBar value={filterValue} onChange={setFilterValue} />;
}
```

### 完整功能用法

```tsx
import { useState, useEffect } from "react";
import FilterBar, { FilterValue } from "./components/FilterBar";
import { fetchAlarmData } from "../api/alarm";

function AlarmPage() {
  const [filterValue, setFilterValue] = useState<FilterValue>({
    areaId: null,
    beginDate: null,
    endDate: null,
    topN: 10,
  });

  const [alarmData, setAlarmData] = useState([]);
  const [loading, setLoading] = useState(false);

  // 初始化回调 - 组件加载时自动调用API
  const handleFilterInitialized = async (initialValue: FilterValue) => {
    console.log("筛选器已初始化:", initialValue);
    await fetchData(initialValue);
  };

  // 获取数据的通用方法
  const fetchData = async (filter: FilterValue) => {
    if (!filter.beginDate || !filter.endDate) return;

    setLoading(true);
    try {
      const result = await fetchAlarmData({
        areaId: filter.areaId,
        beginDate: filter.beginDate,
        endDate: filter.endDate,
        topN: filter.topN,
      });
      setAlarmData(result);
    } catch (error) {
      console.error("获取报警数据失败:", error);
    } finally {
      setLoading(false);
    }
  };

  // 监听筛选值变化，自动刷新数据
  useEffect(() => {
    if (filterValue.beginDate && filterValue.endDate) {
      fetchData(filterValue);
    }
  }, [filterValue]);

  return (
    <div>
      <FilterBar
        value={filterValue}
        onChange={setFilterValue}
        onInitialized={handleFilterInitialized}
        showTopN={true}
        showArea={true}
        showTimeLimit={true}
        datePickerType="dateTimeRange"
        defaultDateMode="monthly"
        timeLimitDays={366}
      />

      {loading ? (
        <div>加载中...</div>
      ) : (
        <div>
          {/* 渲染报警数据 */}
          {alarmData.map((item) => (
            <div key={item.id}>{item.title}</div>
          ))}
        </div>
      )}
    </div>
  );
}
```

### 不同场景的配置

#### 1. 日常监控场景

```tsx
<FilterBar
  value={filterValue}
  onChange={setFilterValue}
  defaultDateMode="daily" // 今日数据
  timeLimitDays={31} // 最多查看31天
  showTimeLimit={true}
  showTopN={true}
/>
```

#### 2. 月度报告场景

```tsx
<FilterBar
  value={filterValue}
  onChange={setFilterValue}
  defaultDateMode="monthly" // 当月数据
  timeLimitDays={366} // 最多查看366天
  showTimeLimit={true}
  datePickerType="dateRange" // 只需要日期，不需要时间
/>
```

#### 3. 区域对比场景

```tsx
<FilterBar
  value={filterValue}
  onChange={setFilterValue}
  showArea={true} // 重点是区域选择
  showTopN={false} // 不需要TopN
  showTimeLimit={false} // 不限制时间范围
  defaultDateMode="monthly"
/>
```

## ⚙️ 配置选项说明

| 属性              | 类型          | 默认值        | 说明                   |
| ----------------- | ------------- | ------------- | ---------------------- |
| `value`           | `FilterValue` | -             | 当前筛选值，必需       |
| `onChange`        | `function`    | -             | 值变化回调，必需       |
| `onInitialized`   | `function`    | -             | 初始化完成回调，可选   |
| `showTopN`        | `boolean`     | `false`       | 是否显示TopN筛选器     |
| `showArea`        | `boolean`     | `true`        | 是否显示区域选择器     |
| `showTimeLimit`   | `boolean`     | `false`       | 是否显示时间限制选择器 |
| `datePickerType`  | `string`      | `"dateRange"` | 日期选择器类型         |
| `defaultDateMode` | `string`      | `"daily"`     | 默认日期模式           |
| `timeLimitDays`   | `number`      | `31`          | 时间限制天数           |

### defaultDateMode 详细说明

- **"daily"**: 自动设置为今天的0:00:00到明天的0:00:00
- **"monthly"**: 自动设置为当月第一天的0:00:00到下月第一天的0:00:00

### timeLimitDays 详细说明

- **31**: 适用于日常监控，限制查询范围在一个月内
- **366**: 适用于年度分析，允许查询整年数据

## 🔧 高级特性

### 1. 自动日期调整

当用户选择的日期范围超出 `timeLimitDays` 设置时，组件会自动调整结束日期，确保日期范围不超出限制。

### 2. 初始化时机

组件在以下情况下会设置默认值并触发 `onInitialized` 回调：

- 组件首次挂载
- `value.beginDate` 或 `value.endDate` 为空

### 3. 数据验证

- 自动验证日期格式并转换为RFC3339格式
- 自动验证数字输入并处理边界情况
- 自动处理空值和异常值

## 🐛 注意事项

1. **初始化只执行一次**: `onInitialized` 回调只在组件首次挂载时执行，避免重复调用API
2. **时间格式**: 组件内部使用RFC3339格式处理时间，确保时区一致性
3. **性能优化**: 使用 `useMemo` 缓存日期范围计算，避免不必要的重渲染
4. **类型安全**: 所有接口都有完整的TypeScript类型定义

## 🚀 扩展建议

如需要扩展功能，建议的扩展点：

1. 添加更多时间限制选项（如7天、90天）
2. 支持自定义默认日期范围
3. 添加快捷日期选择（如"最近7天"、"上周"等）
4. 支持多区域选择
5. 添加筛选条件的本地存储功能
