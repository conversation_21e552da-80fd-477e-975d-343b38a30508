import { useQuery } from "@tanstack/react-query";
import { getDicName } from "api";
import { getMonitorTypeStat } from "api/alarm/alarmStat";
import {
  buildPieCenterContent,
  buildPieOption,
  PieChart,
} from "components/chart/PieChart";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { AlarmManagementRoutes } from "utils/routerConstants";
import { FilterValue } from "./FilterBar";

interface MonitorTypePieProps {
  filter: Pick<FilterValue, "areaId" | "beginDate" | "endDate">;
}

export default function MonitorTypePie({ filter }: MonitorTypePieProps) {
  const navigate = useNavigate();

  // 获取监测类型字典，用于name到ID的转换
  const { data: monitorTypeDic } = useQuery({
    queryKey: ["getDicName", "monitorType"],
    queryFn: () => getDicName("monitorType"),
  });

  // 构建监测类型name到ID的映射
  const monitorTypeMap = useMemo(() => {
    const dicList = monitorTypeDic?.data ?? [];
    const map = new Map<string, number>();
    dicList.forEach((item: { dicValue: string; id: number }) => {
      map.set(item.dicValue, item.id);
    });
    return map;
  }, [monitorTypeDic]);

  // 处理饼图扇形点击事件
  const handleItemClick = (params: { name: string; value: number }) => {
    const { name } = params;

    // 将监测类型名称转换为ID
    const monitorTypeValueId = monitorTypeMap.get(name);
    if (!monitorTypeValueId) {
      console.warn(`无法找到监测类型 "${name}" 对应的ID`);
      return;
    }

    // 构建查询参数
    const searchParams = new URLSearchParams();

    // 时间范围参数转换
    if (filter.beginDate) {
      searchParams.append("alarmTimeGte", filter.beginDate);
    }
    if (filter.endDate) {
      searchParams.append("alarmTimeLt", filter.endDate);
    }

    // 区域参数
    if (filter.areaId) {
      searchParams.append("areaId", filter.areaId.toString());
    }

    // 监测类型参数
    searchParams.append("monitorTypeValueId", monitorTypeValueId.toString());

    // 跳转到传感器报警页面
    const targetUrl = `${AlarmManagementRoutes.ALARM_PROCESS_SENSOR_ALARM}?${searchParams.toString()}`;
    navigate(targetUrl);
  };

  return (
    <PieChart
      title="监测类型分布"
      queryKey={["getMonitorTypeStat"]}
      queryFn={getMonitorTypeStat}
      filter={filter}
      optionBuilder={buildPieOption({ nameField: "name", valueField: "num" })}
      centerContent={buildPieCenterContent({
        totalField: "num",
        label: "监测类型总计",
      })}
      height={300}
      onItemClick={handleItemClick}
    />
  );
}
