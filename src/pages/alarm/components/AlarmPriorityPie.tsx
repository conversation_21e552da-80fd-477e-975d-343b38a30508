import { getPriorityStat } from "api/alarm/alarmStat";
import {
  <PERSON><PERSON><PERSON>,
  buildPieCenterContent,
  buildPieOption,
} from "components/chart/PieChart";
import { SENSOR_ALERTPRIORITY_MAP } from "components/enum";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { AlarmManagementRoutes } from "utils/routerConstants";
import { FilterValue } from "./FilterBar";

interface AlarmPriorityPieProps {
  filter: Pick<FilterValue, "areaId" | "beginDate" | "endDate">;
}

export default function AlarmPriorityPie({ filter }: AlarmPriorityPieProps) {
  const navigate = useNavigate();

  // 构建优先级名称到ID的映射
  const priorityMap = useMemo(() => {
    const map = new Map<string, number>();
    SENSOR_ALERTPRIORITY_MAP.forEach((item) => {
      map.set(item.name, item.id);
    });
    return map;
  }, []);

  // 处理饼图扇形点击事件
  const handleItemClick = (params: { name: string; value: number }) => {
    const { name } = params;

    // 将优先级名称转换为ID 
    const priorityId = priorityMap.get(name);
    if (!priorityId) {
      console.warn(`无法找到优先级 "${name}" 对应的ID`);
      return;
    }

    // 构建查询参数
    const searchParams = new URLSearchParams();

    // 时间范围参数转换
    if (filter.beginDate) {
      searchParams.append("alarmTimeGte", filter.beginDate);
    }
    if (filter.endDate) {
      searchParams.append("alarmTimeLt", filter.endDate);
    }

    // 区域参数
    if (filter.areaId) {
      searchParams.append("areaId", filter.areaId.toString());
    }

    // 优先级参数
    searchParams.append("priority", priorityId.toString());

    // 跳转到传感器报警页面
    const targetUrl = `${AlarmManagementRoutes.ALARM_PROCESS_SENSOR_ALARM}?${searchParams.toString()}`;
    navigate(targetUrl);
  };

  return (
    <PieChart
      title="报警优先级分布"
      queryKey={["getPriorityStat"]}
      queryFn={getPriorityStat}
      filter={filter}
      optionBuilder={buildPieOption({ nameField: "name", valueField: "num" })}
      centerContent={buildPieCenterContent({
        totalField: "num",
        label: "报警总计",
      })}
      height={300}
      onItemClick={handleItemClick}
    />
  );
}
