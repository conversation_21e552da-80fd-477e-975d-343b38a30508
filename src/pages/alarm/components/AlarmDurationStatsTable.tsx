import { TableChart } from "components/chart/TableChart";
import { useNavigate } from "react-router-dom";
import { AlarmManagementRoutes } from "utils/routerConstants";
import {
  getAreaDurationStat,
  getDepartmentDurationStat,
  getDeviceDurationStat,
} from "../../../api/alarm/alarmStat";
import { FilterValue } from "./FilterBar";

interface DeviceStatsTableProps {
  filter: FilterValue;
}

// 设备统计表格组件，展示各设备报警数量，字段严格对标接口 equipment.name、num
export default function AlarmDurationStatsTable({
  filter,
}: DeviceStatsTableProps) {
  const navigate = useNavigate();

  /**
   * 处理下钻点击事件
   * @param record 当前行数据
   * @param statType 统计类型：'area' | 'department' | 'device'
   */
  const handleDrillDown = (record: any, statType: string) => {
    // 构建查询参数
    const params = new URLSearchParams();

    // 基础时间范围参数
    if (filter.beginDate) {
      params.append("alarmTimeGte", filter.beginDate);
    }
    if (filter.endDate) {
      params.append("alarmTimeLt", filter.endDate);
    }

    // 区域参数
    if (filter.areaId) {
      params.append("areaId", filter.areaId.toString());
    }

    // 根据统计类型添加特定参数
    switch (statType) {
      case "area":
        if (record.area?.id) {
          params.append("areaId", record.area.id.toString());
        }
        break;
      case "department":
        if (record.department?.id) {
          params.append("departmentId", record.department.id.toString());
        }
        break;
      case "device":
        if (record.equipment?.id) {
          params.append("equipmentId", record.equipment.id.toString());
        }
        break;
    }

    // 跳转到实时报警页面
    const targetUrl = `${AlarmManagementRoutes.ALARM_PROCESS_SENSOR_ALARM}?${params.toString()}`;
    navigate(targetUrl);
  };

  const tabList = [
    {
      label: "区域统计",
      value: "area",
      queryKey: ["getAreaDurationStat"],
      queryFn: getAreaDurationStat,
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          align: "center" as const,
          width: 80,
          render: (_: any, __: any, index: number) => (
            <span className="text-gray-600">{index + 1}</span>
          ),
        },
        {
          title: "区域名称",
          dataIndex: "area.name",
          align: "left" as const,
        },
        {
          title: "报警时长",
          dataIndex: "alarmTime",
          align: "center" as const,
          render: (value: number, record: any) => (
            <span
              className="font-bold text-blue-600 cursor-pointer hover:text-blue-800 hover:underline transition-colors"
              onClick={() => handleDrillDown(record, "area")}
              title="点击查看详细报警信息"
            >
              {value}
            </span>
          ),
        },
      ],
    },
    {
      label: "设备统计",
      value: "device",
      queryKey: ["getDeviceDurationStat"],
      queryFn: getDeviceDurationStat,
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          align: "center" as const,
          width: 80,
          render: (_: any, __: any, index: number) => (
            <span className="text-gray-600">{index + 1}</span>
          ),
        },
        {
          title: "设备名称",
          dataIndex: "equipment.name",
          align: "left" as const,
        },
        {
          title: "报警时长",
          dataIndex: "alarmTime",
          align: "center" as const,
          render: (value: number, record: any) => (
            <span
              className="font-bold text-red-600 cursor-pointer hover:text-red-800 hover:underline transition-colors"
              onClick={() => handleDrillDown(record, "device")}
              title="点击查看详细报警信息"
            >
              {value}
            </span>
          ),
        },
      ],
    },
    {
      label: "部门统计",
      value: "department",
      queryKey: ["getDepartmentDurationStat"],
      queryFn: getDepartmentDurationStat,
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          align: "center" as const,
          width: 80,
          render: (_: any, __: any, index: number) => (
            <span className="text-gray-600">{index + 1}</span>
          ),
        },
        {
          title: "部门名称",
          dataIndex: "department.name",
          align: "left" as const,
        },
        {
          title: "报警时长",
          dataIndex: "alarmTime",
          align: "center" as const,
          render: (value: number, record: any) => (
            <span
              className="font-bold text-red-600 cursor-pointer hover:text-red-800 hover:underline transition-colors"
              onClick={() => handleDrillDown(record, "department")}
              title="点击查看详细报警信息"
            >
              {value}
            </span>
          ),
        },
      ],
    },
  ];

  return (
    <TableChart
      title="报警时长统计"
      filter={filter}
      tabList={tabList}
      height={400}
      emptyText="暂无报警时长数据"
    />
  );
}
