import { getAlarmDashStat } from "api/alarm/alarmStat";
import { sensorFilterAtom } from "atoms";
import { StatCard } from "components/chart/StatCard";
import { useAtom } from "jotai";
import { useNavigate } from "react-router-dom";
import { AlarmManagementRoutes } from "utils/routerConstants";

export default function BasicStats() {
  const navigate = useNavigate();
  const [filter, setFilter] = useAtom(sensorFilterAtom);
  return (
    <div className="mb-6">
      <StatCard
        queryKey={["getAlarmDashStat"]}
        queryFn={getAlarmDashStat}
        cards={[
          {
            label: "监测指标数",
            valueField: "sensorNum",
            onClick: () => {
              setFilter({ pageNumber: 1, pageSize: 10 });
              navigate(AlarmManagementRoutes.ALARM_SETTINGS_SENSOR);
            },
          },
          {
            label: "运行中监测指标",
            valueField: "sensorActiveNum",
            onClick: () => {
              setFilter((prev) => ({
                ...prev,
                filter: {
                  ...((prev as any).filter ?? {}),
                  isActive: 1,
                },
              }));
              navigate(AlarmManagementRoutes.ALARM_SETTINGS_SENSOR);
            },
          },
          {
            label: "指标类型",
            valueField: "monitorTypeNum",
            to: AlarmManagementRoutes.ALARM_SETTINGS_MONITORTYPE,
          },
          {
            label: "报警原因",
            valueField: "alarmReasonNum",
            to: AlarmManagementRoutes.ALARM_SETTINGS_REASON,
          },
          {
            label: "报警措施",
            valueField: "alarmMeasureNum",
            to: AlarmManagementRoutes.ALARM_SETTINGS_MEASURE,
          },
        ]}
        columns={5}
        height={160}
        gap={20}
      />
    </div>
  );
}
