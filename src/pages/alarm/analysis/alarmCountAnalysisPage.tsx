import React from "react";
import AlarmNumStatsTable from "../components/AlarmNumStatsTable";
import FilterBar, { FilterValue } from "../components/FilterBar";

const AlarmCountAnalysisPage = () => {
  const [filter, setFilter] = React.useState<FilterValue>({
    areaId: undefined,
    beginDate: null,
    endDate: null,
    topN: 10,
  });

  // 检查是否有有效的筛选条件来显示表格
  const hasValidFilter =
    filter.areaId != null && filter.beginDate != null && filter.endDate != null;

  return (
    <div className="p-4">
      {/* 使用最大宽度和居中布局优化宽屏显示效果 */}
      <div className="max-w-6xl mx-auto space-y-6">
        <FilterBar value={filter} onChange={setFilter} showTopN />

        <AlarmNumStatsTable filter={filter} />
      </div>
    </div>
  );
};

export default AlarmCountAnalysisPage;
