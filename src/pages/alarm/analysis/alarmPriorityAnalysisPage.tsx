import React from "react";
import AlarmPriorityPie from "../components/AlarmPriorityPie";
import FilterBar, { FilterValue } from "../components/FilterBar";

const AlarmPriorityAnalysisPage = () => {
  const [filter, setFilter] = React.useState<
    Pick<FilterValue, "areaId" | "beginDate" | "endDate">
  >({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });
  return (
    <div className="p-4">
      <div className="max-w-6xl mx-auto space-y-6">
        <FilterBar value={filter} onChange={setFilter} />

        <AlarmPriorityPie filter={filter} />
      </div>
    </div>
  );
};

export default AlarmPriorityAnalysisPage;
