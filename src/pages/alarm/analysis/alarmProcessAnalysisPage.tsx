import {
  getMeasureStat,
  getProcessStat,
  getReasonStat,
} from "api/alarm/alarmStat";
import { StatCard } from "components/chart/StatCard";
import { TableChart } from "components/chart/TableChart";
import React from "react";
import FilterBar, { FilterValue } from "../components/FilterBar";

const AlarmProcessAnalysisPage = () => {
  const [filter, setFilter] = React.useState<
    Pick<FilterValue, "areaId" | "beginDate" | "endDate">
  >({
    areaId: undefined,
    beginDate: null,
    endDate: null,
  });

  return (
    <div className="p-4">
      <FilterBar value={filter} onChange={setFilter} />
      <StatCard
        queryKey={["getProcessStat"]}
        queryFn={getProcessStat}
        filter={filter}
        cards={[
          { label: "报警数", valueField: "alarmNum" },
          { label: "处置数", valueField: "processNum" },
          {
            label: "处置率",
            valueField: "processRate",
            render: (value: number) => `${(value * 100).toFixed(1)}%`,
          },
        ]}
        columns={3}
        height={160}
        gap={20}
      />
      <div className="grid grid-cols-2 gap-x-5">
        <TableChart
          title="报警原因分析"
          queryKey={["getReasonStat"]}
          queryFn={getReasonStat}
          filter={filter}
          columns={[
            {
              title: "序号",
              render: (_, __, idx) => idx + 1,
              align: "center",
              width: 60,
            },
            { title: "报警原因", dataIndex: "alarmReason.dicValue" },
            { title: "报警数量", dataIndex: "num" },
          ]}
          height={400}
          emptyText="暂无报警原因数据"
        />
        <TableChart
          title="报警措施分析"
          queryKey={["getMeasureStat"]}
          queryFn={getMeasureStat}
          filter={filter}
          columns={[
            {
              title: "序号",
              render: (_, __, idx) => idx + 1,
              align: "center",
              width: 60,
            },
            { title: "报警措施", dataIndex: "alarmMeasure.dicValue" },
            { title: "报警数量", dataIndex: "num" },
          ]}
          height={400}
          emptyText="暂无报警措施数据"
        />
      </div>
    </div>
  );
};

export default AlarmProcessAnalysisPage;
