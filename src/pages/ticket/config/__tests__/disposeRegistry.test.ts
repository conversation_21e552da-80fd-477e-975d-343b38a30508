import { describe, expect, it } from "vitest";
import disposeRegistry from "../disposeRegistry";

describe("disposeRegistry - 组件属性配置测试", () => {
  describe("注册表结构测试", () => {
    it("应该正确导出组件配置注册表", () => {
      expect(disposeRegistry).toBeDefined();
      expect(typeof disposeRegistry).toBe("object");
      expect(disposeRegistry).not.toBeNull();
    });

    it("应该包含所有基础组件类型的配置", () => {
      const expectedComponentTypes = [
        "input",
        "textarea",
        "selector",
        "radio",
        "checkbox",
        "datePicker",
        "employeePicker",
        "table",
        "plainText",
        "annexFilePicker",
        "annexImgPicker",
      ];

      expectedComponentTypes.forEach((compType) => {
        expect(disposeRegistry[compType]).toBeDefined();
        expect(Array.isArray(disposeRegistry[compType])).toBe(true);
      });
    });

    it("应该包含原子类组件配置", () => {
      const atomicComponents = [
        "radio_base",
        "checkbox_base",
        "input_base",
        "text_base",
      ];

      atomicComponents.forEach((compType) => {
        expect(disposeRegistry[compType]).toBeDefined();
        expect(Array.isArray(disposeRegistry[compType])).toBe(true);
      });
    });
  });

  describe("输入框组件配置测试", () => {
    it("应该正确配置输入框属性", () => {
      const inputConfig = disposeRegistry.input;
      expect(inputConfig).toBeDefined();
      expect(Array.isArray(inputConfig)).toBe(true);
      expect(inputConfig.length).toBeGreaterThan(0);

      // 验证必需的配置项
      const configNames = inputConfig.map((config) => config.name);
      expect(configNames).toContain("formName");
      expect(configNames).toContain("placeHolder");
      expect(configNames).toContain("business");
      expect(configNames).toContain("isReq");
    });

    it("应该为输入框配置正确的业务选项", () => {
      const inputConfig = disposeRegistry.input;
      const businessConfig = inputConfig.find(
        (config) => config.name === "business"
      );

      expect(businessConfig).toBeDefined();
      expect(businessConfig?.type).toBe("select");
      expect(businessConfig?.options).toBeDefined();
      expect(Array.isArray(businessConfig?.options)).toBe(true);
      expect(businessConfig?.options?.length).toBeGreaterThan(0);

      // 验证业务选项包含预期值
      const businessValues = businessConfig?.options?.map(
        (opt: any) => opt.value
      );
      expect(businessValues).toContain("0"); // 无
      expect(businessValues).toContain("safetyDisclosureAudience");
      expect(businessValues).toContain("workPlace");
      expect(businessValues).toContain("workContent");
    });
  });

  describe("选择器组件配置测试", () => {
    it("应该正确配置选择器属性", () => {
      const selectorConfig = disposeRegistry.selector;
      expect(selectorConfig).toBeDefined();
      expect(Array.isArray(selectorConfig)).toBe(true);

      // 验证必需的配置项
      const configNames = selectorConfig.map((config) => config.name);
      expect(configNames).toContain("formName");
      expect(configNames).toContain("business");
      expect(configNames).toContain("isReq");
    });

    it("应该为选择器配置正确的业务选项", () => {
      const selectorConfig = disposeRegistry.selector;
      const businessConfig = selectorConfig.find(
        (config) => config.name === "business"
      );

      expect(businessConfig).toBeDefined();
      expect(businessConfig?.options).toBeDefined();
      expect(Array.isArray(businessConfig?.options)).toBe(true);

      // 验证业务选项包含预期值
      const businessValues = businessConfig?.options?.map(
        (opt: any) => opt.value
      );
      expect(businessValues).toContain("0"); // 无
      expect(businessValues).toContain("dh"); // 动火方式
      expect(businessValues).toContain("protective"); // 防护用品
      expect(businessValues).toContain("unit"); // 作业单位
    });
  });

  describe("单选框组件配置测试", () => {
    it("应该正确配置单选框属性", () => {
      const radioConfig = disposeRegistry.radio;
      expect(radioConfig).toBeDefined();
      expect(Array.isArray(radioConfig)).toBe(true);

      // 验证必需的配置项
      const configNames = radioConfig.map((config) => config.name);
      expect(configNames).toContain("formName");
      expect(configNames).toContain("options");
      expect(configNames).toContain("business");
      expect(configNames).toContain("isReq");
    });

    it("应该为单选框配置正确的业务选项", () => {
      const radioConfig = disposeRegistry.radio;
      const businessConfig = radioConfig.find(
        (config) => config.name === "business"
      );

      expect(businessConfig).toBeDefined();
      expect(businessConfig?.options).toBeDefined();
      expect(Array.isArray(businessConfig?.options)).toBe(true);

      // 验证业务选项包含预期值
      const businessValues = businessConfig?.options?.map(
        (opt: any) => opt.value
      );
      expect(businessValues).toContain("0"); // 无
      expect(businessValues).toContain("level"); // 作业级别
      expect(businessValues).toContain("unitCategory"); // 作业单位类别
    });
  });

  describe("表格组件配置测试", () => {
    it("应该正确配置表格属性", () => {
      const tableConfig = disposeRegistry.table;
      expect(tableConfig).toBeDefined();
      expect(Array.isArray(tableConfig)).toBe(true);

      // 验证表格特有的配置项
      const configNames = tableConfig.map((config) => config.name);
      expect(configNames).toContain("rowNum");
      expect(configNames).toContain("colNum");
      expect(configNames).toContain("paddingLeft");
      expect(configNames).toContain("paddingRight");
      expect(configNames).toContain("paddingTop");
      expect(configNames).toContain("paddingBottom");
      expect(configNames).toContain("isHead");
    });

    it("应该为表格配置正确的默认值", () => {
      const tableConfig = disposeRegistry.table;

      const rowNumConfig = tableConfig.find(
        (config) => config.name === "rowNum"
      );
      const colNumConfig = tableConfig.find(
        (config) => config.name === "colNum"
      );
      const isHeadConfig = tableConfig.find(
        (config) => config.name === "isHead"
      );

      expect(rowNumConfig?.defaultValue).toBe(2);
      expect(colNumConfig?.defaultValue).toBe(2);
      expect(isHeadConfig?.defaultValue).toBe(false);
    });
  });

  describe("人员选择器组件配置测试", () => {
    it("应该正确配置人员选择器属性", () => {
      const employeePickerConfig = disposeRegistry.employeePicker;
      expect(employeePickerConfig).toBeDefined();
      expect(Array.isArray(employeePickerConfig)).toBe(true);

      // 验证必需的配置项
      const configNames = employeePickerConfig.map((config) => config.name);
      expect(configNames).toContain("formName");
      expect(configNames).toContain("business");
      expect(configNames).toContain("isReq");
    });

    it("应该为人员选择器配置正确的业务选项", () => {
      const employeePickerConfig = disposeRegistry.employeePicker;
      const businessConfig = employeePickerConfig.find(
        (config) => config.name === "business"
      );

      expect(businessConfig).toBeDefined();
      expect(businessConfig?.options).toBeDefined();
      expect(Array.isArray(businessConfig?.options)).toBe(true);

      // 验证业务选项包含预期值
      const businessValues = businessConfig?.options?.map(
        (opt: any) => opt.value
      );
      expect(businessValues).toContain("0"); // 无
      expect(businessValues).toContain("jobInCharge"); // 作业负责人
      expect(businessValues).toContain("safeyAnalysisInCharge"); // 安全分析确认人
    });
  });

  describe("配置项结构验证测试", () => {
    it("应该确保所有配置项具有必需属性", () => {
      Object.entries(disposeRegistry).forEach(([componentType, configs]) => {
        expect(Array.isArray(configs)).toBe(true);

        configs.forEach((config, index) => {
          // 验证基本属性
          expect(
            config.label,
            `组件类型 ${componentType} 的配置项索引 ${index} - label 验证失败`
          ).toBeDefined();
          expect(
            config.type,
            `组件类型 ${componentType} 的配置项索引 ${index} - type 验证失败`
          ).toBeDefined();
          expect(
            config.name,
            `组件类型 ${componentType} 的配置项索引 ${index} - name 验证失败`
          ).toBeDefined();

          // 验证属性类型
          expect(
            typeof config.label,
            `组件类型 ${componentType} 的配置项索引 ${index} - label 类型验证失败`
          ).toBe("string");
          expect(
            typeof config.type,
            `组件类型 ${componentType} 的配置项索引 ${index} - type 类型验证失败`
          ).toBe("string");
          expect(
            typeof config.name,
            `组件类型 ${componentType} 的配置项索引 ${index} - name 类型验证失败`
          ).toBe("string");

          // 验证选择类型配置项包含选项
          if (config.type === "select" || config.type === "radio") {
            expect(
              config.options,
              `组件类型 ${componentType} 的配置项索引 ${index} - options 验证失败`
            ).toBeDefined();
            expect(
              Array.isArray(config.options),
              `组件类型 ${componentType} 的配置项索引 ${index} - options 数组验证失败`
            ).toBe(true);
          }
        });
      });
    });

    it("应该确保选项结构正确", () => {
      Object.entries(disposeRegistry).forEach(([componentType, configs]) => {
        configs.forEach((config) => {
          if (config.options && Array.isArray(config.options)) {
            config.options.forEach((option, optionIndex) => {
              // 处理不同类型的选项结构
              if (typeof option === "object" && option !== null) {
                expect(
                  option.label,
                  `组件类型 ${componentType} 的选项索引 ${optionIndex} - label 验证失败`
                ).toBeDefined();
                expect(
                  option.value,
                  `组件类型 ${componentType} 的选项索引 ${optionIndex} - value 验证失败`
                ).toBeDefined();
                expect(
                  typeof option.label,
                  `组件类型 ${componentType} 的选项索引 ${optionIndex} - label 类型验证失败`
                ).toBe("string");
                // value 可以是字符串、数字或布尔值
                expect(
                  ["string", "number", "boolean"].includes(typeof option.value),
                  `组件类型 ${componentType} 的选项索引 ${optionIndex} - value 类型验证失败`
                ).toBe(true);
              }
            });
          }
        });
      });
    });
  });

  describe("原子类组件配置测试", () => {
    it("应该正确配置原子类组件", () => {
      const atomicComponents = [
        "radio_base",
        "checkbox_base",
        "input_base",
        "text_base",
      ];

      atomicComponents.forEach((compType) => {
        const config = (disposeRegistry as any)[compType];
        expect(config).toBeDefined();
        expect(Array.isArray(config)).toBe(true);

        // 原子类组件通常配置较简单
        if (config && config.length > 0) {
          config.forEach((item: any) => {
            expect(item.label).toBeDefined();
            expect(item.type).toBeDefined();
            expect(item.name).toBeDefined();
          });
        }
      });
    });
  });

  describe("边界条件和错误处理测试", () => {
    it("应该处理不存在的组件类型", () => {
      const nonExistentType = "nonExistentComponent";
      expect((disposeRegistry as any)[nonExistentType]).toBeUndefined();
    });

    it("应该确保配置数据不可变性", () => {
      const originalInputConfig = disposeRegistry.input;
      const configCopy = [...originalInputConfig];

      // 尝试修改配置（在实际使用中应该避免）
      expect(() => {
        configCopy.push({
          label: "测试配置",
          type: "input",
          name: "testConfig",
        });
      }).not.toThrow();

      // 原始配置应该保持不变
      expect(disposeRegistry.input.length).toBe(originalInputConfig.length);
    });

    it("应该验证所有组件类型都有非空配置", () => {
      Object.entries(disposeRegistry).forEach(([, configs]) => {
        expect(configs).toBeDefined();
        expect(Array.isArray(configs)).toBe(true);
        // 允许空配置数组，但不允许 null 或 undefined
      });
    });
  });
});
