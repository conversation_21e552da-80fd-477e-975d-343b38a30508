import { describe, expect, it } from "vitest";
import {
  dhOptions,
  highPlaceOperationLevelOptions,
  liftingOperationLevelOptions,
  operationLevelOptions,
  protectiveOptions,
} from "../defaultValues";

describe("defaultValues - 防护用品选项", () => {
  it("应该包含所有必要的防护用品选项", () => {
    expect(protectiveOptions).toBeDefined();
    expect(Array.isArray(protectiveOptions)).toBe(true);
    expect(protectiveOptions.length).toBeGreaterThan(0);
  });

  it("每个防护用品选项应该有正确的结构", () => {
    protectiveOptions.forEach((option) => {
      expect(option).toHaveProperty("label");
      expect(option).toHaveProperty("id");
      expect(typeof option.label).toBe("string");
      expect(typeof option.id).toBe("number");
    });
  });

  it("应该包含关键的防护用品", () => {
    const labels = protectiveOptions.map((option) => option.label);

    expect(labels).toContain("安全帽");
    expect(labels).toContain("安全鞋");
    expect(labels).toContain("防毒面罩");
    expect(labels).toContain("五点式安全带/绳");
    expect(labels).toContain("气体报警仪");
  });

  it("防护用品ID应该是唯一的", () => {
    const ids = protectiveOptions.map((option) => option.id);
    const uniqueIds = [...new Set(ids)];
    expect(ids.length).toBe(uniqueIds.length);
  });
});

describe("defaultValues - 动火工具选项", () => {
  it("应该包含所有必要的动火工具选项", () => {
    expect(dhOptions).toBeDefined();
    expect(Array.isArray(dhOptions)).toBe(true);
    expect(dhOptions.length).toBeGreaterThan(0);
  });

  it("每个动火工具选项应该有正确的结构", () => {
    dhOptions.forEach((option) => {
      expect(option).toHaveProperty("label");
      expect(option).toHaveProperty("id");
      expect(typeof option.label).toBe("string");
      expect(typeof option.id).toBe("number");
    });
  });

  it("应该包含关键的动火工具", () => {
    const labels = dhOptions.map((option) => option.label);

    expect(labels).toContain("电焊");
    expect(labels).toContain("气焊(割)");
    expect(labels).toContain("氧乙炔");
    expect(labels).toContain("氩弧焊");
  });

  it("动火工具ID应该是唯一的", () => {
    const ids = dhOptions.map((option) => option.id);
    const uniqueIds = [...new Set(ids)];
    expect(ids.length).toBe(uniqueIds.length);
  });
});

describe("defaultValues - 作业级别选项", () => {
  it("应该包含所有作业级别选项", () => {
    expect(operationLevelOptions).toBeDefined();
    expect(Array.isArray(operationLevelOptions)).toBe(true);
    expect(operationLevelOptions.length).toBe(4);
  });

  it("应该包含正确的作业级别", () => {
    const labels = operationLevelOptions.map((option) => option.label);

    expect(labels).toContain("-");
    expect(labels).toContain("特级");
    expect(labels).toContain("一级");
    expect(labels).toContain("二级");
  });

  it("作业级别应该有正确的ID顺序", () => {
    expect(operationLevelOptions[0]).toEqual({ label: "-", id: 0 });
    expect(operationLevelOptions[1]).toEqual({ label: "特级", id: 1 });
    expect(operationLevelOptions[2]).toEqual({ label: "一级", id: 2 });
    expect(operationLevelOptions[3]).toEqual({ label: "二级", id: 3 });
  });
});

describe("defaultValues - 高处作业级别选项", () => {
  it("应该包含所有高处作业级别选项", () => {
    expect(highPlaceOperationLevelOptions).toBeDefined();
    expect(Array.isArray(highPlaceOperationLevelOptions)).toBe(true);
    expect(highPlaceOperationLevelOptions.length).toBe(5);
  });

  it("应该包含正确的高处作业级别", () => {
    const labels = highPlaceOperationLevelOptions.map((option) => option.label);

    expect(labels).toContain("-");
    expect(labels).toContain("一级");
    expect(labels).toContain("二级");
    expect(labels).toContain("三级");
    expect(labels).toContain("四级");
  });
});

describe("defaultValues - 吊装作业级别选项", () => {
  it("应该包含所有吊装作业级别选项", () => {
    expect(liftingOperationLevelOptions).toBeDefined();
    expect(Array.isArray(liftingOperationLevelOptions)).toBe(true);
    expect(liftingOperationLevelOptions.length).toBe(4);
  });

  it("应该包含正确的吊装作业级别", () => {
    const labels = liftingOperationLevelOptions.map((option) => option.label);

    expect(labels).toContain("-");
    expect(labels).toContain("一级");
    expect(labels).toContain("二级");
    expect(labels).toContain("三级");
  });
});

describe("defaultValues - 数据完整性验证", () => {
  it("所有选项数组都应该被正确导出", () => {
    const allOptions = [
      protectiveOptions,
      dhOptions,
      operationLevelOptions,
      highPlaceOperationLevelOptions,
      liftingOperationLevelOptions,
    ];

    allOptions.forEach((options) => {
      expect(options).toBeDefined();
      expect(Array.isArray(options)).toBe(true);
      expect(options.length).toBeGreaterThan(0);
    });
  });

  it("所有选项都应该有一致的数据结构", () => {
    const allOptions = [
      protectiveOptions,
      dhOptions,
      operationLevelOptions,
      highPlaceOperationLevelOptions,
      liftingOperationLevelOptions,
    ];

    allOptions.forEach((options) => {
      options.forEach((option) => {
        expect(option).toHaveProperty("label");
        expect(option).toHaveProperty("id");
        expect(typeof option.label).toBe("string");
        expect(typeof option.id).toBe("number");
        expect(option.label.length).toBeGreaterThan(0);
        expect(option.id).toBeGreaterThanOrEqual(0);
      });
    });
  });

  it("所有选项的ID都应该是非负整数", () => {
    const allOptions = [
      protectiveOptions,
      dhOptions,
      operationLevelOptions,
      highPlaceOperationLevelOptions,
      liftingOperationLevelOptions,
    ];

    allOptions.forEach((options) => {
      options.forEach((option) => {
        expect(Number.isInteger(option.id)).toBe(true);
        expect(option.id).toBeGreaterThanOrEqual(0);
      });
    });
  });

  it("所有选项的标签都应该是非空字符串", () => {
    const allOptions = [
      protectiveOptions,
      dhOptions,
      operationLevelOptions,
      highPlaceOperationLevelOptions,
      liftingOperationLevelOptions,
    ];

    allOptions.forEach((options) => {
      options.forEach((option) => {
        expect(typeof option.label).toBe("string");
        expect(option.label.trim().length).toBeGreaterThan(0);
      });
    });
  });
});

describe("defaultValues - 边界情况测试", () => {
  it("应该正确处理空选项情况", () => {
    // 测试如果某个选项数组为空的情况
    const emptyOptions: any[] = [];
    expect(Array.isArray(emptyOptions)).toBe(true);
    expect(emptyOptions.length).toBe(0);
  });

  it("应该正确处理特殊字符", () => {
    // 检查是否有包含特殊字符的选项
    const allOptions = [protectiveOptions, dhOptions, operationLevelOptions];

    allOptions.forEach((options) => {
      options.forEach((option) => {
        // 标签可以包含中文、英文、数字、括号、斜杠等
        expect(option.label).toMatch(/^[\u4e00-\u9fa5a-zA-Z0-9\s\(\)\/\-]+$/);
      });
    });
  });
});
