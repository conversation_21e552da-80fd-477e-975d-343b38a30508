import { describe, expect, it } from "vitest";
import {
  STORAGE_KEY,
  component,
  componentGroup,
  groupName,
  inputJoinMap,
  radioJoinMap,
  selectJoinMap,
} from "../defaultFormConfig";

describe("defaultFormConfig - 配置管理层核心测试", () => {
  describe("常量配置测试", () => {
    it("应该正确导出存储键", () => {
      expect(STORAGE_KEY).toBe("container_data");
      expect(typeof STORAGE_KEY).toBe("string");
    });

    it("应该正确导出组名映射", () => {
      expect(groupName).toEqual({
        base: "表单控件",
        business: "系统控件",
        layout: "布局控件",
      });
      expect(Object.keys(groupName)).toHaveLength(3);
    });
  });

  describe("业务映射配置测试", () => {
    it("应该正确配置选择器业务映射", () => {
      expect(selectJoinMap).toBeDefined();
      expect(typeof selectJoinMap).toBe("object");

      // 验证必要的业务类型存在
      expect(selectJoinMap.protective).toBeDefined();
      expect(selectJoinMap.dh).toBeDefined();
      expect(selectJoinMap.unit).toBeDefined();
      expect(selectJoinMap.workArea).toBeDefined();

      // 验证选项结构
      expect(selectJoinMap.protective.options).toBeDefined();
      expect(Array.isArray(selectJoinMap.protective.options)).toBe(true);
      expect(selectJoinMap.dh.options).toBeDefined();
      expect(Array.isArray(selectJoinMap.dh.options)).toBe(true);

      // 验证无选项标记
      expect(selectJoinMap.unit.noOption).toBe(true);
      expect(selectJoinMap.workArea.noOption).toBe(true);
    });

    it("应该正确配置单选框业务映射", () => {
      expect(radioJoinMap).toBeDefined();
      expect(typeof radioJoinMap).toBe("object");

      // 验证必要的业务类型存在
      expect(radioJoinMap.level).toBeDefined();
      expect(radioJoinMap.unitCategory).toBeDefined();

      // 验证选项结构
      expect(radioJoinMap.level.options).toBeDefined();
      expect(Array.isArray(radioJoinMap.level.options)).toBe(true);
      expect(radioJoinMap.unitCategory.options).toBeDefined();
      expect(Array.isArray(radioJoinMap.unitCategory.options)).toBe(true);
    });

    it("应该正确配置输入框业务映射", () => {
      expect(inputJoinMap).toBeDefined();
      expect(typeof inputJoinMap).toBe("object");

      // 验证必要的业务类型存在
      expect(inputJoinMap.guardianCertificate).toBeDefined();
      expect(inputJoinMap.guardianInCharge).toBeDefined();

      // 验证选项结构（输入框通常为空数组）
      expect(inputJoinMap.guardianCertificate.options).toBeDefined();
      expect(Array.isArray(inputJoinMap.guardianCertificate.options)).toBe(
        true
      );
      expect(inputJoinMap.guardianInCharge.options).toBeDefined();
      expect(Array.isArray(inputJoinMap.guardianInCharge.options)).toBe(true);
    });
  });

  describe("组件配置测试", () => {
    it("应该正确定义组件列表", () => {
      expect(component).toBeDefined();
      expect(Array.isArray(component)).toBe(true);
      expect(component.length).toBeGreaterThan(0);
    });

    it("应该包含所有基础组件类型", () => {
      const componentTypes = component.map((c) => c.compType);

      // 验证基础组件类型存在
      expect(componentTypes).toContain("plainText");
      expect(componentTypes).toContain("input");
      expect(componentTypes).toContain("textarea");
      expect(componentTypes).toContain("selector");
      expect(componentTypes).toContain("datePicker");
      expect(componentTypes).toContain("radio");
      expect(componentTypes).toContain("employeePicker");
      expect(componentTypes).toContain("annexFilePicker");
      expect(componentTypes).toContain("annexImgPicker");
      expect(componentTypes).toContain("table");
    });

    it("应该为每个组件正确设置必需属性", () => {
      component.forEach((comp, index) => {
        // 验证必需属性存在
        expect(
          comp.compName,
          `组件索引 ${index} - compName 验证失败`
        ).toBeDefined();
        expect(
          comp.compType,
          `组件索引 ${index} - compType 验证失败`
        ).toBeDefined();
        expect(comp.group, `组件索引 ${index} - group 验证失败`).toBeDefined();
        expect(
          comp.formData,
          `组件索引 ${index} - formData 验证失败`
        ).toBeDefined();

        // 验证属性类型
        expect(
          typeof comp.compName,
          `组件索引 ${index} - compName 类型验证失败`
        ).toBe("string");
        expect(
          typeof comp.compType,
          `组件索引 ${index} - compType 类型验证失败`
        ).toBe("string");
        expect(
          typeof comp.group,
          `组件索引 ${index} - group 类型验证失败`
        ).toBe("string");
        expect(
          typeof comp.formData,
          `组件索引 ${index} - formData 类型验证失败`
        ).toBe("object");

        // 验证组件名称不为空
        expect(
          comp.compName.length,
          `组件索引 ${index} - compName 长度验证失败`
        ).toBeGreaterThan(0);
        expect(
          comp.compType.length,
          `组件索引 ${index} - compType 长度验证失败`
        ).toBeGreaterThan(0);

        // 验证默认表单数据包含必需字段
        expect(comp.formData.isReq, `组件索引 ${index} - isReq 验证失败`).toBe(
          "required"
        );
      });
    });

    it("应该正确设置组件分组", () => {
      const groups = [...new Set(component.map((c) => c.group))];

      // 验证所有组件都属于已定义的分组
      groups.forEach((group) => {
        expect(groupName[group]).toBeDefined();
      });

      // 验证主要分组存在
      expect(groups).toContain("base");
    });

    it("应该为特定组件设置正确的业务类型", () => {
      const fileComponent = component.find(
        (c) => c.compType === "annexFilePicker"
      );
      const imgComponent = component.find(
        (c) => c.compType === "annexImgPicker"
      );

      expect(fileComponent?.business).toBe("file");
      expect(imgComponent?.business).toBe("img");
    });
  });

  describe("组件分组测试", () => {
    it("应该正确生成组件分组", () => {
      expect(componentGroup).toBeDefined();
      expect(Array.isArray(componentGroup)).toBe(true);
      expect(componentGroup.length).toBeGreaterThan(0);
    });

    it("应该按组正确分组组件", () => {
      // 验证分组结构
      componentGroup.forEach((group) => {
        expect(Array.isArray(group)).toBe(true);
        expect(group.length).toBeGreaterThan(0);

        // 验证同一分组内的组件具有相同的group属性
        const groupType = group[0].group;
        group.forEach((comp) => {
          expect(comp.group).toBe(groupType);
        });
      });
    });

    it("应该包含所有原始组件", () => {
      const flattenedComponents = componentGroup.flat();
      expect(flattenedComponents.length).toBe(component.length);

      // 验证所有组件都被包含
      component.forEach((originalComp) => {
        const found = flattenedComponents.find(
          (comp) => comp.compType === originalComp.compType
        );
        expect(found).toBeDefined();
      });
    });
  });

  describe("配置完整性测试", () => {
    it("应该确保所有导出项都已定义", () => {
      expect(STORAGE_KEY).toBeDefined();
      expect(selectJoinMap).toBeDefined();
      expect(radioJoinMap).toBeDefined();
      expect(inputJoinMap).toBeDefined();
      expect(component).toBeDefined();
      expect(componentGroup).toBeDefined();
      expect(groupName).toBeDefined();
    });

    it("应该确保配置数据结构一致性", () => {
      // 验证业务映射的一致性
      Object.values(selectJoinMap).forEach((mapping) => {
        if (!mapping.noOption) {
          expect(mapping.options).toBeDefined();
          expect(Array.isArray(mapping.options)).toBe(true);
        }
      });

      Object.values(radioJoinMap).forEach((mapping) => {
        expect(mapping.options).toBeDefined();
        expect(Array.isArray(mapping.options)).toBe(true);
      });

      Object.values(inputJoinMap).forEach((mapping) => {
        expect(mapping.options).toBeDefined();
        expect(Array.isArray(mapping.options)).toBe(true);
      });
    });
  });

  describe("边界条件测试", () => {
    it("应该处理空组件列表", () => {
      const emptyComponents: any[] = [];
      expect(() => {
        // 模拟空组件列表的分组操作
        const emptyGroup = emptyComponents.reduce((acc, comp) => {
          const group = acc.find((g: any[]) => g[0]?.group === comp.group);
          if (group) {
            group.push(comp);
          } else {
            acc.push([comp]);
          }
          return acc;
        }, []);
        expect(emptyGroup).toEqual([]);
      }).not.toThrow();
    });

    it("应该验证组件类型唯一性", () => {
      const componentTypes = component.map((c) => c.compType);
      const uniqueTypes = [...new Set(componentTypes)];

      expect(componentTypes.length).toBe(uniqueTypes.length);
    });
  });
});
