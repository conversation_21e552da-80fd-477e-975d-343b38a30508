import { fireEvent, render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { FormConfigPage } from "../formConfig";

// Mock Semi UI components
vi.mock("@douyinfe/semi-ui", async () => {
  const actual = await vi.importActual("@douyinfe/semi-ui");

  const MockInput = ({ field, label, rules, ...props }: any) => (
    <div data-testid={`form-input-${field}`}>
      <label>{label}</label>
      <input {...props} data-testid={`input-${field}`} />
    </div>
  );

  const MockForm = ({ children }: any) => (
    <form data-testid="form">{children}</form>
  );
  MockForm.Input = MockInput;

  return {
    ...actual,
    Form: MockForm,
    Button: ({ children, onClick, ...props }: any) => (
      <button onClick={onClick} {...props} data-testid="button">
        {children}
      </button>
    ),
    Col: ({ children, span }: any) => (
      <div data-testid="col" data-span={span}>
        {children}
      </div>
    ),
    Row: ({ children }: any) => <div data-testid="row">{children}</div>,
    Divider: () => <hr data-testid="divider" />,
  };
});

// Mock react-sortablejs
vi.mock("react-sortablejs", () => ({
  ReactSortable: ({ children, list, setList }: any) => (
    <div data-testid="sortable-container">{children}</div>
  ),
}));

// Mock ramda functions
vi.mock("ramda", () => ({
  clone: vi.fn((obj) => JSON.parse(JSON.stringify(obj))),
  range: vi.fn((start, end) =>
    Array.from({ length: end - start }, (_, i) => start + i)
  ),
}));

// Mock uuid
vi.mock("uuid", () => ({
  v4: vi.fn(() => "mock-uuid-123"),
}));

describe("FormConfigPage", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该正确渲染表单配置组件", () => {
    render(<FormConfigPage />);

    // 检查基本结构 - 应该有多个row元素（主row和form内的row）
    const rows = screen.getAllByTestId("row");
    expect(rows.length).toBeGreaterThanOrEqual(1);

    // 检查列结构 - 应该有至少4个col元素（3个主列 + form内的列）
    const cols = screen.getAllByTestId("col");
    expect(cols.length).toBeGreaterThanOrEqual(3);
  });

  it("应该显示组件库面板", () => {
    render(<FormConfigPage />);

    // 检查基础组件标题
    expect(screen.getByText("基础组件")).toBeInTheDocument();

    // 检查布局方式标题
    expect(screen.getByText("布局方式")).toBeInTheDocument();

    // 检查按钮文本（实际的按钮文本）
    expect(screen.getByText("添加输入框")).toBeInTheDocument();
    expect(screen.getByText("添加多行输入")).toBeInTheDocument();
    expect(screen.getByText("加一列")).toBeInTheDocument();
    expect(screen.getByText("减一列")).toBeInTheDocument();
  });

  it("应该显示表单预览区域", () => {
    render(<FormConfigPage />);

    // 检查表单预览区域 - 应该有form元素
    expect(screen.getByTestId("form")).toBeInTheDocument();

    // 检查初始列显示（默认显示一列，索引为0）
    expect(screen.getByText("0")).toBeInTheDocument();
  });

  it("应该显示属性配置面板", () => {
    render(<FormConfigPage />);

    // 检查属性配置面板
    expect(screen.getByText("额外属性")).toBeInTheDocument();
  });

  it("应该正确处理列数调整", () => {
    render(<FormConfigPage />);

    // 检查列数调整按钮
    const addButton = screen.getByText("加一列");
    const removeButton = screen.getByText("减一列");

    expect(addButton).toBeInTheDocument();
    expect(removeButton).toBeInTheDocument();

    // 测试点击加一列
    fireEvent.click(addButton);

    // 测试点击减一列
    fireEvent.click(removeButton);
  });

  it("应该正确处理组件拖拽", () => {
    render(<FormConfigPage />);

    // 先添加一个输入框组件，这样才会有拖拽容器
    const addInputButton = screen.getByText("添加输入框");
    fireEvent.click(addInputButton);

    // 现在应该有拖拽容器了
    expect(screen.getByTestId("sortable-container")).toBeInTheDocument();
  });

  it("应该正确渲染表单项", () => {
    render(<FormConfigPage />);

    // 先添加一个输入框组件
    const addInputButton = screen.getByText("添加输入框");
    fireEvent.click(addInputButton);

    // 现在应该有拖拽容器和表单项
    expect(screen.getByTestId("sortable-container")).toBeInTheDocument();

    // 检查是否渲染了表单输入项
    expect(screen.getByTestId("form-input-mock-uuid-123")).toBeInTheDocument();
  });

  it("应该处理表单项创建", () => {
    render(<FormConfigPage />);

    // 检查组件库中的按钮（实际的文本是"添加输入框"）
    const addInputButton = screen.getByText("添加输入框");
    expect(addInputButton).toBeInTheDocument();

    // 模拟点击添加按钮
    fireEvent.click(addInputButton);

    // 验证表单项被创建
    expect(screen.getByTestId("sortable-container")).toBeInTheDocument();
  });

  it("应该正确处理不同的组件类型", () => {
    render(<FormConfigPage />);

    // 检查实际存在的组件按钮（根据FormConfigPage的实际实现）
    expect(screen.getByText("添加输入框")).toBeInTheDocument();
    expect(screen.getByText("添加多行输入")).toBeInTheDocument();

    // 检查布局控制按钮
    expect(screen.getByText("加一列")).toBeInTheDocument();
    expect(screen.getByText("减一列")).toBeInTheDocument();
  });

  it("应该正确处理布局配置", () => {
    render(<FormConfigPage />);

    // 检查布局相关的控件
    const addColumnBtn = screen.getByText("加一列");
    const removeColumnBtn = screen.getByText("减一列");

    expect(addColumnBtn).toBeInTheDocument();
    expect(removeColumnBtn).toBeInTheDocument();
  });

  it("应该正确处理表单验证", () => {
    render(<FormConfigPage />);

    // 先添加一个输入框组件
    const addInputButton = screen.getByText("添加输入框");
    fireEvent.click(addInputButton);

    // 现在检查表单结构存在，验证逻辑会在实际使用中触发
    expect(screen.getByTestId("sortable-container")).toBeInTheDocument();

    // 检查表单输入项是否有验证规则
    expect(screen.getByTestId("form-input-mock-uuid-123")).toBeInTheDocument();
  });

  it("应该正确处理组件属性配置", () => {
    render(<FormConfigPage />);

    // 检查属性配置区域
    expect(screen.getByText("额外属性")).toBeInTheDocument();
  });

  it("应该正确处理表单数据结构", () => {
    render(<FormConfigPage />);

    // 检查基本结构渲染正确 - 应该有多个row元素
    const rows = screen.getAllByTestId("row");
    expect(rows.length).toBeGreaterThanOrEqual(1);

    // 检查列结构 - 应该有至少4个col元素（3个主列 + form内的列）
    const cols = screen.getAllByTestId("col");
    expect(cols.length).toBeGreaterThanOrEqual(3);
  });

  it("应该正确处理组件排序", () => {
    render(<FormConfigPage />);

    // 先添加一个输入框组件
    const addInputButton = screen.getByText("添加输入框");
    fireEvent.click(addInputButton);

    // 现在检查排序容器存在
    expect(screen.getByTestId("sortable-container")).toBeInTheDocument();
  });

  it("应该正确处理响应式布局", () => {
    render(<FormConfigPage />);

    // 检查列的span属性设置
    const cols = screen.getAllByTestId("col");

    // 主布局应该有3个主要列：左侧(4) + 中间(16) + 右侧(4)
    // 但form内部也会有col元素，所以我们需要检查前3个是主布局的
    expect(cols.length).toBeGreaterThanOrEqual(3);

    // 检查主布局的列（前3个应该是主布局的列）
    expect(cols[0]).toHaveAttribute("data-span", "4"); // 左侧面板
    expect(cols[1]).toHaveAttribute("data-span", "16"); // 中间预览区

    // 找到span为4的第二个元素（右侧面板）
    const span4Cols = Array.from(cols).filter(
      (col) => col.getAttribute("data-span") === "4"
    );
    expect(span4Cols.length).toBeGreaterThanOrEqual(2); // 左侧和右侧都是span=4
  });
});
