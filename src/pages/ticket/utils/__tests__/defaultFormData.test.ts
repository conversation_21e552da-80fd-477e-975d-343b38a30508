import { describe, expect, it } from "vitest";
import defaultFormData from "../defaultFormData";

describe("defaultFormData", () => {
  describe("数据结构验证", () => {
    it("应该导出一个数组", () => {
      expect(Array.isArray(defaultFormData)).toBe(true);
      expect(defaultFormData).toHaveLength(2);
    });

    it("应该包含正确的基础字段", () => {
      defaultFormData.forEach((item, index) => {
        expect(item).toHaveProperty("compId");
        expect(item).toHaveProperty("compName");
        expect(item).toHaveProperty("compType");
        expect(item).toHaveProperty("chosen");
        expect(item).toHaveProperty("selected");
        expect(item).toHaveProperty("itemId");
        expect(item).toHaveProperty("parentId");
        expect(item).toHaveProperty("nodeIndex");
        expect(item).toHaveProperty("business");
        expect(item).toHaveProperty("formData");
        expect(item).toHaveProperty("children");

        // 验证字段类型
        expect(typeof item.compId).toBe("string");
        expect(typeof item.compName).toBe("string");
        expect(typeof item.compType).toBe("string");
        expect(typeof item.chosen).toBe("boolean");
        expect(typeof item.selected).toBe("boolean");
        expect(typeof item.itemId).toBe("string");
        expect(item.parentId).toBeNull();
        expect(typeof item.nodeIndex).toBe("number");
        expect(typeof item.business).toBe("string");
        expect(typeof item.formData).toBe("object");
        expect(Array.isArray(item.children)).toBe(true);
      });
    });
  });

  describe("第一个元素 - 作业内容", () => {
    const workContentItem = defaultFormData[0];

    it("应该有正确的基础配置", () => {
      expect(workContentItem.compId).toBe("10-1");
      expect(workContentItem.compName).toBe("作业内容");
      expect(workContentItem.compType).toBe("input");
      expect(workContentItem.chosen).toBe(false);
      expect(workContentItem.selected).toBe(true);
      expect(workContentItem.itemId).toBe("formId-content");
      expect(workContentItem.parentId).toBeNull();
      expect(workContentItem.nodeIndex).toBe(0);
      expect(workContentItem.business).toBe("workContent");
      expect(workContentItem.children).toEqual([]);
    });

    it("应该有正确的formData配置", () => {
      const { formData } = workContentItem;
      
      expect(formData.formName).toBe("作业内容");
      expect(formData.isPrint).toBe(true);
      expect(formData.isReq).toBe("not");
      expect(formData.disabled).toBe(true);
      expect(formData.placeHolder).toBeNull();
      expect(formData.business).toBe("workContent");
    });
  });

  describe("第二个元素 - 作业区域", () => {
    const workAreaItem = defaultFormData[1];

    it("应该有正确的基础配置", () => {
      expect(workAreaItem.compId).toBe("10-2");
      expect(workAreaItem.compName).toBe("作业区域");
      expect(workAreaItem.compType).toBe("selector");
      expect(workAreaItem.chosen).toBe(false);
      expect(workAreaItem.selected).toBe(true);
      expect(workAreaItem.itemId).toBe("formId-area");
      expect(workAreaItem.parentId).toBeNull();
      expect(workAreaItem.nodeIndex).toBe(1);
      expect(workAreaItem.business).toBe("workArea");
      expect(workAreaItem.children).toEqual([]);
    });

    it("应该有正确的formData配置", () => {
      const { formData } = workAreaItem;
      
      expect(formData.formName).toBe("作业区域");
      expect(formData.isPrint).toBe(true);
      expect(formData.isReq).toBe("not");
      expect(formData.disabled).toBe(true);
      expect(formData.placeHolder).toBeNull();
      expect(formData.business).toBe("workArea");
    });
  });

  describe("数据一致性验证", () => {
    it("business字段应该与formData.business一致", () => {
      defaultFormData.forEach((item) => {
        expect(item.business).toBe(item.formData.business);
      });
    });

    it("compName应该与formData.formName一致", () => {
      defaultFormData.forEach((item) => {
        expect(item.compName).toBe(item.formData.formName);
      });
    });

    it("nodeIndex应该按顺序递增", () => {
      defaultFormData.forEach((item, index) => {
        expect(item.nodeIndex).toBe(index);
      });
    });

    it("所有元素都应该是选中状态", () => {
      defaultFormData.forEach((item) => {
        expect(item.selected).toBe(true);
      });
    });

    it("所有元素都应该是未选择状态", () => {
      defaultFormData.forEach((item) => {
        expect(item.chosen).toBe(false);
      });
    });

    it("所有元素都应该没有父级", () => {
      defaultFormData.forEach((item) => {
        expect(item.parentId).toBeNull();
      });
    });

    it("所有元素都应该没有子级", () => {
      defaultFormData.forEach((item) => {
        expect(item.children).toEqual([]);
      });
    });
  });

  describe("formData通用配置验证", () => {
    it("所有元素都应该启用打印", () => {
      defaultFormData.forEach((item) => {
        expect(item.formData.isPrint).toBe(true);
      });
    });

    it("所有元素都应该是非必填", () => {
      defaultFormData.forEach((item) => {
        expect(item.formData.isReq).toBe("not");
      });
    });

    it("所有元素都应该是禁用状态", () => {
      defaultFormData.forEach((item) => {
        expect(item.formData.disabled).toBe(true);
      });
    });

    it("所有元素的placeHolder都应该为null", () => {
      defaultFormData.forEach((item) => {
        expect(item.formData.placeHolder).toBeNull();
      });
    });
  });

  describe("业务字段验证", () => {
    it("应该包含预期的业务字段", () => {
      const businessFields = defaultFormData.map(item => item.business);
      expect(businessFields).toContain("workContent");
      expect(businessFields).toContain("workArea");
    });

    it("业务字段应该是唯一的", () => {
      const businessFields = defaultFormData.map(item => item.business);
      const uniqueBusinessFields = [...new Set(businessFields)];
      expect(businessFields).toHaveLength(uniqueBusinessFields.length);
    });
  });

  describe("组件类型验证", () => {
    it("应该包含预期的组件类型", () => {
      const compTypes = defaultFormData.map(item => item.compType);
      expect(compTypes).toContain("input");
      expect(compTypes).toContain("selector");
    });

    it("第一个元素应该是input类型", () => {
      expect(defaultFormData[0].compType).toBe("input");
    });

    it("第二个元素应该是selector类型", () => {
      expect(defaultFormData[1].compType).toBe("selector");
    });
  });
});
