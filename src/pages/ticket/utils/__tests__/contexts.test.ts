import React from "react";
import { describe, expect, it } from "vitest";
import { FormContext } from "../contexts";

describe("contexts", () => {
  describe("FormContext", () => {
    it("应该是一个React Context对象", () => {
      expect(FormContext).toBeDefined();
      expect(FormContext.Provider).toBeDefined();
      expect(FormContext.Consumer).toBeDefined();
    });

    it("应该有正确的默认值", () => {
      // 通过创建一个Consumer来测试默认值
      let contextValue: any;

      const TestComponent = () => {
        return React.createElement(FormContext.Consumer, {}, (value) => {
          contextValue = value;
          return null;
        });
      };

      // 在没有Provider的情况下渲染Consumer
      React.createElement(TestComponent);

      // 验证默认值为null
      expect(FormContext._currentValue).toBe(null);
    });

    it("应该能够提供和消费值", () => {
      const testValue = { formData: { test: "value" } };
      let receivedValue: any;

      const Consumer = () => {
        return React.createElement(FormContext.Consumer, {}, (value) => {
          receivedValue = value;
          return null;
        });
      };

      const Provider = () => {
        return React.createElement(
          FormContext.Provider,
          { value: testValue },
          React.createElement(Consumer)
        );
      };

      // 创建Provider组件
      React.createElement(Provider);

      // 验证Context能够正常工作
      expect(FormContext).toHaveProperty("Provider");
      expect(FormContext).toHaveProperty("Consumer");
    });

    it("应该有正确的Context结构", () => {
      // React Context的基本结构验证
      expect(FormContext).toHaveProperty("Provider");
      expect(FormContext).toHaveProperty("Consumer");
      expect(typeof FormContext.Provider).toBe("object");
      expect(typeof FormContext.Consumer).toBe("object");
    });
  });
});
