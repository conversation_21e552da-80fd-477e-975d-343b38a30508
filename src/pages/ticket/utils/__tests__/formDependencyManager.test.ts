import { ItemType } from "../../../../types/sendbox";
import { DependencyManager } from "../formDependencyManager";

// Mock form fields for testing
const mockFormFields: ItemType[] = [
  {
    compId: "1",
    itemId: "field1",
    compName: "作业单位类别",
    compType: "radio",
    business: "unitCategory",
    group: "base",
    formData: {
      formName: "作业单位类别",
      candidateList: [
        { id: 1, label: "本厂" },
        { id: 2, label: "承包商" },
      ],
    },
    children: [],
  },
  {
    compId: "2",
    itemId: "field2",
    compName: "MOC登记号",
    compType: "input",
    business: "mocNumber",
    group: "base",
    formData: {
      formName: "MOC登记号",
      dependent: "field1",
      dependentValue: 2,
    },
    children: [],
  },
  {
    compId: "3",
    itemId: "field3",
    compName: "普通字段",
    compType: "input",
    business: "normal",
    group: "base",
    formData: {
      formName: "普通字段",
    },
    children: [],
  },
];

describe("DependencyManager", () => {
  let manager: DependencyManager;

  beforeEach(() => {
    manager = new DependencyManager(mockFormFields);
  });

  describe("构建依赖映射", () => {
    test("应该正确构建依赖关系映射", () => {
      const dependentFields = manager.getDependentFields("field1");
      expect(dependentFields).toContain("field2");
    });
  });

  describe("字段可见性判断", () => {
    test("无依赖的字段应该默认显示", () => {
      expect(manager.shouldShowField("field1")).toBe(true);
      expect(manager.shouldShowField("field3")).toBe(true);
    });

    test("有依赖但未满足条件的字段应该隐藏", () => {
      expect(manager.shouldShowField("field2")).toBe(false);
    });

    test("有依赖且满足条件的字段应该显示", () => {
      manager.updateFieldValue("field1", 2);
      expect(manager.shouldShowField("field2")).toBe(true);
    });

    test("应该处理字符串和数字类型的值比较", () => {
      manager.updateFieldValue("field1", "2");
      expect(manager.shouldShowField("field2")).toBe(true);
    });
  });

  describe("字段值更新", () => {
    test("更新字段值应该返回受影响的字段", () => {
      const affectedFields = manager.updateFieldValue("field1", 2);
      expect(affectedFields).toContain("field2");
    });

    test("批量更新字段值应该返回所有受影响的字段", () => {
      const affectedFields = manager.batchUpdateFieldValues({
        field1: 2,
        field3: "test",
      });
      expect(affectedFields).toContain("field2");
    });
  });

  describe("获取隐藏和可见字段", () => {
    test("应该正确返回隐藏的字段列表", () => {
      const hiddenFields = manager.getHiddenFields();
      expect(hiddenFields).toContain("field2");
    });

    test("应该正确返回可见的字段列表", () => {
      const visibleFields = manager.getVisibleFields();
      expect(visibleFields).toContain("field1");
      expect(visibleFields).toContain("field3");
      expect(visibleFields).not.toContain("field2");
    });

    test("满足依赖条件后字段应该变为可见", () => {
      manager.updateFieldValue("field1", 2);
      const visibleFields = manager.getVisibleFields();
      expect(visibleFields).toContain("field2");
    });
  });

  describe("依赖关系验证", () => {
    test("有效的依赖配置应该通过验证", () => {
      const validation = manager.validateDependencyConfig();
      expect(validation.valid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test("依赖不存在的字段应该返回错误", () => {
      const invalidFields: ItemType[] = [
        {
          compId: "1",
          itemId: "field1",
          compName: "测试字段",
          compType: "input",
          business: "test",
          group: "base",
          formData: {
            formName: "测试字段",
            dependent: "nonexistent",
            dependentValue: 1,
          },
          children: [],
        },
      ];

      const invalidManager = new DependencyManager(invalidFields);
      const validation = invalidManager.validateDependencyConfig();
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]).toContain("不存在");
    });

    test("依赖值不在候选列表中应该返回错误", () => {
      const invalidFields: ItemType[] = [
        {
          compId: "1",
          itemId: "field1",
          compName: "单选字段",
          compType: "radio",
          business: "radio",
          group: "base",
          formData: {
            formName: "单选字段",
            candidateList: [
              { id: 1, label: "选项1" },
              { id: 2, label: "选项2" },
            ],
          },
          children: [],
        },
        {
          compId: "2",
          itemId: "field2",
          compName: "依赖字段",
          compType: "input",
          business: "dependent",
          group: "base",
          formData: {
            formName: "依赖字段",
            dependent: "field1",
            dependentValue: 999, // 不存在的值
          },
          children: [],
        },
      ];

      const invalidManager = new DependencyManager(invalidFields);
      const validation = invalidManager.validateDependencyConfig();
      expect(validation.valid).toBe(false);
      expect(validation.errors.length).toBeGreaterThan(0);
      expect(validation.errors[0]).toContain("选项中不存在");
    });
  });

  describe("嵌套表单结构处理", () => {
    test("应该正确处理嵌套的表单结构", () => {
      const nestedFields: ItemType[] = [
        {
          compId: "1",
          itemId: "parent",
          compName: "父级字段",
          compType: "wrap",
          business: "parent",
          group: "layout",
          formData: {},
          children: [
            {
              compId: "2",
              itemId: "child1",
              compName: "子字段1",
              compType: "radio",
              business: "child1",
              group: "base",
              formData: {
                formName: "子字段1",
                candidateList: [
                  { id: 1, label: "选项1" },
                  { id: 2, label: "选项2" },
                ],
              },
              children: [],
            },
            {
              compId: "3",
              itemId: "child2",
              compName: "子字段2",
              compType: "input",
              business: "child2",
              group: "base",
              formData: {
                formName: "子字段2",
                dependent: "child1",
                dependentValue: 1,
              },
              children: [],
            },
          ],
        },
      ];

      const nestedManager = new DependencyManager(nestedFields);

      // 验证嵌套字段被正确扁平化
      expect(nestedManager.shouldShowField("child1")).toBe(true);
      expect(nestedManager.shouldShowField("child2")).toBe(false);

      // 验证依赖关系在嵌套结构中正常工作
      nestedManager.updateFieldValue("child1", 1);
      expect(nestedManager.shouldShowField("child2")).toBe(true);
    });
  });

  describe("工具方法", () => {
    test("应该正确获取字段的依赖信息", () => {
      const dependency = manager.getFieldDependency("field2");
      expect(dependency).toEqual({
        dependent: "field1",
        dependentValue: 2,
      });
    });

    test("无依赖字段应该返回null", () => {
      const dependency = manager.getFieldDependency("field1");
      expect(dependency).toBeNull();
    });

    test("应该能够重置字段值", () => {
      manager.updateFieldValue("field1", 2);
      manager.resetFieldValues();
      const allValues = manager.getAllFieldValues();
      expect(allValues.size).toBe(0);
    });

    test("应该能够获取所有字段值", () => {
      manager.updateFieldValue("field1", 2);
      manager.updateFieldValue("field3", "test");
      const allValues = manager.getAllFieldValues();
      expect(allValues.get("field1")).toBe(2);
      expect(allValues.get("field3")).toBe("test");
    });
  });
});
