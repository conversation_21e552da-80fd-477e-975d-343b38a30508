import { describe, expect, it, vi } from "vitest";
import { RemoveObserver, DisposeObserver } from "../observers";
import { Observer } from "@utils/observer";

describe("observers", () => {
  describe("RemoveObserver", () => {
    it("应该是Observer的实例", () => {
      expect(RemoveObserver).toBeInstanceOf(Observer);
    });

    it("应该有正确的初始状态", () => {
      expect(RemoveObserver.subs).toBeDefined();
      expect(Array.isArray(RemoveObserver.subs)).toBe(true);
    });

    it("应该能够添加观察者", () => {
      const mockCallback = vi.fn();
      const initialLength = RemoveObserver.subs.length;
      
      RemoveObserver.watch(mockCallback);
      
      expect(RemoveObserver.subs).toHaveLength(initialLength + 1);
      expect(RemoveObserver.subs).toContain(mockCallback);
    });

    it("应该能够通知观察者", () => {
      const mockCallback1 = vi.fn();
      const mockCallback2 = vi.fn();
      const testData = { id: "test", action: "remove" };
      
      // 清空现有观察者
      RemoveObserver.destroy();
      
      // 添加观察者
      RemoveObserver.watch(mockCallback1);
      RemoveObserver.watch(mockCallback2);
      
      // 通知观察者
      RemoveObserver.notify(testData);
      
      expect(mockCallback1).toHaveBeenCalledWith(testData);
      expect(mockCallback2).toHaveBeenCalledWith(testData);
      expect(mockCallback1).toHaveBeenCalledTimes(1);
      expect(mockCallback2).toHaveBeenCalledTimes(1);
    });

    it("应该能够销毁所有观察者", () => {
      const mockCallback = vi.fn();
      
      RemoveObserver.watch(mockCallback);
      expect(RemoveObserver.subs.length).toBeGreaterThan(0);
      
      RemoveObserver.destroy();
      expect(RemoveObserver.subs).toHaveLength(0);
    });

    it("应该能够处理多次通知", () => {
      const mockCallback = vi.fn();
      const testData1 = { id: "test1" };
      const testData2 = { id: "test2" };
      
      RemoveObserver.destroy();
      RemoveObserver.watch(mockCallback);
      
      RemoveObserver.notify(testData1);
      RemoveObserver.notify(testData2);
      
      expect(mockCallback).toHaveBeenCalledTimes(2);
      expect(mockCallback).toHaveBeenNthCalledWith(1, testData1);
      expect(mockCallback).toHaveBeenNthCalledWith(2, testData2);
    });
  });

  describe("DisposeObserver", () => {
    it("应该是Observer的实例", () => {
      expect(DisposeObserver).toBeInstanceOf(Observer);
    });

    it("应该有正确的初始状态", () => {
      expect(DisposeObserver.subs).toBeDefined();
      expect(Array.isArray(DisposeObserver.subs)).toBe(true);
    });

    it("应该能够添加观察者", () => {
      const mockCallback = vi.fn();
      const initialLength = DisposeObserver.subs.length;
      
      DisposeObserver.watch(mockCallback);
      
      expect(DisposeObserver.subs).toHaveLength(initialLength + 1);
      expect(DisposeObserver.subs).toContain(mockCallback);
    });

    it("应该能够通知观察者", () => {
      const mockCallback1 = vi.fn();
      const mockCallback2 = vi.fn();
      const testData = { id: "test", action: "dispose" };
      
      // 清空现有观察者
      DisposeObserver.destroy();
      
      // 添加观察者
      DisposeObserver.watch(mockCallback1);
      DisposeObserver.watch(mockCallback2);
      
      // 通知观察者
      DisposeObserver.notify(testData);
      
      expect(mockCallback1).toHaveBeenCalledWith(testData);
      expect(mockCallback2).toHaveBeenCalledWith(testData);
      expect(mockCallback1).toHaveBeenCalledTimes(1);
      expect(mockCallback2).toHaveBeenCalledTimes(1);
    });

    it("应该能够销毁所有观察者", () => {
      const mockCallback = vi.fn();
      
      DisposeObserver.watch(mockCallback);
      expect(DisposeObserver.subs.length).toBeGreaterThan(0);
      
      DisposeObserver.destroy();
      expect(DisposeObserver.subs).toHaveLength(0);
    });

    it("应该能够处理空参数通知", () => {
      const mockCallback = vi.fn();
      
      DisposeObserver.destroy();
      DisposeObserver.watch(mockCallback);
      
      DisposeObserver.notify(null);
      DisposeObserver.notify(undefined);
      
      expect(mockCallback).toHaveBeenCalledTimes(2);
      expect(mockCallback).toHaveBeenNthCalledWith(1, null);
      expect(mockCallback).toHaveBeenNthCalledWith(2, undefined);
    });
  });

  describe("观察者独立性测试", () => {
    it("RemoveObserver和DisposeObserver应该是独立的实例", () => {
      expect(RemoveObserver).not.toBe(DisposeObserver);
      expect(RemoveObserver.subs).not.toBe(DisposeObserver.subs);
    });

    it("一个观察者的操作不应该影响另一个", () => {
      const removeMockCallback = vi.fn();
      const disposeMockCallback = vi.fn();
      
      // 清空两个观察者
      RemoveObserver.destroy();
      DisposeObserver.destroy();
      
      // 分别添加观察者
      RemoveObserver.watch(removeMockCallback);
      DisposeObserver.watch(disposeMockCallback);
      
      // 通知RemoveObserver
      RemoveObserver.notify({ action: "remove" });
      
      expect(removeMockCallback).toHaveBeenCalledTimes(1);
      expect(disposeMockCallback).not.toHaveBeenCalled();
      
      // 通知DisposeObserver
      DisposeObserver.notify({ action: "dispose" });
      
      expect(removeMockCallback).toHaveBeenCalledTimes(1);
      expect(disposeMockCallback).toHaveBeenCalledTimes(1);
    });

    it("销毁一个观察者不应该影响另一个", () => {
      const removeMockCallback = vi.fn();
      const disposeMockCallback = vi.fn();
      
      // 清空并添加观察者
      RemoveObserver.destroy();
      DisposeObserver.destroy();
      RemoveObserver.watch(removeMockCallback);
      DisposeObserver.watch(disposeMockCallback);
      
      // 销毁RemoveObserver
      RemoveObserver.destroy();
      
      expect(RemoveObserver.subs).toHaveLength(0);
      expect(DisposeObserver.subs).toHaveLength(1);
      
      // DisposeObserver应该仍然工作
      DisposeObserver.notify({ action: "dispose" });
      expect(disposeMockCallback).toHaveBeenCalledTimes(1);
    });
  });

  describe("实际使用场景测试", () => {
    it("应该模拟eventCover组件中的使用场景", () => {
      const removeCallback = vi.fn();
      const disposeCallback = vi.fn();
      
      // 清空观察者
      RemoveObserver.destroy();
      DisposeObserver.destroy();
      
      // 模拟组件注册观察者
      RemoveObserver.watch(removeCallback);
      DisposeObserver.watch(disposeCallback);
      
      // 模拟eventData
      const eventData = {
        id: "event-123",
        isBuiltIn: false,
        name: "测试事件",
      };
      
      // 模拟点击删除按钮
      RemoveObserver.notify(eventData);
      expect(removeCallback).toHaveBeenCalledWith(eventData);
      
      // 模拟点击配置按钮
      DisposeObserver.notify(eventData);
      expect(disposeCallback).toHaveBeenCalledWith(eventData);
    });

    it("应该能够处理多个组件同时使用", () => {
      const component1RemoveCallback = vi.fn();
      const component1DisposeCallback = vi.fn();
      const component2RemoveCallback = vi.fn();
      const component2DisposeCallback = vi.fn();
      
      // 清空观察者
      RemoveObserver.destroy();
      DisposeObserver.destroy();
      
      // 模拟多个组件注册观察者
      RemoveObserver.watch(component1RemoveCallback);
      RemoveObserver.watch(component2RemoveCallback);
      DisposeObserver.watch(component1DisposeCallback);
      DisposeObserver.watch(component2DisposeCallback);
      
      const eventData = { id: "shared-event" };
      
      // 通知所有观察者
      RemoveObserver.notify(eventData);
      DisposeObserver.notify(eventData);
      
      // 所有回调都应该被调用
      expect(component1RemoveCallback).toHaveBeenCalledWith(eventData);
      expect(component2RemoveCallback).toHaveBeenCalledWith(eventData);
      expect(component1DisposeCallback).toHaveBeenCalledWith(eventData);
      expect(component2DisposeCallback).toHaveBeenCalledWith(eventData);
    });
  });
});
