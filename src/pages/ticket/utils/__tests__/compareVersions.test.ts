import { describe, expect, it, vi } from "vitest";
import { compareVersions, type IsUpdate } from "../compareVersions";

// Mock utils
vi.mock("utils", () => ({
  getFormId: vi.fn(() => "mock-form-id-123"),
}));

describe("compareVersions", () => {
  describe("基础功能测试", () => {
    it("应该处理空templates数组", () => {
      const apiTmpl = { templates: [] };
      const result = compareVersions(apiTmpl);
      expect(result).toEqual(apiTmpl);
    });

    it("应该处理undefined templates", () => {
      const apiTmpl = { templates: undefined as any };
      const result = compareVersions(apiTmpl);
      expect(result).toEqual(apiTmpl);
    });

    it("应该处理无效JSON的formTemplate", () => {
      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: "invalid json",
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };
      const result = compareVersions(apiTmpl);
      expect(result).toEqual(apiTmpl);
    });
  });

  describe("版本检测测试", () => {
    it("应该检测到v0版本 - temporaryPowerJobInChargeCertificate", () => {
      const formTemplate = [
        {
          compType: "input",
          business: "temporaryPowerJobInChargeCertificate",
          formData: { formName: "临时用电作业负责人证书" },
        },
      ];

      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: JSON.stringify(formTemplate),
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };

      const result = compareVersions(apiTmpl, true) as IsUpdate;
      expect(result.version).toBe("v0");
      expect(result.isBlock).toBe(true);
      expect(result.msg).toContain("当前版本为v0");
    });

    it("应该检测到v0版本 - guardianCertificate", () => {
      const formTemplate = [
        {
          compType: "input",
          business: "guardianCertificate",
          formData: { formName: "监护人证书" },
        },
      ];

      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: JSON.stringify(formTemplate),
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };

      const result = compareVersions(apiTmpl, true) as IsUpdate;
      expect(result.version).toBe("v0");
      expect(result.isBlock).toBe(true);
    });

    it("应该识别非v0版本", () => {
      const formTemplate = [
        {
          compType: "selector",
          business: "workContent",
          formData: { formName: "作业内容" },
        },
      ];

      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: JSON.stringify(formTemplate),
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };

      const result = compareVersions(apiTmpl);
      expect(result).toEqual(apiTmpl);
    });
  });

  describe("版本升级测试", () => {
    it("应该将v0版本升级到v1 - temporaryPowerJobInChargeCertificate", () => {
      const formTemplate = [
        {
          compType: "input",
          business: "temporaryPowerJobInChargeCertificate",
          formData: {
            formName: "临时用电作业负责人证书",
            type: "text",
            isReq: "required",
          },
        },
        {
          compType: "selector",
          business: "workContent",
          formData: { formName: "作业内容" },
        },
      ];

      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: JSON.stringify(formTemplate),
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };

      const result = compareVersions(apiTmpl, false) as any;
      // compareVersions在升级时直接返回升级后的formTemplate数组
      const upgradedTemplate = result;

      // 验证第一个元素被转换为selector
      expect(upgradedTemplate[0].compType).toBe("selector");
      expect(upgradedTemplate[0].business).toBe(
        "temporaryPowerJobInChargeCertificate"
      );
      expect(upgradedTemplate[0].compName).toBe("复选框");
      expect(upgradedTemplate[0].itemId).toBe("mock-form-id-123");
      expect(upgradedTemplate[0].formData.multiple).toBe(true);
      expect(upgradedTemplate[0].formData.serviceRange).toEqual([1]);
      expect(upgradedTemplate[0].formData.type).toBeUndefined(); // type字段应该被omit

      // 验证第二个元素保持不变
      expect(upgradedTemplate[1]).toEqual(formTemplate[1]);
    });

    it("应该将v0版本升级到v1 - guardianCertificate", () => {
      const formTemplate = [
        {
          compType: "input",
          business: "guardianCertificate",
          formData: {
            formName: "监护人证书",
            type: "text",
            isReq: "required",
          },
        },
      ];

      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: JSON.stringify(formTemplate),
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };

      const result = compareVersions(apiTmpl, false) as any;
      // compareVersions在升级时直接返回升级后的formTemplate数组
      const upgradedTemplate = result;

      expect(upgradedTemplate[0].compType).toBe("selector");
      expect(upgradedTemplate[0].business).toBe("guardianCertificate");
      expect(upgradedTemplate[0].formData.multiple).toBe(true);
      expect(upgradedTemplate[0].formData.serviceRange).toEqual([1]);
    });

    it("应该处理混合的v0和非v0元素", () => {
      const formTemplate = [
        {
          compType: "input",
          business: "temporaryPowerJobInChargeCertificate",
          formData: { formName: "临时用电作业负责人证书" },
        },
        {
          compType: "selector",
          business: "workContent",
          formData: { formName: "作业内容" },
        },
        {
          compType: "input",
          business: "guardianCertificate",
          formData: { formName: "监护人证书" },
        },
      ];

      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: JSON.stringify(formTemplate),
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };

      const result = compareVersions(apiTmpl, false) as any;
      // compareVersions在升级时直接返回升级后的formTemplate数组
      const upgradedTemplate = result;

      // 第一个和第三个应该被转换
      expect(upgradedTemplate[0].compType).toBe("selector");
      expect(upgradedTemplate[2].compType).toBe("selector");

      // 第二个应该保持不变
      expect(upgradedTemplate[1]).toEqual(formTemplate[1]);
    });
  });

  describe("边界情况测试", () => {
    it("应该处理空的formTemplate数组", () => {
      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: "[]",
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };

      const result = compareVersions(apiTmpl);
      expect(result).toEqual(apiTmpl);
    });

    it("应该处理不匹配条件的input元素", () => {
      const formTemplate = [
        {
          compType: "input",
          business: "otherBusiness",
          formData: { formName: "其他业务" },
        },
      ];

      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: JSON.stringify(formTemplate),
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };

      const result = compareVersions(apiTmpl);
      expect(result).toEqual(apiTmpl);
    });

    it("应该处理非input类型的匹配business", () => {
      const formTemplate = [
        {
          compType: "selector",
          business: "temporaryPowerJobInChargeCertificate",
          formData: { formName: "临时用电作业负责人证书" },
        },
      ];

      const apiTmpl = {
        templates: [
          {
            allowNoAppointment: 1,
            formTemplate: JSON.stringify(formTemplate),
            formTemplateId: 1,
            icon: "icon.png",
            id: 1,
            name: "test",
            processTemplate: [],
            processTemplateId: 1,
          },
        ],
      };

      const result = compareVersions(apiTmpl);
      expect(result).toEqual(apiTmpl);
    });
  });
});
