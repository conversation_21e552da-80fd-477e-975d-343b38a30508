import { ItemType } from "../../../types/sendbox";

/**
 * 表单依赖关系管理器
 * 负责处理表单字段间的依赖关系，包括字段可见性控制和依赖验证
 */
export class DependencyManager {
  private formFields: ItemType[];
  private fieldValues: Map<string, any> = new Map();
  private dependencyMap: Map<string, string[]> = new Map();

  constructor(formFields: ItemType[]) {
    this.formFields = this.flattenFormFields(formFields);
    this.buildDependencyMap();
  }

  /**
   * 扁平化表单字段（处理嵌套结构）
   * 将嵌套的表单结构转换为扁平的字段列表，便于依赖关系处理
   */
  private flattenFormFields(fields: ItemType[]): ItemType[] {
    const result: ItemType[] = [];

    const flatten = (items: ItemType[]) => {
      items.forEach((item) => {
        result.push(item);
        if (item.children && item.children.length > 0) {
          flatten(item.children);
        }
      });
    };

    flatten(fields);
    return result;
  }

  /**
   * 构建依赖关系映射
   * 创建从依赖字段到被依赖字段的映射关系
   */
  buildDependencyMap(): void {
    this.dependencyMap.clear();

    this.formFields.forEach((field) => {
      const { dependent } = field.formData || {};
      if (dependent && field.itemId) {
        if (!this.dependencyMap.has(dependent)) {
          this.dependencyMap.set(dependent, []);
        }
        this.dependencyMap.get(dependent)!.push(field.itemId);
      }
    });
  }

  /**
   * 检查字段是否应该显示
   * 根据依赖关系和当前字段值判断字段的可见性
   */
  shouldShowField(fieldId: string): boolean {
    const field = this.formFields.find((f) => f.itemId === fieldId);
    if (!field || !field.formData?.dependent) {
      return true; // 无依赖的字段默认显示
    }

    const dependentValue = this.fieldValues.get(field.formData.dependent);
    const expectedValue = field.formData.dependentValue;

    // 处理不同类型的值比较
    if (
      typeof dependentValue === "string" &&
      typeof expectedValue === "number"
    ) {
      return parseInt(dependentValue, 10) === expectedValue;
    }
    if (
      typeof dependentValue === "number" &&
      typeof expectedValue === "string"
    ) {
      return dependentValue === parseInt(expectedValue, 10);
    }

    return dependentValue === expectedValue;
  }

  /**
   * 更新字段值并返回受影响的字段
   * 当字段值发生变化时，更新依赖管理器并返回所有受影响的字段ID
   */
  updateFieldValue(fieldId: string, value: any): string[] {
    this.fieldValues.set(fieldId, value);
    return this.dependencyMap.get(fieldId) || [];
  }

  /**
   * 获取所有隐藏的字段ID
   * 返回当前状态下应该隐藏的所有字段ID列表
   */
  getHiddenFields(): string[] {
    return this.formFields
      .filter((field) => field.itemId && !this.shouldShowField(field.itemId))
      .map((field) => field.itemId!)
      .filter(Boolean);
  }

  /**
   * 获取所有可见的字段ID
   * 返回当前状态下应该显示的所有字段ID列表
   */
  getVisibleFields(): string[] {
    return this.formFields
      .filter((field) => field.itemId && this.shouldShowField(field.itemId))
      .map((field) => field.itemId!)
      .filter(Boolean);
  }

  /**
   * 验证依赖关系配置
   * 检查所有依赖关系配置的有效性，返回验证结果和错误信息
   */
  validateDependencyConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    const fieldIds = new Set(
      this.formFields.map((f) => f.itemId).filter(Boolean)
    );

    this.formFields.forEach((field) => {
      const { dependent, dependentValue } = field.formData || {};
      const fieldName =
        field.formData?.formName || field.compName || field.itemId;

      // 检查依赖字段是否存在
      if (dependent && !fieldIds.has(dependent)) {
        errors.push(`字段 "${fieldName}" 依赖的字段 "${dependent}" 不存在`);
      }

      // 检查依赖值是否有效
      if (dependent && dependentValue !== undefined) {
        const dependentField = this.formFields.find(
          (f) => f.itemId === dependent
        );

        if (dependentField) {
          // 对于单选框，检查依赖值是否在候选列表中
          if (dependentField.compType === "radio") {
            const candidateList = dependentField.formData?.candidateList;
            if (candidateList && Array.isArray(candidateList)) {
              const validValues = candidateList.map((c: any) => c.id);
              const hasValidValue = validValues.some((validValue: any) => {
                // 支持不同类型的值比较
                if (
                  typeof validValue === "string" &&
                  typeof dependentValue === "number"
                ) {
                  return parseInt(validValue, 10) === dependentValue;
                }
                if (
                  typeof validValue === "number" &&
                  typeof dependentValue === "string"
                ) {
                  return validValue === parseInt(dependentValue, 10);
                }
                return validValue === dependentValue;
              });

              if (!hasValidValue && dependentValue !== 0) {
                errors.push(
                  `字段 "${fieldName}" 的依赖值 "${dependentValue}" 在依赖字段 "${dependentField.formData?.formName || dependentField.compName}" 的选项中不存在`
                );
              }
            }
          }

          // 对于其他类型的字段，可以在这里添加相应的验证逻辑
        }
      }

      // 检查循环依赖
      if (dependent && this.hasCircularDependency(field.itemId!, dependent)) {
        errors.push(
          `字段 "${fieldName}" 与字段 "${dependent}" 之间存在循环依赖`
        );
      }
    });

    return { valid: errors.length === 0, errors };
  }

  /**
   * 检查是否存在循环依赖
   * 使用深度优先搜索检测依赖关系中的循环
   */
  private hasCircularDependency(
    startFieldId: string,
    targetFieldId: string
  ): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (fieldId: string): boolean => {
      if (recursionStack.has(fieldId)) {
        return true; // 发现循环
      }
      if (visited.has(fieldId)) {
        return false; // 已经访问过，无循环
      }

      visited.add(fieldId);
      recursionStack.add(fieldId);

      const field = this.formFields.find((f) => f.itemId === fieldId);
      if (field?.formData?.dependent) {
        if (dfs(field.formData.dependent)) {
          return true;
        }
      }

      recursionStack.delete(fieldId);
      return false;
    };

    return dfs(startFieldId);
  }

  /**
   * 获取字段的依赖信息
   * 返回指定字段的依赖配置信息
   */
  getFieldDependency(
    fieldId: string
  ): { dependent?: string; dependentValue?: any } | null {
    const field = this.formFields.find((f) => f.itemId === fieldId);
    if (!field?.formData) {
      return null;
    }

    const { dependent, dependentValue } = field.formData;
    if (!dependent) {
      return null;
    }

    return { dependent, dependentValue };
  }

  /**
   * 获取依赖某个字段的所有字段
   * 返回依赖指定字段的所有字段ID列表
   */
  getDependentFields(fieldId: string): string[] {
    return this.dependencyMap.get(fieldId) || [];
  }

  /**
   * 重置字段值
   * 清空所有字段值，通常在表单重置时使用
   */
  resetFieldValues(): void {
    this.fieldValues.clear();
  }

  /**
   * 获取当前所有字段值
   * 返回当前存储的所有字段值的副本
   */
  getAllFieldValues(): Map<string, any> {
    return new Map(this.fieldValues);
  }

  /**
   * 批量更新字段值
   * 一次性更新多个字段的值，返回所有受影响的字段ID
   */
  batchUpdateFieldValues(values: Record<string, any>): string[] {
    const affectedFields = new Set<string>();

    Object.entries(values).forEach(([fieldId, value]) => {
      this.fieldValues.set(fieldId, value);
      const affected = this.dependencyMap.get(fieldId) || [];
      affected.forEach((id) => affectedFields.add(id));
    });

    return Array.from(affectedFields);
  }
}
