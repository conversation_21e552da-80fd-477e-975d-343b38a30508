import { fireEvent, render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import EventCover from "../index";

// Mock dependencies
vi.mock("@douyinfe/semi-ui", () => ({
  Button: ({ children, onClick, type, className }: any) => (
    <button onClick={onClick} data-type={type} className={className}>
      {children}
    </button>
  ),
}));

vi.mock("@douyinfe/semi-icons", () => ({
  IconDelete: () => <div data-testid="icon-delete">Delete Icon</div>,
}));

// Mock observers
vi.mock("../../../utils/observers", () => ({
  DisposeObserver: {
    notify: vi.fn(),
  },
  RemoveObserver: {
    notify: vi.fn(),
  },
}));

// Test data factories
const createMockEventData = (overrides = {}) => ({
  id: "test-event-1",
  compType: "input",
  formData: { label: "测试输入框" },
  isBuiltIn: false,
  ...overrides,
});

const createMockProps = (overrides = {}) => ({
  eventData: createMockEventData(),
  children: <div data-testid="child-content">Child Content</div>,
  ...overrides,
});

describe("EventCover", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // ==================== Level 1: 基础结构测试 ====================

  describe("Basic Structure", () => {
    it("should import EventCover component successfully", () => {
      expect(EventCover).toBeDefined();
      expect(typeof EventCover).toBe("function");
    });

    it("should render EventCover component correctly", () => {
      const props = createMockProps();
      render(<EventCover {...props} />);

      expect(screen.getByTestId("child-content")).toBeInTheDocument();
      expect(screen.getByText("Child Content")).toBeInTheDocument();
    });

    it("should handle component initialization", () => {
      const eventData = createMockEventData();

      expect(eventData.id).toBe("test-event-1");
      expect(eventData.compType).toBe("input");
      expect(eventData.isBuiltIn).toBe(false);
    });

    it("should apply correct CSS classes", () => {
      const props = createMockProps();
      render(<EventCover {...props} />);

      const container = screen.getByTestId("child-content").parentElement;
      expect(container).toHaveClass("rounded-md");
      expect(container).toHaveClass("border-dashed");
      expect(container).toHaveClass("cursor-move");
      expect(container).toHaveClass("relative");
    });

    it("should handle empty children gracefully", () => {
      const props = createMockProps({ children: null });
      render(<EventCover {...props} />);

      // 验证组件能正常渲染，即使没有子元素
      const container = screen.getByRole("button").parentElement;
      expect(container).toBeInTheDocument();
    });
  });

  // ==================== Level 2: 属性验证测试 ====================

  describe("Props Validation", () => {
    it("should handle required props correctly", () => {
      const eventData = createMockEventData();
      const props = createMockProps({ eventData });

      expect(props.eventData).toBeDefined();
      expect(props.children).toBeDefined();
      expect(props.eventData.id).toBe("test-event-1");
    });

    it("should show delete button for non-built-in components", () => {
      const props = createMockProps();
      render(<EventCover {...props} />);

      const deleteButton = screen.getByRole("button");
      expect(deleteButton).toBeInTheDocument();
      expect(deleteButton).toHaveAttribute("data-type", "danger");
      expect(screen.getByTestId("icon-delete")).toBeInTheDocument();
    });

    it("should hide delete button for built-in components", () => {
      const props = createMockProps({
        eventData: createMockEventData({ isBuiltIn: true }),
      });

      render(<EventCover {...props} />);

      expect(screen.queryByRole("button")).not.toBeInTheDocument();
      expect(screen.queryByTestId("icon-delete")).not.toBeInTheDocument();
    });

    it("should handle different component types", () => {
      const selectEventData = createMockEventData({
        id: "test-event-2",
        compType: "select",
        formData: {
          label: "测试选择框",
          options: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
        },
      });

      expect(selectEventData.compType).toBe("select");
      expect(selectEventData.formData.options).toHaveLength(2);
    });
  });

  // ==================== Level 3: 交互逻辑测试 ====================

  describe("User Interactions", () => {
    it("should handle container click events", () => {
      const props = createMockProps();
      render(<EventCover {...props} />);

      const container = screen.getByTestId("child-content").parentElement;
      fireEvent.click(container!);

      // 验证点击事件被处理
      expect(container).toBeInTheDocument();
    });

    it("should handle delete button click", () => {
      const props = createMockProps();
      render(<EventCover {...props} />);

      const deleteButton = screen.getByRole("button");
      fireEvent.click(deleteButton);

      // 验证删除按钮被点击
      expect(deleteButton).toBeInTheDocument();
    });

    it("should prevent event bubbling on container click", () => {
      const mockParentClick = vi.fn();
      const props = createMockProps();

      render(
        <div onClick={mockParentClick}>
          <EventCover {...props} />
        </div>
      );

      const container = screen.getByTestId("child-content").parentElement;
      fireEvent.click(container!);

      // 验证父元素的点击事件没有被触发
      expect(mockParentClick).not.toHaveBeenCalled();
    });

    it("should prevent event bubbling on delete button click", () => {
      const mockParentClick = vi.fn();
      const props = createMockProps();

      render(
        <div onClick={mockParentClick}>
          <EventCover {...props} />
        </div>
      );

      const deleteButton = screen.getByRole("button");
      fireEvent.click(deleteButton);

      // 验证父元素的点击事件没有被触发
      expect(mockParentClick).not.toHaveBeenCalled();
    });
  });

  // ==================== Level 4: 业务逻辑测试 ====================

  describe("Business Logic", () => {
    it("should handle complex children rendering", () => {
      const complexChildren = (
        <div data-testid="complex-child">
          <h3>标题</h3>
          <p>描述文本</p>
          <button>子按钮</button>
        </div>
      );

      const props = createMockProps({ children: complexChildren });
      render(<EventCover {...props} />);

      expect(screen.getByTestId("complex-child")).toBeInTheDocument();
      expect(screen.getByText("标题")).toBeInTheDocument();
      expect(screen.getByText("描述文本")).toBeInTheDocument();
      expect(screen.getByText("子按钮")).toBeInTheDocument();
    });

    it("should process different event data types", () => {
      const selectEventData = createMockEventData({
        id: "test-event-2",
        compType: "select",
        formData: {
          label: "测试选择框",
          options: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
        },
      });

      const props = createMockProps({ eventData: selectEventData });
      render(<EventCover {...props} />);

      const container = screen.getByTestId("child-content").parentElement;
      fireEvent.click(container!);

      // 验证组件能处理不同的事件数据
      expect(container).toBeInTheDocument();
    });

    it("should handle noMask property correctly", () => {
      const props = createMockProps({ noMask: true });
      render(<EventCover {...props} />);

      expect(screen.getByTestId("child-content")).toBeInTheDocument();
      // noMask属性目前在组件中未使用，但应该能正常传递
    });

    it("should validate built-in component behavior", () => {
      const builtInEventData = createMockEventData({ isBuiltIn: true });
      const props = createMockProps({ eventData: builtInEventData });

      expect(builtInEventData.isBuiltIn).toBe(true);

      render(<EventCover {...props} />);

      // 内置组件不显示删除按钮
      expect(screen.queryByRole("button")).not.toBeInTheDocument();
    });

    it("should validate non-built-in component behavior", () => {
      const nonBuiltInEventData = createMockEventData({ isBuiltIn: false });
      const props = createMockProps({ eventData: nonBuiltInEventData });

      expect(nonBuiltInEventData.isBuiltIn).toBe(false);

      render(<EventCover {...props} />);

      // 非内置组件显示删除按钮
      expect(screen.getByRole("button")).toBeInTheDocument();
    });

    it("should handle component type variations", () => {
      const componentTypes = ["input", "select", "textarea", "table"];

      componentTypes.forEach((compType) => {
        const eventData = createMockEventData({ compType });
        expect(eventData.compType).toBe(compType);
      });
    });
  });

  // ==================== Level 5: 集成测试 ====================

  describe("Integration Tests", () => {
    it("should work with complete EventCover workflow", () => {
      const props = createMockProps();
      render(<EventCover {...props} />);

      // 验证组件完整性
      expect(screen.getByTestId("child-content")).toBeInTheDocument();

      // 验证删除按钮功能
      const deleteButton = screen.getByRole("button");
      expect(deleteButton).toBeInTheDocument();

      // 验证点击交互
      fireEvent.click(deleteButton);
      expect(deleteButton).toBeInTheDocument();
    });

    it("should maintain stability across different configurations", () => {
      const configurations = [
        { isBuiltIn: true, compType: "input" },
        { isBuiltIn: false, compType: "select" },
        { isBuiltIn: false, compType: "textarea" },
      ];

      configurations.forEach((config) => {
        const eventData = createMockEventData(config);
        const props = createMockProps({ eventData });

        // 验证配置数据
        expect(eventData.isBuiltIn).toBe(config.isBuiltIn);
        expect(eventData.compType).toBe(config.compType);

        // 验证组件行为
        render(<EventCover {...props} />);
        expect(screen.getByTestId("child-content")).toBeInTheDocument();

        // 清理DOM
        screen.getByTestId("child-content").remove();
      });
    });

    it("should handle complex integration scenarios", () => {
      const complexScenario = {
        eventData: createMockEventData({
          id: "complex-event",
          compType: "table",
          formData: {
            colNum: 3,
            rowNum: 2,
            borderWidth: 1,
            borderColor: "#000000",
          },
          isBuiltIn: false,
        }),
        children: (
          <div data-testid="complex-content">
            <div>表格组件</div>
            <div>3列2行</div>
          </div>
        ),
        noMask: false,
      };

      render(<EventCover {...complexScenario} />);

      // 验证复杂场景渲染
      expect(screen.getByTestId("complex-content")).toBeInTheDocument();
      expect(screen.getByText("表格组件")).toBeInTheDocument();
      expect(screen.getByText("3列2行")).toBeInTheDocument();

      // 验证删除按钮存在（非内置组件）
      expect(screen.getByRole("button")).toBeInTheDocument();

      // 验证事件处理
      const container = screen.getByTestId("complex-content").parentElement;
      fireEvent.click(container!);
      expect(container).toBeInTheDocument();
    });
  });
});
