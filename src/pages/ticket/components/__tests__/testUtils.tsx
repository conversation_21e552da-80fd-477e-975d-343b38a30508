/**
 * 组件库测试工具函数库
 * 为动态表单系统组件库层提供统一的测试工具和Mock模板
 */

import { render } from "@testing-library/react";
import { Provider } from "jotai";
import { vi } from "vitest";
import React from "react";

// ==================== 通用渲染函数 ====================

/**
 * 使用Jotai Provider包装组件进行渲染
 */
export const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <Provider>
      {component}
    </Provider>
  );
};

/**
 * 使用完整Context包装组件进行渲染
 */
export const renderWithFullContext = (component: React.ReactElement, contextValue = {}) => {
  const mockContextValue = {
    containerState: {},
    setContainerState: vi.fn(),
    ...contextValue,
  };

  // Mock FormContext
  const MockFormContext = {
    Provider: ({ children }: any) => children,
    Consumer: ({ children }: any) => children(mockContextValue),
  };

  return render(
    <Provider>
      <MockFormContext.Provider value={mockContextValue}>
        {component}
      </MockFormContext.Provider>
    </Provider>
  );
};

// ==================== Mock数据工厂 ====================

/**
 * 创建Mock表单数据
 */
export const createMockFormData = (overrides = {}) => ({
  label: "测试标签",
  required: false,
  placeholder: "请输入",
  width: 100,
  height: 40,
  ...overrides,
});

/**
 * 创建Mock表格数据
 */
export const createMockTableData = (rows = 2, cols = 2) => {
  const tableData = [];
  for (let i = 0; i < rows; i++) {
    const rowData = [];
    for (let j = 0; j < cols; j++) {
      rowData.push({
        isEditable: j % 2 === 0,
        isNewLine: j === 0,
        children: [
          {
            compType: "input",
            formData: createMockFormData({ label: `单元格${i}-${j}` }),
          },
        ],
      });
    }
    tableData.push(rowData);
  }
  return tableData;
};

/**
 * 创建Mock单元格数据
 */
export const createMockCellData = (overrides = {}) => ({
  comType: "tableCell",
  formData: {
    isNewLine: false,
    assign: "center",
    width: 50,
    paddingLeft: 10,
    paddingRight: 10,
    ...overrides.formData,
  },
  children: [],
  ...overrides,
});

/**
 * 创建Mock组件属性
 */
export const createMockComponentProps = (overrides = {}) => ({
  current: [
    {
      formData: createMockFormData(),
      children: [],
    },
  ],
  idx: 0,
  ...overrides,
});

// ==================== Mock模板库 ====================

/**
 * Semi UI组件标准Mock模板
 */
export const createSemiUIMocks = () => ({
  Button: ({ children, onClick, disabled, type, ...props }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      data-type={type}
      {...props}
    >
      {children}
    </button>
  ),
  Form: ({ children, onSubmit, ...props }: any) => (
    <form 
      onSubmit={(e) => { 
        e.preventDefault(); 
        onSubmit?.({}); 
      }} 
      {...props}
    >
      {children}
    </form>
  ),
  Input: ({ value, onChange, placeholder, disabled, ...props }: any) => (
    <input 
      value={value} 
      onChange={(e) => onChange?.(e.target.value)} 
      placeholder={placeholder} 
      disabled={disabled}
      {...props} 
    />
  ),
  Select: ({ value, onChange, options, placeholder, ...props }: any) => (
    <select 
      value={value} 
      onChange={(e) => onChange?.(e.target.value)} 
      {...props}
    >
      <option value="">{placeholder}</option>
      {options?.map((opt: any) => (
        <option key={opt.value} value={opt.value}>{opt.label}</option>
      ))}
    </select>
  ),
  Radio: ({ value, onChange, children, ...props }: any) => (
    <input 
      type="radio" 
      value={value} 
      onChange={(e) => onChange?.(e.target.value)}
      {...props}
    />
  ),
  RadioGroup: ({ value, onChange, children, ...props }: any) => (
    <div data-testid="radio-group" {...props}>
      {children}
    </div>
  ),
  SideSheet: ({ visible, children, onCancel, title, ...props }: any) =>
    visible ? (
      <div data-testid="side-sheet" {...props}>
        <div data-testid="side-sheet-header">
          <span>{title}</span>
          <button onClick={onCancel} data-testid="close-button">Close</button>
        </div>
        <div data-testid="side-sheet-content">
          {children}
        </div>
      </div>
    ) : null,
  Modal: ({ visible, children, onCancel, title, ...props }: any) =>
    visible ? (
      <div data-testid="modal" {...props}>
        <div data-testid="modal-header">
          <span>{title}</span>
          <button onClick={onCancel} data-testid="modal-close">×</button>
        </div>
        <div data-testid="modal-content">
          {children}
        </div>
      </div>
    ) : null,
});

/**
 * Jotai状态管理标准Mock模板
 */
export const createJotaiMocks = (initialState = {}) => {
  const mockState = { ...initialState };
  const mockSetState = vi.fn((newState) => {
    Object.assign(mockState, typeof newState === 'function' ? newState(mockState) : newState);
  });

  return {
    useAtom: vi.fn(() => [mockState, mockSetState]),
    useAtomValue: vi.fn(() => mockState),
    useSetAtom: vi.fn(() => mockSetState),
    atom: vi.fn(() => mockState),
  };
};

/**
 * Context标准Mock模板
 */
export const createContextMocks = (contextValue = {}) => ({
  FormContext: {
    Provider: ({ children }: any) => children,
    Consumer: ({ children }: any) => children({
      containerState: {},
      setContainerState: vi.fn(),
      ...contextValue,
    }),
  },
});

// ==================== 测试辅助函数 ====================

/**
 * 等待异步操作完成
 */
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0));

/**
 * 创建Mock事件对象
 */
export const createMockEvent = (overrides = {}) => ({
  preventDefault: vi.fn(),
  stopPropagation: vi.fn(),
  target: { value: "" },
  ...overrides,
});

/**
 * 验证Mock函数调用
 */
export const expectMockCalled = (mockFn: any, times = 1) => {
  expect(mockFn).toHaveBeenCalledTimes(times);
};

/**
 * 验证Mock函数调用参数
 */
export const expectMockCalledWith = (mockFn: any, ...args: any[]) => {
  expect(mockFn).toHaveBeenCalledWith(...args);
};

// ==================== 组件特定Mock ====================

/**
 * FormTable组件专用Mock数据
 */
export const createFormTableMocks = () => ({
  props: createMockComponentProps({
    current: [
      {
        formData: { colNum: 3, rowNum: 2 },
        children: createMockTableData(2, 3),
      },
    ],
  }),
  tableData: createMockTableData(2, 3),
  cellAction: {
    visible: false,
    data: null,
  },
});

/**
 * CellActionPanel组件专用Mock数据
 */
export const createCellActionPanelMocks = () => ({
  cb: vi.fn(),
  dataSource: createMockTableData(2, 2),
  setTableData: vi.fn(),
  cellAction: {
    visible: true,
    data: createMockCellData(),
  },
});

/**
 * DisposeForm组件专用Mock数据
 */
export const createDisposeFormMocks = () => ({
  formData: createMockFormData({
    title: "处置表单",
    description: "测试处置表单",
  }),
  onSubmit: vi.fn(),
  onCancel: vi.fn(),
});
