import { render } from "@testing-library/react";
import { Provider } from "jotai";
import React from "react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { ColForm, colActionAtom } from "../colActionPanel";

// ==================== 测试工具函数 ====================

const renderWithProviders = (component: React.ReactElement) => {
  return render(<Provider>{component}</Provider>);
};

const createMockColData = (overrides = {}) => ({
  index: 0,
  colNum: 3,
  rowNum: 2,
  width: 100,
  align: "center",
  ...overrides,
});

const createMockTableData = (rows = 2, cols = 3) => {
  const tableData = [];
  for (let i = 0; i < rows; i++) {
    const row = [];
    for (let j = 0; j < cols; j++) {
      row.push({
        compType: "input",
        formData: {
          label: `单元格${i}-${j}`,
          placeholder: `占位符${i}-${j}`,
        },
      });
    }
    tableData.push(row);
  }
  return tableData;
};

// Mock dependencies
vi.mock("@douyinfe/semi-ui", () => ({
  ArrayField: ({ children, field }: any) => (
    <div data-testid="array-field" data-field={field}>
      {children}
    </div>
  ),
  Button: ({ children, onClick, type, ...props }: any) => (
    <button onClick={onClick} data-type={type} {...props}>
      {children}
    </button>
  ),
  Form: ({ children, onSubmit, initValues, ...props }: any) => (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit?.(initValues || {});
      }}
      {...props}
    >
      {children}
    </form>
  ),
  SideSheet: ({ visible, children, onCancel, title }: any) =>
    visible ? (
      <div data-testid="side-sheet">
        <h3>{title}</h3>
        <button onClick={onCancel}>Close</button>
        {children}
      </div>
    ) : null,
}));

vi.mock("@douyinfe/semi-icons", () => ({
  IconMinusCircle: () => <div data-testid="icon-minus">-</div>,
  IconPlusCircle: () => <div data-testid="icon-plus">+</div>,
}));

// Mock Jotai
vi.mock("jotai", () => ({
  useAtom: vi.fn(() => [
    { visible: false, index: 0, dataSource: null },
    vi.fn(),
  ]),
  useResetAtom: vi.fn(() => vi.fn()),
  atom: vi.fn(() => ({})),
  atomWithReset: vi.fn(() => ({})),
  Provider: ({ children }: any) => children,
}));

describe("ColForm", () => {
  const mockCb = vi.fn();

  const defaultProps = {
    cb: mockCb,
    dataSource: createMockTableData(2, 3),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  // ==================== Level 1: 基础结构测试 ====================

  describe("Basic Structure", () => {
    it("should import ColForm component successfully", () => {
      expect(ColForm).toBeDefined();
      expect(typeof ColForm).toBe("function");
    });

    it("should import colActionAtom successfully", () => {
      expect(colActionAtom).toBeDefined();
      expect(typeof colActionAtom).toBe("object");
    });

    it("should render without crashing", () => {
      // 验证组件基本结构
      expect(defaultProps.cb).toBeDefined();
      expect(defaultProps.dataSource).toBeDefined();
      expect(Array.isArray(defaultProps.dataSource)).toBe(true);
    });

    it("should handle component initialization", () => {
      // 验证组件初始化
      expect(typeof ColForm).toBe("function");
      expect(defaultProps.dataSource).toHaveLength(2);
      expect(defaultProps.dataSource[0]).toHaveLength(3);
    });
  });

  // ==================== Level 2: 属性验证测试 ====================

  describe("Props Validation", () => {
    it("should handle required props correctly", () => {
      expect(defaultProps.cb).toBeDefined();
      expect(typeof defaultProps.cb).toBe("function");
      expect(defaultProps.dataSource).toBeDefined();
      expect(Array.isArray(defaultProps.dataSource)).toBe(true);
    });

    it("should handle callback function", () => {
      const testCallback = vi.fn();
      const testProps = { cb: testCallback, dataSource: [] };

      expect(testProps.cb).toBeDefined();
      expect(typeof testProps.cb).toBe("function");
    });

    it("should handle empty dataSource gracefully", () => {
      const emptyProps = { cb: mockCb, dataSource: [] };

      expect(emptyProps.dataSource).toBeDefined();
      expect(Array.isArray(emptyProps.dataSource)).toBe(true);
      expect(emptyProps.dataSource).toHaveLength(0);
    });

    it("should validate dataSource structure", () => {
      const tableData = defaultProps.dataSource;

      expect(tableData).toHaveLength(2);
      expect(tableData[0]).toHaveLength(3);
      expect(tableData[0][0]).toHaveProperty("compType");
      expect(tableData[0][0]).toHaveProperty("formData");
    });
  });

  // ==================== Level 3: 交互逻辑测试 ====================

  describe("User Interactions", () => {
    it("should handle callback execution", () => {
      // 验证回调函数调用
      const testData = createMockColData({ index: 1 });
      mockCb(testData);

      expect(mockCb).toHaveBeenCalledWith(testData);
      expect(mockCb).toHaveBeenCalledTimes(1);
    });

    it("should handle column operations", () => {
      // 验证列操作逻辑
      const colData = createMockColData({
        index: 2,
        width: 150,
        align: "left",
      });

      expect(colData.index).toBe(2);
      expect(colData.width).toBe(150);
      expect(colData.align).toBe("left");
    });

    it("should handle table data manipulation", () => {
      // 验证表格数据操作
      const tableData = createMockTableData(3, 4);

      // 模拟添加列
      tableData.forEach((row) => {
        row.push({
          compType: "newInput",
          formData: { label: "新列" },
        });
      });

      expect(tableData[0]).toHaveLength(5);
      expect(tableData[0][4].formData.label).toBe("新列");
    });

    it("should handle form state changes", () => {
      // 验证表单状态变化
      const initialState = { visible: false, index: 0 };
      const newState = { visible: true, index: 2 };

      expect(initialState.visible).toBe(false);
      expect(newState.visible).toBe(true);
      expect(newState.index).toBe(2);
    });
  });

  // ==================== Level 4: 业务逻辑测试 ====================

  describe("Business Logic", () => {
    it("should process column configuration correctly", () => {
      const colConfig = createMockColData({
        index: 1,
        colNum: 5,
        rowNum: 3,
        width: 120,
        align: "right",
      });

      expect(colConfig.index).toBe(1);
      expect(colConfig.colNum).toBe(5);
      expect(colConfig.rowNum).toBe(3);
      expect(colConfig.width).toBe(120);
      expect(colConfig.align).toBe("right");
    });

    it("should handle table structure validation", () => {
      const tableData = createMockTableData(2, 3);

      // 验证表格结构
      expect(tableData).toHaveLength(2);
      tableData.forEach((row, rowIndex) => {
        expect(row).toHaveLength(3);
        row.forEach((cell, colIndex) => {
          expect(cell.compType).toBe("input");
          expect(cell.formData.label).toBe(`单元格${rowIndex}-${colIndex}`);
        });
      });
    });

    it("should process column alignment options", () => {
      const alignments = ["left", "center", "right"];

      alignments.forEach((align) => {
        const colData = createMockColData({ align });
        expect(colData.align).toBe(align);
      });
    });

    it("should handle column width calculations", () => {
      const widths = [50, 100, 150, 200];

      widths.forEach((width) => {
        const colData = createMockColData({ width });
        expect(colData.width).toBe(width);
        expect(typeof colData.width).toBe("number");
      });
    });

    it("should validate column index boundaries", () => {
      const maxCols = 5;

      for (let i = 0; i < maxCols; i++) {
        const colData = createMockColData({ index: i, colNum: maxCols });
        expect(colData.index).toBe(i);
        expect(colData.index).toBeLessThan(maxCols);
      }
    });

    it("should handle table data transformation", () => {
      const originalData = createMockTableData(2, 2);

      // 模拟转换操作
      const transformedData = originalData.map((row, rowIndex) =>
        row.map((cell, colIndex) => ({
          ...cell,
          formData: {
            ...cell.formData,
            id: `cell-${rowIndex}-${colIndex}`,
          },
        }))
      );

      expect(transformedData[0][0].formData.id).toBe("cell-0-0");
      expect(transformedData[1][1].formData.id).toBe("cell-1-1");
    });
  });

  // ==================== Level 5: 集成测试 ====================

  describe("Integration Tests", () => {
    it("should work with complete data flow", () => {
      const completeProps = {
        cb: mockCb,
        dataSource: createMockTableData(3, 4),
      };

      // 验证完整数据流
      expect(completeProps.cb).toBeDefined();
      expect(completeProps.dataSource).toHaveLength(3);
      expect(completeProps.dataSource[0]).toHaveLength(4);
    });

    it("should maintain component stability", () => {
      // 验证组件稳定性
      for (let i = 0; i < 3; i++) {
        const testProps = {
          cb: vi.fn(),
          dataSource: createMockTableData(i + 1, i + 2),
        };

        expect(testProps.dataSource).toHaveLength(i + 1);
        expect(testProps.dataSource[0]).toHaveLength(i + 2);
      }
    });

    it("should handle complex table scenarios", () => {
      const complexData = createMockTableData(4, 5);

      // 模拟复杂操作：删除列
      const afterDeleteCol = complexData.map((row) =>
        row.filter((_, colIndex) => colIndex !== 2)
      );

      expect(afterDeleteCol[0]).toHaveLength(4);
      expect(afterDeleteCol[0][2].formData.label).toBe("单元格0-3");

      // 模拟复杂操作：插入列
      const afterInsertCol = complexData.map((row) => {
        const newRow = [...row];
        newRow.splice(1, 0, {
          compType: "insertedInput",
          formData: { label: "插入列" },
        });
        return newRow;
      });

      expect(afterInsertCol[0]).toHaveLength(6);
      expect(afterInsertCol[0][1].formData.label).toBe("插入列");
    });
  });
});
