import { render, screen } from "@testing-library/react";
import { vi } from "vitest";
import { RenderItem } from "../renderItem";

// Mock 所有依赖
vi.mock("@douyinfe/semi-ui", () => {
  // 创建Select.Option组件
  const SelectOption = ({ children, ...props }: any) => (
    <option data-testid="semi-select-option" {...props}>
      {children}
    </option>
  );

  // 创建Select组件并挂载Option静态属性
  const Select = ({ label, children, ...props }: any) => (
    <select data-testid="semi-select" label={label} {...props}>
      {children}
    </select>
  );
  Select.Option = SelectOption;

  return {
    Form: {
      Input: ({ label, ...props }: any) => (
        <input data-testid="semi-input" label={label} {...props} />
      ),
      InputNumber: ({ label, ...props }: any) => (
        <input data-testid="semi-input-number" label={label} {...props} />
      ),
      Select,
      TextArea: ({ label, ...props }: any) => (
        <textarea data-testid="semi-textarea" label={label} {...props} />
      ),
      DatePicker: ({ label, ...props }: any) => (
        <input data-testid="semi-datepicker" label={label} {...props} />
      ),
      TimePicker: ({ label, ...props }: any) => (
        <input data-testid="semi-timepicker" label={label} {...props} />
      ),
      Switch: ({ label, ...props }: any) => (
        <input data-testid="semi-switch" label={label} {...props} />
      ),
      RadioGroup: ({ label, children, ...props }: any) => (
        <div data-testid="semi-radio-group" label={label} {...props}>
          {children}
        </div>
      ),
      Radio: ({ children, ...props }: any) => (
        <input data-testid="semi-radio" type="radio" {...props} />
      ),
      CheckboxGroup: ({ label, children, ...props }: any) => (
        <div data-testid="semi-checkbox-group" label={label} {...props}>
          {children}
        </div>
      ),
      Checkbox: ({ children, ...props }: any) => (
        <input data-testid="semi-checkbox" type="checkbox" {...props} />
      ),
      TagInput: ({ label, ...props }: any) => (
        <input data-testid="semi-tag-input" label={label} {...props} />
      ),
      Table: ({ dataSource, ...props }: any) => (
        <div data-testid="semi-table" {...props}>
          {dataSource?.map((item: any, index: number) => (
            <div key={index} data-testid={`table-row-${index}`}>
              {JSON.stringify(item)}
            </div>
          ))}
        </div>
      ),
    },
    Tooltip: ({ children, ...props }: any) => (
      <div data-testid="semi-tooltip" {...props}>
        {children}
      </div>
    ),
    useFormApi: () => ({
      getValue: vi.fn(() => undefined),
      setValue: vi.fn(),
    }),
  };
});

vi.mock("@tanstack/react-query", () => ({
  useQuery: () => ({
    data: { data: { results: [] } },
    isLoading: false,
    error: null,
  }),
}));

vi.mock("jotai", () => ({
  useAtom: () => [vi.fn(), vi.fn()],
  useAtomValue: () => vi.fn(),
}));

vi.mock("api", () => ({
  getRiskMeasureList: vi.fn(() => Promise.resolve({ data: { results: [] } })),
}));

vi.mock("atoms", () => ({
  certificateSelectAtom: {},
  mapPickerAtom: {},
}));

vi.mock("ramda", () => ({
  find: vi.fn(() => ({ name: "测试风险" })),
  propEq: vi.fn(() => vi.fn()),
  pick: vi.fn(() => ({ id: 1, name: "测试", type: 1 })),
  remove: vi.fn((index, count, array) =>
    array.filter((_: any, i: number) => i !== index)
  ),
}));

// Mock 所有 components 组件
vi.mock("components", () => ({
  AreaSearch: ({ ...props }: any) => (
    <div data-testid="area-search" {...props} />
  ),
  ContractorSearch: ({ ...props }: any) => (
    <div data-testid="contractor-search" {...props} />
  ),
  DepartmentPicker: ({ ...props }: any) => (
    <div data-testid="department-picker" {...props} />
  ),
  EmployeePicker: ({ ...props }: any) => (
    <div data-testid="employee-picker" {...props} />
  ),
  EmployeeSearch: ({ ...props }: any) => (
    <div data-testid="employee-search" {...props} />
  ),
  MapPicker: ({ ...props }: any) => <div data-testid="map-picker" {...props} />,
  Upload: ({ ...props }: any) => (
    <div data-testid="upload-component" {...props} />
  ),
  RISK_MEASURE_ACCIDENTTYPE: [{ id: 1, name: "测试风险类型" }],
}));

vi.mock("./renderTable", () => ({
  RenderTable: ({ item, ...props }: any) => (
    <div data-testid="render-table" {...props}>
      {item?.formData?.tableData?.map((row: any, index: number) => (
        <div key={index} data-testid={`table-row-${index}`}>
          {JSON.stringify(row)}
        </div>
      ))}
    </div>
  ),
}));

describe("RenderItem 核心功能测试", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // 基础组件渲染测试
  describe("基础组件渲染测试", () => {
    it("应该正确渲染 plainText 组件", () => {
      const item = {
        compType: "plainText",
        formData: {
          actualValue: "测试文本",
          textType: "title",
          textAlign: "center",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByText("测试文本")).toBeInTheDocument();
    });

    it("应该正确渲染 input 组件", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试输入",
          placeHolder: "请输入",
          isReq: "required",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-input")).toBeInTheDocument();
    });

    it("应该正确渲染 inputNumber 组件", () => {
      const item = {
        compType: "input",
        formData: {
          formName: "测试数字输入",
          type: "float",
          placeHolder: "请输入数字",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-input-number")).toBeInTheDocument();
    });

    it("应该正确渲染 radio 组件", () => {
      const item = {
        compType: "radio",
        formData: {
          formName: "测试单选",
          candidateList: [
            { id: 1, label: "选项1" },
            { id: 2, label: "选项2" },
          ],
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-radio-group")).toBeInTheDocument();
    });

    it("应该正确渲染 checkbox 组件", () => {
      const item = {
        compType: "checkbox",
        formData: {
          formName: "测试多选",
          candidateList: [{ option: "选项1" }, { option: "选项2" }],
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-checkbox-group")).toBeInTheDocument();
    });

    it("应该正确渲染 datePicker 组件", () => {
      const item = {
        compType: "datePicker",
        formData: {
          formName: "测试日期选择",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("semi-datepicker")).toBeInTheDocument();
    });
  });

  // 特殊组件渲染测试
  describe("特殊组件渲染测试", () => {
    it("应该正确渲染 AreaSearch 组件", () => {
      const item = {
        compType: "selector",
        business: "workArea",
        formData: {
          formName: "区域搜索",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("area-search")).toBeInTheDocument();
    });

    it("应该正确渲染 DepartmentPicker 组件", () => {
      const item = {
        compType: "selector",
        business: "department",
        formData: {
          formName: "部门选择",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("department-picker")).toBeInTheDocument();
    });

    it("应该正确渲染 EmployeePicker 组件", () => {
      const item = {
        compType: "employeePicker",
        formData: {
          formName: "员工选择",
          serviceRange: [2, 3],
          candidateList: [],
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      expect(screen.getByTestId("employee-picker")).toBeInTheDocument();
    });

    it("应该正确渲染 table 组件", () => {
      const item = {
        compType: "table",
        formData: {
          formName: "表格组件",
          colNum: 2,
          isHead: true,
          paddingTop: 0,
          paddingRight: 0,
          paddingBottom: 0,
          paddingLeft: 0,
        },
        children: [
          {
            formData: { width: "50%" },
            children: [
              {
                compType: "plainText",
                formData: {
                  actualValue: "表头1",
                  textAlign: "center",
                },
              },
            ],
          },
          {
            formData: { width: "50%" },
            children: [
              {
                compType: "plainText",
                formData: {
                  actualValue: "表头2",
                  textAlign: "center",
                },
              },
            ],
          },
        ],
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试表格内容是否正确渲染
      expect(screen.getByText("表头1")).toBeInTheDocument();
      expect(screen.getByText("表头2")).toBeInTheDocument();

      // 测试表格容器是否存在
      expect(screen.getByText("表头1").closest(".border")).toBeInTheDocument();
    });

    it("应该正确渲染 riskPicker 组件", () => {
      const item = {
        compType: "riskPicker",
        formData: {
          formName: "风险选择",
          placeHolder: "请选择风险",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试风险选择器是否正确渲染
      expect(screen.getByTestId("semi-select")).toBeInTheDocument();
    });

    it("应该正确渲染 mapPicker 组件", () => {
      const item = {
        compType: "mapPicker",
        formData: {
          formName: "地图选择",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试地图选择器是否正确渲染
      expect(screen.getByTestId("map-picker")).toBeInTheDocument();
      expect(screen.getByTestId("semi-input")).toBeInTheDocument();
    });

    it("应该正确渲染 annexImgPicker 组件", () => {
      const item = {
        compType: "annexImgPicker",
        formData: {
          formName: "图片上传",
          isReq: "required",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试图片上传组件是否正确渲染
      expect(screen.getByTestId("upload-component")).toBeInTheDocument();
    });

    it("应该正确渲染 annexFilePicker 组件", () => {
      const item = {
        compType: "annexFilePicker",
        formData: {
          formName: "文件上传",
          isReq: "required",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试文件上传组件是否正确渲染
      expect(screen.getByTestId("upload-component")).toBeInTheDocument();
    });

    it("应该正确渲染 Form.Select 组件", () => {
      const item = {
        compType: "selector",
        business: "normal",
        formData: {
          formName: "普通下拉",
          candidateList: [
            { id: 1, label: "选项A" },
            { id: 2, label: "选项B" },
          ],
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试下拉选择器是否正确渲染
      expect(screen.getByTestId("semi-select")).toBeInTheDocument();
      expect(screen.getByText("选项A")).toBeInTheDocument();
      expect(screen.getByText("选项B")).toBeInTheDocument();
    });

    it("应该正确渲染 Form.TagInput 组件", () => {
      const item = {
        compType: "selector",
        business: "guardianInCharge",
        formData: {
          formName: "作业监护人",
          placeHolder: "请选择监护人",
          isReq: "required",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试标签输入组件是否正确渲染
      expect(screen.getByTestId("semi-tag-input")).toBeInTheDocument();
    });

    // TODO: ContractorSearch 组件测试需要特殊处理
    // 需要 mock formApi.getValue("form.unitCategory") === 2 才能命中该分支
    // 当前 useFormApi mock 返回的 getValue 总是 undefined，所以不会命中 contractor-search 分支
    /*
    it("应该正确渲染 ContractorSearch 组件", () => {
      // 临时修改 useFormApi mock 来满足渲染条件
      const originalUseFormApi = require("@douyinfe/semi-ui").useFormApi;
      const mockGetValue = vi.fn((key: string) => {
        if (key === "form.unitCategory") return 2;
        return undefined;
      });

      // 替换 useFormApi
      require("@douyinfe/semi-ui").useFormApi = vi.fn(() => ({
        getValue: mockGetValue,
        setValue: vi.fn(),
      }));

      const item = {
        compType: "selector",
        business: "unit",
        formData: {
          formName: "承包商选择",
          placeHolder: "请选择承包商",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试承包商选择组件是否正确渲染
      expect(screen.getByTestId("contractor-search")).toBeInTheDocument();

      // 恢复原始的 useFormApi
      require("@douyinfe/semi-ui").useFormApi = originalUseFormApi;
    });
    */

    it("应该正确渲染 EmployeeSearch 组件", () => {
      const item = {
        compType: "employeePicker",
        formData: {
          formName: "员工搜索",
          serviceRange: [1], // 不包含2或3，会走EmployeeSearch分支
          candidateList: [], // 空列表
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试员工搜索组件是否正确渲染
      expect(screen.getByTestId("employee-search")).toBeInTheDocument();
    });

    it("应该正确处理未知的 compType", () => {
      const item = {
        compType: "unknownType",
        formData: {
          formName: "未知类型",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试未知类型是否正确显示"未定义"
      expect(screen.getByText("未定义")).toBeInTheDocument();
    });

    it("应该正确处理空的 compType", () => {
      const item = {
        compType: "",
        formData: {
          formName: "空类型",
        },
      };

      render(<RenderItem item={item} k={1} rule={[]} />);

      // 测试空类型是否正确显示"未定义"
      expect(screen.getByText("未定义")).toBeInTheDocument();
    });
  });
});
