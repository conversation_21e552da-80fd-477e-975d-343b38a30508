import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { RenderTable } from "../renderTable";

// Mock RenderItem 组件
vi.mock("../renderItem", () => ({
  RenderItem: ({ item, k, isTable }: any) => (
    <div data-testid={`render-item-${k}`} data-is-table={isTable}>
      {item?.formData?.actualValue || ""}
    </div>
  ),
}));

describe("RenderTable - 表格渲染组件测试", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("基础渲染测试", () => {
    it("应该正确渲染空表格", () => {
      const item = {
        formData: {
          colNum: 2,
          isHead: false,
        },
        children: [],
      };

      const { container } = render(<RenderTable item={item} />);

      // 验证表格容器存在
      const tableContainer = container.querySelector(".w-full.col-span-3");
      expect(tableContainer).toBeInTheDocument();
    });

    it("应该正确渲染基本表格结构", () => {
      const item = {
        formData: {
          colNum: 2,
          isHead: true,
          paddingTop: 10,
          paddingRight: 15,
          paddingBottom: 10,
          paddingLeft: 15,
        },
        children: [
          {
            formData: { width: "50%" },
            children: [
              {
                compType: "plainText",
                formData: {
                  actualValue: "表头1",
                  textAlign: "center",
                },
              },
            ],
          },
          {
            formData: { width: "50%" },
            children: [
              {
                compType: "plainText",
                formData: {
                  actualValue: "表头2",
                  textAlign: "center",
                },
              },
            ],
          },
        ],
      };

      const { container } = render(<RenderTable item={item} />);

      // 验证表格内容
      expect(screen.getByText("表头1")).toBeInTheDocument();
      expect(screen.getByText("表头2")).toBeInTheDocument();

      // 验证表格结构
      const tableWrapper = container.querySelector(".w-full.col-span-3");
      expect(tableWrapper).toHaveStyle({
        paddingTop: "10px",
        paddingRight: "15px",
        paddingBottom: "10px",
        paddingLeft: "15px",
      });
    });
  });

  describe("单元格样式测试", () => {
    it("应该正确计算单元格宽度", () => {
      const item = {
        formData: { colNum: 3 },
        children: [
          {
            formData: { width: "40%" },
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "列1" },
              },
            ],
          },
          {
            formData: { width: "30%" },
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "列2" },
              },
            ],
          },
          {
            formData: { width: "30%" },
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "列3" },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      // 验证内容存在
      expect(screen.getByText("列1")).toBeInTheDocument();
      expect(screen.getByText("列2")).toBeInTheDocument();
      expect(screen.getByText("列3")).toBeInTheDocument();
    });

    it("应该正确处理表头样式", () => {
      const item = {
        formData: {
          colNum: 2,
          isHead: true,
        },
        children: [
          {
            formData: { width: "50%" },
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "表头1" },
              },
            ],
          },
          {
            formData: { width: "50%" },
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "表头2" },
              },
            ],
          },
          {
            formData: { width: "50%" },
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "数据1" },
              },
            ],
          },
          {
            formData: { width: "50%" },
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "数据2" },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      // 验证表头和数据都存在
      expect(screen.getByText("表头1")).toBeInTheDocument();
      expect(screen.getByText("表头2")).toBeInTheDocument();
      expect(screen.getByText("数据1")).toBeInTheDocument();
      expect(screen.getByText("数据2")).toBeInTheDocument();
    });
  });

  describe("组件类型渲染测试", () => {
    it("应该正确渲染 plainText 组件", () => {
      const item = {
        formData: { colNum: 1 },
        children: [
          {
            formData: { width: "100%" },
            children: [
              {
                compType: "plainText",
                formData: {
                  actualValue: "纯文本内容",
                  textAlign: "left",
                },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      expect(screen.getByText("纯文本内容")).toBeInTheDocument();
      expect(screen.getByTestId("render-item-0")).toHaveAttribute(
        "data-is-table",
        "true"
      );
    });

    it("应该正确渲染 radio 组件", () => {
      const item = {
        formData: { colNum: 1 },
        children: [
          {
            formData: { width: "100%" },
            children: [
              {
                compType: "radio",
                formData: {
                  formName: "选择项",
                  isReq: "required",
                },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      // 验证标签文本存在
      expect(screen.getByText("选择项")).toBeInTheDocument();
      expect(screen.getByTestId("render-item-0")).toHaveAttribute(
        "data-is-table",
        "true"
      );
    });

    it("应该正确渲染 input 组件", () => {
      const item = {
        formData: { colNum: 1 },
        children: [
          {
            formData: { width: "100%" },
            children: [
              {
                compType: "input",
                formData: {
                  formName: "输入框",
                  isReq: "required",
                },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      // 验证标签文本存在
      expect(screen.getByText("输入框")).toBeInTheDocument();
      expect(screen.getByTestId("render-item-0")).toHaveAttribute(
        "data-is-table",
        "true"
      );
    });

    it("应该正确渲染 selector 组件", () => {
      const item = {
        formData: { colNum: 1 },
        children: [
          {
            formData: { width: "100%" },
            children: [
              {
                compType: "selector",
                formData: {
                  formName: "选择器",
                  isReq: "required",
                },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      // 验证标签文本存在
      expect(screen.getByText("选择器")).toBeInTheDocument();
      expect(screen.getByTestId("render-item-0")).toHaveAttribute(
        "data-is-table",
        "true"
      );
    });

    it("应该正确处理未知组件类型", () => {
      const item = {
        formData: { colNum: 1 },
        children: [
          {
            formData: { width: "100%" },
            children: [
              {
                compType: "unknown",
                formData: {
                  formName: "未知组件",
                },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      // 未知组件类型应该不渲染任何内容
      expect(screen.queryByText("未知组件")).not.toBeInTheDocument();
    });
  });

  describe("边界条件测试", () => {
    it("应该处理空的 item 参数", () => {
      const { container } = render(<RenderTable item={null} />);

      // 应该渲染基本结构而不报错
      const tableContainer = container.querySelector(".w-full.col-span-3");
      expect(tableContainer).toBeInTheDocument();
    });

    it("应该处理缺少 formData 的情况", () => {
      const item = {
        children: [
          {
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "测试内容" },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      expect(screen.getByText("测试内容")).toBeInTheDocument();
    });

    it("应该处理缺少 children 的情况", () => {
      const item = {
        formData: {
          colNum: 2,
          isHead: false,
        },
      };

      const { container } = render(<RenderTable item={item} />);

      // 应该渲染空表格而不报错
      const tableContainer = container.querySelector(".w-full.col-span-3");
      expect(tableContainer).toBeInTheDocument();
    });

    it("应该正确处理默认列数", () => {
      const item = {
        children: [
          {
            formData: {},
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "测试" },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      expect(screen.getByText("测试")).toBeInTheDocument();
    });
  });

  describe("样式计算测试", () => {
    it("应该正确计算单元格内边距", () => {
      const item = {
        formData: { colNum: 1 },
        children: [
          {
            formData: {
              width: "100%",
              paddingLeft: 20,
              paddingRight: 25,
            },
            children: [
              {
                compType: "plainText",
                formData: { actualValue: "测试内容" },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      expect(screen.getByText("测试内容")).toBeInTheDocument();
    });

    it("应该正确处理文本对齐", () => {
      const item = {
        formData: { colNum: 1 },
        children: [
          {
            formData: { align: "left" },
            children: [
              {
                compType: "plainText",
                formData: {
                  actualValue: "左对齐文本",
                  textAlign: "left",
                },
              },
            ],
          },
        ],
      };

      render(<RenderTable item={item} />);

      expect(screen.getByText("左对齐文本")).toBeInTheDocument();
    });
  });
});
