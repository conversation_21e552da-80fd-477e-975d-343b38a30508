import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import * as DisposeModule from "../index";

// Mock the disposeForm module
vi.mock("../disposeForm", () => ({
  RenderDisposeFormUsingFormApi: ({ disposeData }: any) => (
    <div data-testid="dispose-form">
      <span>Dispose Form for {disposeData?.compType || "unknown"}</span>
    </div>
  ),
  disposeFormAtom: {
    init: { visible: false, data: null },
  },
  ComponentUsingFormApi: vi.fn(() => null),
  Dispose: vi.fn(() => null),
}));

// Test data factories
const createMockDisposeData = (overrides = {}) => ({
  compType: "input",
  compName: "测试输入框",
  itemId: "test-item-1",
  formData: {
    formName: "testField",
    placeHolder: "请输入内容",
    isReq: false,
  },
  ...overrides,
});

const createMockEventData = (overrides = {}) => ({
  idx: 0,
  current: [
    {
      compType: "input",
      formData: { formName: "field1" },
    },
  ],
  ...overrides,
});

describe("Dispose Index", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // ==================== Level 1: 基础结构测试 ====================

  describe("Basic Structure", () => {
    it("should import dispose module successfully", () => {
      expect(DisposeModule).toBeDefined();
      expect(typeof DisposeModule).toBe("object");
    });

    it("should export RenderDisposeFormUsingFormApi component", () => {
      expect(DisposeModule.RenderDisposeFormUsingFormApi).toBeDefined();
      expect(typeof DisposeModule.RenderDisposeFormUsingFormApi).toBe(
        "function"
      );
    });

    it("should export disposeFormAtom", () => {
      expect(DisposeModule.disposeFormAtom).toBeDefined();
      expect(typeof DisposeModule.disposeFormAtom).toBe("object");
    });

    it("should handle module initialization", () => {
      const atom = DisposeModule.disposeFormAtom;
      expect(atom.init).toBeDefined();
      expect(atom.init.visible).toBe(false);
      expect(atom.init.data).toBe(null);
    });
  });

  // ==================== Level 2: 属性验证测试 ====================

  describe("Props Validation", () => {
    it("should handle required props for RenderDisposeFormUsingFormApi", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;
      const mockDisposeData = createMockDisposeData();

      expect(mockDisposeData.compType).toBe("input");
      expect(mockDisposeData.formData).toBeDefined();
      expect(typeof RenderDisposeFormUsingFormApi).toBe("function");
    });

    it("should validate dispose data structure", () => {
      const disposeData = createMockDisposeData({
        compType: "select",
        formData: {
          formName: "selectField",
          candidateList: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
        },
      });

      expect(disposeData.compType).toBe("select");
      expect(disposeData.formData.candidateList).toHaveLength(2);
      expect(disposeData.formData.candidateList[0]).toHaveProperty("label");
      expect(disposeData.formData.candidateList[0]).toHaveProperty("value");
    });

    it("should handle empty dispose data gracefully", () => {
      const emptyData = createMockDisposeData({
        compType: "",
        formData: {},
      });

      expect(emptyData.compType).toBe("");
      expect(emptyData.formData).toEqual({});
    });

    it("should validate event data structure", () => {
      const eventData = createMockEventData({
        current: [
          { compType: "input", formData: { formName: "field1" } },
          { compType: "select", formData: { formName: "field2" } },
        ],
      });

      expect(eventData.idx).toBe(0);
      expect(eventData.current).toHaveLength(2);
      expect(eventData.current[0].compType).toBe("input");
      expect(eventData.current[1].compType).toBe("select");
    });
  });

  // ==================== Level 3: 交互逻辑测试 ====================

  describe("User Interactions", () => {
    it("should handle component rendering with dispose data", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;
      const mockDisposeData = createMockDisposeData();

      render(<RenderDisposeFormUsingFormApi disposeData={mockDisposeData} />);

      expect(screen.getByTestId("dispose-form")).toBeInTheDocument();
      expect(screen.getByText("Dispose Form for input")).toBeInTheDocument();
    });

    it("should handle null dispose data gracefully", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;

      render(<RenderDisposeFormUsingFormApi disposeData={null} />);

      expect(screen.getByTestId("dispose-form")).toBeInTheDocument();
      expect(screen.getByText("Dispose Form for unknown")).toBeInTheDocument();
    });

    it("should handle different component types", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;
      const selectDisposeData = createMockDisposeData({
        compType: "select",
        formData: {
          label: "测试选择框",
          options: [
            { label: "选项1", value: "1" },
            { label: "选项2", value: "2" },
          ],
        },
      });

      render(<RenderDisposeFormUsingFormApi disposeData={selectDisposeData} />);

      expect(screen.getByTestId("dispose-form")).toBeInTheDocument();
      expect(screen.getByText("Dispose Form for select")).toBeInTheDocument();
    });

    it("should handle module re-exports correctly", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;

      expect(typeof RenderDisposeFormUsingFormApi).toBe("function");

      const mockData = createMockDisposeData({ compType: "textarea" });
      render(<RenderDisposeFormUsingFormApi disposeData={mockData} />);

      expect(screen.getByText("Dispose Form for textarea")).toBeInTheDocument();
    });
  });

  // ==================== Level 4: 业务逻辑测试 ====================

  describe("Business Logic", () => {
    it("should handle complex component types correctly", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;
      const complexDisposeData = createMockDisposeData({
        compType: "table",
        formData: {
          colNum: 3,
          rowNum: 2,
          borderWidth: 1,
          borderColor: "#000000",
        },
      });

      render(
        <RenderDisposeFormUsingFormApi disposeData={complexDisposeData} />
      );

      expect(screen.getByTestId("dispose-form")).toBeInTheDocument();
      expect(screen.getByText("Dispose Form for table")).toBeInTheDocument();
    });

    it("should process custom component configurations", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;
      const customDisposeData = createMockDisposeData({
        compType: "custom",
        formData: {
          customProperty: "customValue",
          config: {
            enabled: true,
            settings: {},
          },
        },
      });

      render(<RenderDisposeFormUsingFormApi disposeData={customDisposeData} />);

      expect(screen.getByTestId("dispose-form")).toBeInTheDocument();
      expect(screen.getByText("Dispose Form for custom")).toBeInTheDocument();
    });

    it("should handle undefined component types gracefully", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;
      const undefinedDisposeData = {
        formData: { label: "无类型组件" },
      };

      render(
        <RenderDisposeFormUsingFormApi disposeData={undefinedDisposeData} />
      );

      expect(screen.getByTestId("dispose-form")).toBeInTheDocument();
      expect(screen.getByText("Dispose Form for unknown")).toBeInTheDocument();
    });

    it("should process empty component types", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;
      const emptyTypeDisposeData = createMockDisposeData({
        compType: "",
        formData: { label: "空类型组件" },
      });

      render(
        <RenderDisposeFormUsingFormApi disposeData={emptyTypeDisposeData} />
      );

      expect(screen.getByTestId("dispose-form")).toBeInTheDocument();
      expect(screen.getByText("Dispose Form for unknown")).toBeInTheDocument();
    });

    it("should validate atom initialization state", () => {
      const atom = DisposeModule.disposeFormAtom;

      expect(atom.init.visible).toBe(false);
      expect(atom.init.data).toBe(null);
      expect(typeof atom.init.visible).toBe("boolean");
      expect(atom.init.data).toBeNull();
    });

    it("should handle module export structure", () => {
      const exportedKeys = Object.keys(DisposeModule);

      expect(exportedKeys).toContain("RenderDisposeFormUsingFormApi");
      expect(exportedKeys).toContain("disposeFormAtom");
      expect(exportedKeys.length).toBeGreaterThanOrEqual(2);
    });
  });

  // ==================== Level 5: 集成测试 ====================

  describe("Integration Tests", () => {
    it("should work with complete dispose module workflow", () => {
      const { RenderDisposeFormUsingFormApi, disposeFormAtom } = DisposeModule;

      // 验证模块完整性
      expect(RenderDisposeFormUsingFormApi).toBeDefined();
      expect(disposeFormAtom).toBeDefined();

      // 验证组件渲染
      const mockData = createMockDisposeData();
      render(<RenderDisposeFormUsingFormApi disposeData={mockData} />);

      expect(screen.getByTestId("dispose-form")).toBeInTheDocument();
    });

    it("should maintain stability across different component types", () => {
      const { RenderDisposeFormUsingFormApi } = DisposeModule;
      const componentTypes = ["input", "select", "textarea"];

      componentTypes.forEach((compType) => {
        const disposeData = createMockDisposeData({ compType });

        // 验证数据结构
        expect(disposeData.compType).toBe(compType);
        expect(disposeData.formData).toBeDefined();
        expect(typeof RenderDisposeFormUsingFormApi).toBe("function");
      });
    });

    it("should handle complex integration scenarios", () => {
      const { RenderDisposeFormUsingFormApi, disposeFormAtom } = DisposeModule;

      // 复杂场景：多个组件类型的数据
      const complexScenarios = [
        createMockDisposeData({
          compType: "input",
          formData: { formName: "field1", isReq: true },
        }),
        createMockDisposeData({
          compType: "select",
          formData: {
            formName: "field2",
            candidateList: [{ label: "选项1", value: "1" }],
          },
        }),
        createMockDisposeData({
          compType: "table",
          formData: { colNum: 3, rowNum: 2 },
        }),
      ];

      // 验证每个场景的数据结构
      complexScenarios.forEach((scenario) => {
        expect(scenario.compType).toBeDefined();
        expect(scenario.formData).toBeDefined();
        expect(typeof scenario.compType).toBe("string");
        expect(typeof scenario.formData).toBe("object");
      });

      // 验证atom状态保持稳定
      expect(disposeFormAtom.init.visible).toBe(false);
      expect(disposeFormAtom.init.data).toBe(null);

      // 验证组件函数可用性
      expect(typeof RenderDisposeFormUsingFormApi).toBe("function");
    });
  });
});
