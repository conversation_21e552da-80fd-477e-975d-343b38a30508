import { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { BaseTicket } from '../base';

// Mock dependencies
vi.mock('@reactivers/hooks', () => ({
  useAuth: () => ({
    user: {
      userInfo: {
        id: 'test-user-id',
        name: 'Test User'
      }
    }
  })
}));

vi.mock('@tanstack/react-query', () => ({
  useQuery: vi.fn(() => ({
    data: { name: 'Test Employee', id: 'emp-1' },
    isLoading: false,
    error: null
  }))
}));

vi.mock('jotai', () => ({
  useAtom: () => [null, vi.fn()],
}));

vi.mock('jotai/utils', () => ({
  useResetAtom: () => vi.fn(),
}));

vi.mock('react-router-dom', () => ({
  useParams: () => ({ id: 'test-id', cid: 'test-cid' })
}));

vi.mock('api', () => ({
  getEmployee: vi.fn(() => Promise.resolve({ name: 'Test Employee' })),
  getJobSliceCode: vi.fn(() => Promise.resolve({ code: 'TEST-CODE' }))
}));

vi.mock('atoms', () => ({
  jobAppointmentFilterAtom: {}
}));

// Mock Semi UI components
vi.mock('@douyinfe/semi-ui', () => ({
  Col: ({ children, span }: any) => <div data-testid={`col-${span}`}>{children}</div>,
  Form: {
    Section: ({ children, text }: any) => (
      <div data-testid="form-section">
        <h3>{text}</h3>
        {children}
      </div>
    ),
    Input: ({ field, label, rules, disabled }: any) => (
      <div data-testid={`form-input-${field}`}>
        <label>{label}</label>
        <input 
          name={field} 
          disabled={disabled}
          required={rules?.[0]?.required}
        />
      </div>
    ),
    DatePicker: ({ field, label, rules, type, presets }: any) => (
      <div data-testid={`form-datepicker-${field}`}>
        <label>{label}</label>
        <input 
          name={field} 
          type="datetime-local"
          required={rules?.[0]?.required}
        />
        {presets && <div data-testid="date-presets">{presets.length} presets</div>}
      </div>
    )
  },
  Row: ({ children, gutter }: any) => <div data-testid={`row-gutter-${gutter}`}>{children}</div>,
  useFormApi: () => ({
    setValue: vi.fn(),
    getValue: vi.fn()
  })
}));

// Mock custom components
vi.mock('components', () => ({
  DepartmentSearch: ({ field, label, isRequired }: any) => (
    <div data-testid={`department-search-${field}`}>
      <label>{label}</label>
      <input name={field} required={isRequired} />
    </div>
  ),
  EmployeeSearch: ({ field, label, isRequired }: any) => (
    <div data-testid={`employee-search-${field}`}>
      <label>{label}</label>
      <input name={field} required={isRequired} />
    </div>
  ),
  JobAppointmentTableModal: ({ children }: any) => (
    <div data-testid="job-appointment-modal">{children}</div>
  )
}));

describe('BaseTicket', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('应该正确渲染基础信息表单', () => {
    const mockTmpl = {
      allowNoAppointment: 1
    };

    render(<BaseTicket tmpl={mockTmpl} />);

    // 验证基础信息section存在
    expect(screen.getByTestId('form-section')).toBeInTheDocument();
    expect(screen.getByText('基础信息')).toBeInTheDocument();
  });

  it('应该渲染所有必要的表单字段', () => {
    const mockTmpl = {
      allowNoAppointment: 1
    };

    render(<BaseTicket tmpl={mockTmpl} />);

    // 验证核心表单字段
    expect(screen.getByTestId('form-input-name')).toBeInTheDocument();
    expect(screen.getByTestId('form-input-code')).toBeInTheDocument();
    expect(screen.getByTestId('employee-search-applyPersonId')).toBeInTheDocument();
    expect(screen.getByTestId('department-search-applyDepartmentId')).toBeInTheDocument();
    expect(screen.getByTestId('form-datepicker-planBeginTime')).toBeInTheDocument();
    expect(screen.getByTestId('form-datepicker-planEndTime')).toBeInTheDocument();
  });

  it('应该正确设置表单字段标签', () => {
    const mockTmpl = {
      allowNoAppointment: 1
    };

    render(<BaseTicket tmpl={mockTmpl} />);

    // 验证字段标签
    expect(screen.getByText('作业票名称')).toBeInTheDocument();
    expect(screen.getByText('作业票编号')).toBeInTheDocument();
    expect(screen.getByText('申请人')).toBeInTheDocument();
    expect(screen.getByText('申请单位')).toBeInTheDocument();
    expect(screen.getByText('计划开始时间')).toBeInTheDocument();
    expect(screen.getByText('计划结束时间')).toBeInTheDocument();
  });

  it('应该正确设置必填字段', () => {
    const mockTmpl = {
      allowNoAppointment: 1
    };

    render(<BaseTicket tmpl={mockTmpl} />);

    // 验证必填字段
    const nameInput = screen.getByTestId('form-input-name').querySelector('input');
    const codeInput = screen.getByTestId('form-input-code').querySelector('input');
    const applyPersonInput = screen.getByTestId('employee-search-applyPersonId').querySelector('input');
    const applyDeptInput = screen.getByTestId('department-search-applyDepartmentId').querySelector('input');

    expect(nameInput).toHaveAttribute('required');
    expect(codeInput).toHaveAttribute('required');
    expect(applyPersonInput).toHaveAttribute('required');
    expect(applyDeptInput).toHaveAttribute('required');
  });

  it('应该正确设置作业票编号为禁用状态', () => {
    const mockTmpl = {
      allowNoAppointment: 1
    };

    render(<BaseTicket tmpl={mockTmpl} />);

    const codeInput = screen.getByTestId('form-input-code').querySelector('input');
    expect(codeInput).toHaveAttribute('disabled');
  });

  it('应该为日期选择器提供预设选项', () => {
    const mockTmpl = {
      allowNoAppointment: 1
    };

    render(<BaseTicket tmpl={mockTmpl} />);

    // 验证日期预设
    const datePresets = screen.getAllByTestId('date-presets');
    expect(datePresets).toHaveLength(2); // planBeginTime 和 planEndTime
    datePresets.forEach(preset => {
      expect(preset).toHaveTextContent('2 presets');
    });
  });

  it('应该正确处理空的tmpl参数', () => {
    render(<BaseTicket tmpl={null} />);

    // 组件应该仍然能够渲染
    expect(screen.getByTestId('form-section')).toBeInTheDocument();
    expect(screen.getByText('基础信息')).toBeInTheDocument();
  });

  it('应该正确处理未定义的tmpl参数', () => {
    render(<BaseTicket tmpl={undefined} />);

    // 组件应该仍然能够渲染
    expect(screen.getByTestId('form-section')).toBeInTheDocument();
    expect(screen.getByText('基础信息')).toBeInTheDocument();
  });

  it('应该使用正确的栅格布局', () => {
    const mockTmpl = {
      allowNoAppointment: 1
    };

    render(<BaseTicket tmpl={mockTmpl} />);

    // 验证Row组件的gutter设置
    expect(screen.getByTestId('row-gutter-16')).toBeInTheDocument();

    // 验证Col组件的span设置
    const cols = screen.getAllByTestId(/^col-8$/);
    expect(cols.length).toBeGreaterThan(0);
  });
});
