import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { ProcessesTicket } from "../processes";

// Mock dependencies
vi.mock("components", () => ({
  EmployeePicker: ({ callback, show, setShow, options, serviceRange }: any) => (
    <div
      data-testid="employee-picker"
      style={{ display: show ? "block" : "none" }}
    >
      <div data-testid="picker-options">{JSON.stringify(options)}</div>
      <div data-testid="service-range">{JSON.stringify(serviceRange)}</div>
      <button
        data-testid="picker-confirm"
        onClick={() =>
          callback([{ id: "emp-1", name: "Test Employee", type: "internal" }])
        }
      >
        确认选择
      </button>
      <button data-testid="picker-close" onClick={() => setShow(false)}>
        关闭
      </button>
    </div>
  ),
  employeePickerCallback: vi.fn(),
  EmployeeSearch: ({
    field,
    label,
    pickKeys,
    multiple,
    isRequired,
    serviceRange,
  }: any) => (
    <div data-testid={`employee-search-${field}`}>
      <label>{label}</label>
      <input
        name={field}
        required={isRequired}
        multiple={multiple}
        data-pick-keys={JSON.stringify(pickKeys)}
        data-service-range={JSON.stringify(serviceRange)}
      />
    </div>
  ),
}));

// Create mock form API functions
const mockFormApi = {
  setValue: vi.fn(),
  getValue: vi.fn(() => []),
};

// Mock Semi UI components
vi.mock("@douyinfe/semi-ui", () => ({
  Col: ({ children, span }: any) => (
    <div data-testid={`col-${span}`}>{children}</div>
  ),
  Form: {
    Section: ({ children, text }: any) => (
      <div data-testid="form-section">
        <h3>{text}</h3>
        {children}
      </div>
    ),
    Input: ({ field, label, rules, onClick, placeholder }: any) => (
      <div data-testid={`form-input-${field}`}>
        <label>{label}</label>
        <input
          name={field}
          placeholder={placeholder}
          required={rules?.[0]?.required}
          onClick={onClick}
        />
      </div>
    ),
  },
  Row: ({ children, gutter }: any) => (
    <div data-testid={`row-gutter-${gutter}`}>{children}</div>
  ),
  TextArea: ({ autosize, value }: any) => (
    <textarea data-testid="debug-textarea" data-autosize={autosize}>
      {value}
    </textarea>
  ),
  useFormApi: () => mockFormApi,
  useFormState: () => ({
    values: { test: "value" },
  }),
}));

describe("ProcessesTicket", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该正确渲染审批人员section", () => {
    const mockTmpl = {
      processTemplate: [],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    expect(screen.getByTestId("form-section")).toBeInTheDocument();
    expect(screen.getByText("审批人员")).toBeInTheDocument();
  });

  it("应该正确处理空的processTemplate", () => {
    const mockTmpl = {
      processTemplate: [],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    const row = screen.getByTestId("row-gutter-16");
    expect(row).toBeInTheDocument();
    expect(row.children).toHaveLength(0);
  });

  it("应该正确渲染nodeAssignMethod为1的审批节点", () => {
    const mockTmpl = {
      processTemplate: [
        {
          name: "部门主管审批",
          nodeAssignMethod: 1,
          candidatePerson: [],
          peoplePickTypeList: [1, 2],
        },
      ],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    expect(screen.getByTestId("col-8")).toBeInTheDocument();
  });

  it("应该跳过nodeAssignMethod不为1的节点", () => {
    const mockTmpl = {
      processTemplate: [
        {
          name: "自动审批",
          nodeAssignMethod: 2,
          candidatePerson: [],
          peoplePickTypeList: [1, 2],
        },
      ],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    const row = screen.getByTestId("row-gutter-16");
    expect(row.children).toHaveLength(0);
  });

  it("candidatePerson为空时应该渲染ApproverComponent", () => {
    const mockTmpl = {
      processTemplate: [
        {
          name: "部门主管审批",
          nodeAssignMethod: 1,
          candidatePerson: [],
          peoplePickTypeList: [1, 2],
        },
      ],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    // ApproverComponent会渲染一个可点击的输入框
    const approverInput = screen.getByDisplayValue
      ? screen.queryByDisplayValue("")
      : screen.getByRole("textbox");

    if (!approverInput) {
      // 如果没有找到输入框，检查是否有相关的表单元素
      const formInputs = screen.getAllByTestId(/form-input-/);
      expect(formInputs.length).toBeGreaterThan(0);
    }
  });

  it("candidatePerson有值时应该渲染EmployeeSearch", () => {
    const mockTmpl = {
      processTemplate: [
        {
          name: "部门主管审批",
          nodeAssignMethod: 1,
          candidatePerson: ["person-1", "person-2"],
          peoplePickTypeList: [1, 2],
        },
      ],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    expect(
      screen.getByTestId("employee-search-jobProcessesInfo[0].candidatePersons")
    ).toBeInTheDocument();
    expect(screen.getByText("部门主管审批")).toBeInTheDocument();
  });

  it("isSpecial为true时应该渲染验收人选择", () => {
    const mockTmpl = {
      processTemplate: [],
    };

    render(<ProcessesTicket tmpl={mockTmpl} isSpecial={true} />);

    expect(
      screen.getByTestId("employee-search-acceptCandidateIds")
    ).toBeInTheDocument();
    expect(screen.getByText("验收人")).toBeInTheDocument();
  });

  it("isSpecial为false时不应该渲染验收人选择", () => {
    const mockTmpl = {
      processTemplate: [],
    };

    render(<ProcessesTicket tmpl={mockTmpl} isSpecial={false} />);

    expect(
      screen.queryByTestId("employee-search-acceptCandidateIds")
    ).not.toBeInTheDocument();
    expect(screen.queryByText("验收人")).not.toBeInTheDocument();
  });

  it("应该在useEffect中设置流程名称", () => {
    const mockTmpl = {
      processTemplate: [
        {
          name: "部门主管审批",
          nodeAssignMethod: 1,
          candidatePerson: [],
        },
      ],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    expect(mockFormApi.setValue).toHaveBeenCalledWith(
      "jobProcessesInfo[0].name",
      "部门主管审批"
    );
  });

  it("应该正确处理空的流程名称", () => {
    const mockTmpl = {
      processTemplate: [
        {
          nodeAssignMethod: 1,
          candidatePerson: [],
        },
      ],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    expect(mockFormApi.setValue).toHaveBeenCalledWith(
      "jobProcessesInfo[0].name",
      ""
    );
  });

  it("应该正确处理null或undefined的tmpl", () => {
    render(<ProcessesTicket tmpl={null} />);

    expect(screen.getByTestId("form-section")).toBeInTheDocument();
    expect(screen.getByText("审批人员")).toBeInTheDocument();
  });

  it("应该正确设置EmployeeSearch的属性", () => {
    const mockTmpl = {
      processTemplate: [
        {
          name: "部门主管审批",
          nodeAssignMethod: 1,
          candidatePerson: ["person-1", "person-2"],
        },
      ],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    const employeeSearch = screen.getByTestId(
      "employee-search-jobProcessesInfo[0].candidatePersons"
    );
    const input = employeeSearch.querySelector("input");

    expect(input).toHaveAttribute("required");
    expect(input).toHaveAttribute("multiple");
    expect(input).toHaveAttribute(
      "data-pick-keys",
      JSON.stringify(["person-1", "person-2"])
    );
    expect(input).toHaveAttribute("data-service-range", JSON.stringify([1, 3]));
  });

  it("验收人EmployeeSearch应该有正确的属性", () => {
    const mockTmpl = {
      processTemplate: [],
    };

    render(<ProcessesTicket tmpl={mockTmpl} isSpecial={true} />);

    const acceptorSearch = screen.getByTestId(
      "employee-search-acceptCandidateIds"
    );
    const input = acceptorSearch.querySelector("input");

    expect(input).toHaveAttribute("required");
    expect(input).toHaveAttribute("multiple");
    expect(input).toHaveAttribute("data-service-range", JSON.stringify([1]));
  });

  it("应该使用正确的栅格布局", () => {
    const mockTmpl = {
      processTemplate: [
        {
          name: "审批1",
          nodeAssignMethod: 1,
          candidatePerson: [],
        },
      ],
    };

    render(<ProcessesTicket tmpl={mockTmpl} />);

    expect(screen.getByTestId("row-gutter-16")).toBeInTheDocument();
    expect(screen.getByTestId("col-8")).toBeInTheDocument();
  });
});
