import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { render, screen, waitFor } from "@testing-library/react";
import { searchJsTemplateList } from "api";
import { Provider } from "jotai";
import { <PERSON><PERSON>er<PERSON>outer } from "react-router-dom";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { JsTemplateUserContent } from "../content";

// Mock API functions
vi.mock("api", () => ({
  delJsTemplate: vi.fn(),
  delJsTemplates: vi.fn(),
  getJsTemplate: vi.fn(),
  searchJsTemplateList: vi.fn(),
}));

// Mock hooks
vi.mock("hooks", () => ({
  useBtnHooks: vi.fn(() => (type: string, element: any) => element),
  useDeleteHooks: vi.fn(() => [
    vi.fn(), // removeBtn
    { selectedRowKeys: [], onSelectChange: vi.fn() }, // rowSelection
    vi.fn(), // removesBtn
  ]),
}));

// Mock react-router-dom
vi.mock("react-router-dom", async () => {
  const actual = await vi.importActual("react-router-dom");
  return {
    ...actual,
    useLoaderData: vi.fn(() => ({})),
    useLocation: vi.fn(() => ({ pathname: "/test" })),
    useNavigate: vi.fn(() => vi.fn()),
  };
});

// Mock useCopyToClipboard
vi.mock("usehooks-ts", () => ({
  useCopyToClipboard: vi.fn(() => ["", vi.fn()]),
}));

// Mock pages/ticket/modal
vi.mock("../../modal", () => ({
  jsonExportModalAtom: {
    visible: false,
    data: null,
    id: null,
    isModal: false,
  },
}));

// Mock Semi UI components
vi.mock("@douyinfe/semi-ui", async () => {
  const actual = await vi.importActual("@douyinfe/semi-ui");
  return {
    ...actual,
    Toast: {
      success: vi.fn(),
      error: vi.fn(),
    },
    Table: ({ dataSource, columns, loading }: any) => (
      <div data-testid="template-table">
        {loading ? (
          <div>Loading...</div>
        ) : (
          <div>
            {dataSource?.map((item: any, index: number) => (
              <div key={index} data-testid={`table-row-${index}`}>
                {item.name || item.title || `Row ${index}`}
              </div>
            ))}
          </div>
        )}
      </div>
    ),
    Button: ({ children, onClick }: any) => (
      <button onClick={onClick} data-testid="button">
        {children}
      </button>
    ),
    ButtonGroup: ({ children }: any) => <div>{children}</div>,
    Dropdown: ({ children, render }: any) => (
      <div>
        {children}
        {render && <div data-testid="dropdown-menu">{render}</div>}
      </div>
    ),
    Tooltip: ({ children }: any) => <div>{children}</div>,
  };
});

// Mock TableConfig component
vi.mock("components", () => ({
  TableConfig: ({ visible, columns, handleClose, handleSave }: any) =>
    visible ? (
      <div data-testid="table-config">
        <button onClick={() => handleClose(false)}>Close</button>
        <button onClick={() => handleSave(columns)}>Save</button>
      </div>
    ) : null,
}));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <Provider>
        <BrowserRouter>{children}</BrowserRouter>
      </Provider>
    </QueryClientProvider>
  );
};

describe("JsTemplateUserContent", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该正确渲染模板列表组件", () => {
    render(
      <TestWrapper>
        <JsTemplateUserContent />
      </TestWrapper>
    );

    expect(screen.getByTestId("template-table")).toBeInTheDocument();
  });

  it("应该显示加载状态", () => {
    const mockSearchJsTemplateList = vi.mocked(searchJsTemplateList);
    mockSearchJsTemplateList.mockImplementation(
      () => new Promise(() => {}) // Never resolves
    );

    render(
      <TestWrapper>
        <JsTemplateUserContent />
      </TestWrapper>
    );

    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  it("应该正确处理模板数据", async () => {
    const mockData = {
      data: {
        results: [
          { id: "1", name: "模板1", title: "测试模板1" },
          { id: "2", name: "模板2", title: "测试模板2" },
        ],
      },
    };

    const mockSearchJsTemplateList = vi.mocked(searchJsTemplateList);
    mockSearchJsTemplateList.mockResolvedValue(mockData);

    render(
      <TestWrapper>
        <JsTemplateUserContent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId("table-row-0")).toBeInTheDocument();
      expect(screen.getByTestId("table-row-1")).toBeInTheDocument();
    });
  });

  it("应该处理空数据情况", async () => {
    const mockData = {
      data: {
        results: [],
      },
    };

    const mockSearchJsTemplateList = vi.mocked(searchJsTemplateList);
    mockSearchJsTemplateList.mockResolvedValue(mockData);

    render(
      <TestWrapper>
        <JsTemplateUserContent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId("template-table")).toBeInTheDocument();
      expect(screen.queryByTestId("table-row-0")).not.toBeInTheDocument();
    });
  });

  it("应该处理API错误", async () => {
    const mockSearchJsTemplateList = vi.mocked(searchJsTemplateList);
    mockSearchJsTemplateList.mockRejectedValue(new Error("API Error"));

    render(
      <TestWrapper>
        <JsTemplateUserContent />
      </TestWrapper>
    );

    // 组件应该仍然渲染，但没有数据
    await waitFor(() => {
      expect(screen.getByTestId("template-table")).toBeInTheDocument();
    });
  });

  it("应该正确处理分页", async () => {
    const mockData = {
      data: {
        results: [{ id: "1", name: "模板1" }],
        totalCount: 100,
        pageNumber: 1,
        pageSize: 10,
      },
    };

    const mockSearchJsTemplateList = vi.mocked(searchJsTemplateList);
    mockSearchJsTemplateList.mockResolvedValue(mockData);

    render(
      <TestWrapper>
        <JsTemplateUserContent />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByTestId("template-table")).toBeInTheDocument();
    });
  });
});
