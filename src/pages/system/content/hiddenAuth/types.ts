export interface AuthFormValues {
  loginType: number;

  // 钉钉相关字段
  dingtalkAgentId?: string;
  dingtalkAppKey?: string;
  dingtalkAppSecret?: string;
  dingtalkRedirectUri?: string;
  dingtalkRedirectMobileUri?: string;
  dingtalkRedirectWapUri?: string;

  // 蓝凌OA相关字段
  appid?: string;
  token?: string;
  baseUrl?: string;
  queryTimeout?: number;

  // 第三方同步配置
  statusIsOn?: number;
  copyIsOn?: number;
}

export type FormValues = Record<string, any>;
export type CachedValues = Record<number, FormValues>; // pluginId -> formValues
