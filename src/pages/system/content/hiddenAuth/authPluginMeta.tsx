import {
  getIcon,
  pickAuthProviderColor,
} from "components/region_plugins/utils";
import type React from "react";

import {
  DingTalkAuthForm,
  LandrayOAAuthForm,
  SystemBuiltinAuthForm,
} from "./components";

/** 展示 & UI 需要的认证插件信息 */
export interface AuthPluginInfo {
  readonly name: string;
  readonly description: string;
  readonly icon: React.ReactNode;
  readonly color: string;
  readonly version: string;
  readonly lastUpdate: string;
  readonly enabled?: boolean;
}

/** 元数据项：id + info (+ Form) */
export interface AuthPluginMeta {
  readonly id: number;
  readonly info: AuthPluginInfo;
  /** 具体认证插件表单组件，必须存在 */
  readonly Form: React.FC;
}

/**
 * 认证插件清单 —— 单一数据源
 * 后续增删插件只需改此处。
 */
export const AUTH_PLUGIN_META: ReadonlyArray<AuthPluginMeta> = [
  {
    id: 1, // 系统内置
    info: {
      name: "系统内置",
      description: "使用系统内置的用户认证方式",
      icon: getIcon("system"),
      color: pickAuthProviderColor("S"),
      version: "v1.0.0",
      lastUpdate: "2025-01-29",
      enabled: true,
    },
    Form: SystemBuiltinAuthForm,
  },
  {
    id: 2, // 钉钉
    info: {
      name: "钉钉认证",
      description: "使用钉钉企业认证登录",
      icon: getIcon("dingtalk"),
      color: pickAuthProviderColor("D"),
      version: "v1.2.0",
      lastUpdate: "2025-01-29",
      enabled: true,
    },
    Form: DingTalkAuthForm,
  },
  {
    id: 3, // 蓝凌OA
    info: {
      name: "蓝凌OA",
      description: "使用蓝凌OA系统认证登录",
      icon: getIcon("landray"),
      color: pickAuthProviderColor("L"),
      version: "v1.0.0",
      lastUpdate: "2025-07-29",
      enabled: true,
    },
    Form: LandrayOAAuthForm,
  },
] as const;
