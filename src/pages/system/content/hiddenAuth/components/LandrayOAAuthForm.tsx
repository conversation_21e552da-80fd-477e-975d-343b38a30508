import { IconChevronDown, IconChevronUp, IconCopy } from "@douyinfe/semi-icons";
import { Button, Col, Form, Row, Toast } from "@douyinfe/semi-ui";
import { ISON_TYPE_MAP } from "components";
import React, { useState } from "react";
import { copyToClipboard } from "utils/clipboard";
import { LandrayOASSOCallbackRoute } from "utils/routerConstants";

export const LandrayOAAuthForm: React.FC = () => {
  const rules = [{ required: true, message: "此为必填项" }];
  const [isCallbackSectionCollapsed, setIsCallbackSectionCollapsed] =
    useState(true);

  const handleCopyCallbackUrl = async () => {
    const callbackUrl = `${window.location.origin}${LandrayOASSOCallbackRoute}`;

    await copyToClipboard(callbackUrl, {
      successMessage: "回调地址已复制到剪贴板",
      errorMessage: "复制失败，请手动复制地址",
      onSuccess: () => Toast.success("回调地址已复制到剪贴板"),
      onError: () => Toast.error("复制失败，请手动复制地址"),
    });
  };

  const toggleCallbackSection = () => {
    setIsCallbackSectionCollapsed(!isCallbackSectionCollapsed);
  };

  return (
    <>
      <Form.Section
        text={
          <div className="flex items-center justify-between w-full">
            <span>回调配置</span>
            <Button
              theme="borderless"
              icon={
                isCallbackSectionCollapsed ? (
                  <IconChevronDown />
                ) : (
                  <IconChevronUp />
                )
              }
              onClick={toggleCallbackSection}
              size="small"
              type="tertiary"
            >
              {isCallbackSectionCollapsed ? "展开" : "收起"}
            </Button>
          </div>
        }
      >
        {!isCallbackSectionCollapsed && (
          <Row gutter={24}>
            <Col span={24}>
              <div className="bg-gray-50 p-4 rounded-lg border">
                <div className="flex items-center justify-between mb-2">
                  <label className="text-sm font-medium text-gray-700">
                    回调地址
                  </label>
                  <Button
                    theme="borderless"
                    icon={<IconCopy />}
                    onClick={handleCopyCallbackUrl}
                    size="small"
                    type="tertiary"
                  >
                    复制
                  </Button>
                </div>
                <div className="bg-white p-3 rounded border font-mono text-sm text-gray-800 break-all">
                  {`${window.location.origin}${LandrayOASSOCallbackRoute}`}
                </div>
                <div className="text-xs text-gray-500 mt-2">
                  请将此地址配置到蓝凌OA系统中，用于单点登录回调
                </div>
              </div>
            </Col>
          </Row>
        )}
      </Form.Section>

      <Form.Section text="应用配置">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Input
              label="应用ID"
              field="appId"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="应用Token"
              field="token"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input
              label="信息同步地址"
              field="baseUrl"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input
              label="信息查询地址"
              field="queryUri"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.InputNumber
              label="同步超时"
              field="queryTimeout"
              suffix="秒"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.InputNumber
              label="最大同步层级"
              field="maxLevel"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Form.Section>

      <Form.Section text="第三方认证信息同步配置">
        <Row gutter={24}>
          <Col span={12}>
            <Form.RadioGroup
              label="离职同步是否打开"
              field="statusIsOn"
              rules={rules}
            >
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="账户同步是否打开"
              field="copyIsOn"
              rules={rules}
            >
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.RadioGroup
              label="部门同步是否打开"
              field="departmentIsOn"
              rules={rules}
            >
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
      </Form.Section>
    </>
  );
};
