import { Col, Form, Row } from "@douyinfe/semi-ui";
import { ISON_TYPE_MAP } from "components";
import React from "react";

export const DingTalkAuthForm: React.FC = () => {
  const rules = [{ required: true, message: "此为必填项" }];

  return (
    <>
      <Form.Section text="钉钉应用配置">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Input
              label="钉钉应用agentId"
              field="dingtalkAgentId"
              trigger="blur"
              rules={rules}
            />
          </Col>
          <Col span={12}>
            <Form.Input
              label="钉钉应用AppKey"
              field="dingtalkAppKey"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input
              label="钉钉应用AppSecret"
              field="dingtalkAppSecret"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input
              label="钉钉网页端回调地址"
              field="dingtalkRedirectUri"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input
              label="钉钉App端回调地址"
              field="dingtalkRedirectMobileUri"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Input
              label="钉钉移动网页端回调地址"
              field="dingtalkRedirectWapUri"
              trigger="blur"
              rules={rules}
            />
          </Col>
        </Row>
      </Form.Section>

      <Form.Section text="第三方认证信息同步配置">
        <Row gutter={24}>
          <Col span={12}>
            <Form.RadioGroup
              label="离职同步是否打开"
              field="statusIsOn"
              rules={rules}
            >
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup
              label="账户同步是否打开"
              field="copyIsOn"
              rules={rules}
            >
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>
                  {item.name}
                </Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
      </Form.Section>
    </>
  );
};
