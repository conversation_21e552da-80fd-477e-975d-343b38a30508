import { useQuery } from "@tanstack/react-query";
import { useAtom } from "jotai";
import { useMemo } from "react";
import {
  ProgressItem,
  UseProgressDataConfig,
  UseProgressDataReturn,
} from "../components/progress-timeline/types";

/**
 * Custom hook for fetching and managing progress data
 *
 * This hook abstracts the common pattern of fetching progress data
 * using React Query and managing atom state with Jotai. It supports
 * different API functions and atoms through configuration.
 *
 * @param config - Configuration object containing apiFunction, atom, and queryKey
 * @returns Object containing loading state, data, and error
 */
export const useProgressData = (
  config: UseProgressDataConfig
): UseProgressDataReturn => {
  const { apiFunction, atom, queryKey } = config;

  // Get the current atom value (contains id and show state)
  const [info] = useAtom(atom);

  // Use React Query to fetch data
  const { isLoading, data, error } = useQuery({
    queryKey: [queryKey, info?.id],
    queryFn: () => {
      if (!info?.id) {
        throw new Error("No ID provided for progress data fetch");
      }
      return apiFunction(info.id);
    },
    enabled: Boolean(info?.id),
    // Add some sensible defaults for caching and error handling
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Standardize the data format
  const standardizedData = useMemo((): ProgressItem[] => {
    if (!data?.data) {
      return [];
    }

    // Ensure the data conforms to our ProgressItem interface
    const rawData = data.data;

    // If data is already an array, use it directly
    if (Array.isArray(rawData)) {
      return rawData.map(
        (item: any, index: number): ProgressItem => ({
          id: String(
            item.id ||
              (item.name
                ? `progress-${item.name}-${index}`
                : `progress-item-${index}`)
          ),
          name: item.name || "",
          status: item.status || 1,
          subprocessList: (item.subprocessList || []).map(
            (subprocess: any) => ({
              name: subprocess.name || "",
              status: subprocess.status || 1,
              finishTime: subprocess.finishTime,
              candidateList: subprocess.candidateList || [],
              finishList: subprocess.finishList || [],
            })
          ),
        })
      );
    }

    // If data is not an array, wrap it in an array
    return [
      {
        id: String(
          rawData.id ||
            (rawData.name ? `progress-${rawData.name}-0` : `progress-item-0`)
        ),
        name: rawData.name || "",
        status: rawData.status || 1,
        subprocessList: (rawData.subprocessList || []).map(
          (subprocess: any) => ({
            name: subprocess.name || "",
            status: subprocess.status || 1,
            finishTime: subprocess.finishTime,
            candidateList: subprocess.candidateList || [],
            finishList: subprocess.finishList || [],
          })
        ),
      },
    ];
  }, [data]);

  return {
    isLoading,
    data: standardizedData,
    error: error as Error | undefined,
  };
};
