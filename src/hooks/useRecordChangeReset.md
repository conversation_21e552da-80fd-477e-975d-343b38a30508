# useRecordChangeReset

> **位置**：`src/hooks/useRecordChangeReset.ts`
>
> 当业务上下文（如详情记录 ID、路由参数、Tab Key 等）变化时，自动重置传入的 Jotai `atom`。常用于过滤器或表单状态在“切换记录 / 切换 Tab”时的清理。

## 使用场景

- 详情页侧栏：切换左侧列表的记录时，需要清空右侧过滤器状态。
- Tab 页面：用户在同一组件实例中切换不同工作空间，需要重置内部筛选或表单。
- 任何“组件不卸载，但业务上下文已变”的场景。

## API

```ts
useRecordChangeReset<T>(atom: PrimitiveAtom<T>, contextKey: string \| number \| undefined): void
```

| 参数         | 类型                            | 说明                                     |
| ------------ | ------------------------------- | ---------------------------------------- |
| `atom`       | `PrimitiveAtom<T>`              | 需要重置的 Jotai 原子                    |
| `contextKey` | `string \| number \| undefined` | 能唯一标识业务上下文的键，变化即触发重置 |

> **幂等说明**：若多次调用 `reset()` 对业务无副作用，故重复触发也安全。

## 示例

### 1. 在详情页中使用

```tsx
import { useRecordChangeReset } from "hooks/useRecordChangeReset";
import { detailFilterAtom } from "@/atoms/xxx";

const DetailSide = ({ currentId }: { currentId: string }) => {
  useRecordChangeReset(detailFilterAtom, currentId);
  // ... 其他逻辑
};
```

### 2. 在 Tab 布局中使用

```tsx
const TabContent = () => {
  const { activeKey } = useTabsContext();
  useRecordChangeReset(tabFilterAtom, activeKey);
  // ...
};
```

## 实现要点

1. 通过 `useRef` 缓存上一次的 `contextKey` 值。
2. 在 `useEffect` 里比较新旧 `contextKey`，若不同则执行 `reset()`。
3. 依赖 `jotai/utils` 的 `useResetAtom`，无需手动分发空对象。

```ts
const reset = useResetAtom(atom)
...
if (prevKeyRef.current !== contextKey) {
  reset()
  prevKeyRef.current = contextKey
}
```

## 相关规范

- 已在 `.cursorrules` 的“状态管理策略”与“开发检查清单”添加检查项：
  - **过滤器组件在卸载时自动重置其内部状态**
  - **容器层如有业务上下文切换，也必须调用 `useRecordChangeReset`**

---

如有更复杂需求（例如需要在重置前做清理、或依赖多重 key），可在此 Hook 外部再做封装，保持核心逻辑简单可复用。
