import { useQuery } from "@tanstack/react-query";
import {
  getJobSliceAppointment,
  getLocationServiceProvider,
  getSpecialWorkConfig,
} from "api";
import { useMemo } from "react";

export const useJobTicketAppointmentOptions = (id: number | null) => {
  const { data } = useQuery({
    queryKey: ["getJobTicketAppointment", id],
    queryFn: () => getJobSliceAppointment(id),
    enabled: Boolean(id),
  });

  const appointmentListOptions = useMemo(() => {
    return data?.data ?? {};
  }, [data]);

  return appointmentListOptions;
};

export const useLocationService = () => {
  const { data: locationServiceRes } = useQuery({
    queryKey: ["getLocationService"],
    queryFn: () => getLocationServiceProvider(),
  });
  const locationService = useMemo(() => {
    return locationServiceRes?.data ?? {};
  }, [locationServiceRes]);

  return locationService;
};

export const useSpecialWorkConfig = () => {
  const { data: specialWorkConfigRes } = useQuery({
    queryKey: ["getSpecialWorkConfig"],
    queryFn: () => getSpecialWorkConfig(),
  });
  const specialWorkConfig = useMemo(() => {
    return specialWorkConfigRes?.data ?? {};
  }, [specialWorkConfigRes]);

  return specialWorkConfig;
};
