import { useAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { omit } from "ramda";
import { FC, useCallback } from "react";
import { formatRFC3339 } from "utils";

type useFilterSearchProps = {
  atomModal: any;
  excludeKeysWhenReset?: any[];
  otherAtomNeedReset?: any;
};

export const useFilterSearch: FC<useFilterSearchProps> = (
  atomModal,
  excludeKeysWhenReset,
  otherAtomNeedReset
) => {
  const [atom, setAtom] = useAtom(atomModal);
  const resetOtherAtom = useResetAtom(otherAtomNeedReset);
  const handleSearch = useCallback(
    (values) => {
      const timeMaps = [
        "operationTimeLt",
        "operationTimeGte",
        "workTimeEndGt",
        "workTimeEndLt",
        "reportTimeGte",
        "reportTimeLte",
        "bookDateGte",
        "bookDateLte",
        "applyTimeGte",
        "applyTimeLt",
        "beginTimeGte",
        "beginTimeLt",
      ];
      const rangeTimeMaps = [
        "operationTime",
        "loginTime",
        "commitmentTime",
        "applyTime",
        "beginTime",
        "checkTime",
        "reportTime",
        "workEndTime",
        "taskBeginTime",
        "processBeginTime",
        "evaluationTime",
        "alarmTime",
      ];
      const query = values?.query ?? "";
      let other = {
        ...(atom?.filter || {}),
        ...omit(["query"], values),
      };
      for (let k in other) {
        if (timeMaps.includes(k)) {
          /* if (k === 'loginTime') {
          const [start, end] = other[k].map(formatRFC3339);
          other.loginTimeGte = start;
          other.loginTimeLt = end;
          delete other[k];
        } else { */
          other[k] = formatRFC3339(other[k]);
          // }
        } else if (rangeTimeMaps.includes(k)) {
          const [start, end] = other[k].map(formatRFC3339);
          other[`${k}Gte`] = start;
          other[`${k}Gt`] = start;
          other[`${k}Lte`] = end;
          other[`${k}Lt`] = end;
          delete other[k];
        } else if (atom.hasOwnProperty(k)) {
          atom[k] = other[k];
          delete other[k]; // 删除已经在atom中的key
        }
      }
      setAtom({
        ...atom,
        pageNumber: 1,
        filter: other,
        query: query,
      });
    },
    [atom, setAtom]
  );

  const handleReset = useCallback(() => {
    const filter = {};
    if (excludeKeysWhenReset?.length) {
      excludeKeysWhenReset.forEach((key: string) => {
        filter[key] = atom.filter[key];
      });
    }
    setAtom({
      // ...atom,
      // fix: 如果有其他类似于pageSize, pageNumber的atom需要reset，可以在这里添加；防备的是departmentId & subjectId这样的属性
      pageSize: atom.pageSize,
      pageNumber: atom.pageNumber,
      filter,
      query: "",
    });
    if (otherAtomNeedReset) resetOtherAtom();
  }, [atom, setAtom, excludeKeysWhenReset]);

  return [handleSearch, handleReset];
};
