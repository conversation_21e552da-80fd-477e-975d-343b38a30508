# useFilterSearch Hook 使用文档

## 概述

`useFilterSearch` 是一个专门用于管理列表页面搜索和过滤功能的自定义 Hook。它与 Jotai 状态管理库深度集成，提供了统一的搜索、重置、时间格式化和分页管理功能。

## 功能特性

- ✅ **统一的搜索和重置逻辑**：标准化的表单搜索和重置处理
- ✅ **自动时间格式化**：支持单个时间字段和时间范围字段的自动格式化
- ✅ **智能分页重置**：搜索时自动重置到第一页
- ✅ **灵活的重置控制**：支持部分字段保留不重置
- ✅ **多 Atom 关联重置**：支持重置其他相关 Atom
- ✅ **过滤参数智能处理**：自动区分 atom 属性和 filter 参数

## API 接口

### 参数类型定义

```typescript
type useFilterSearchProps = {
  atomModal: any; // 主要的过滤状态 Atom
  excludeKeysWhenReset?: any[]; // 重置时需要保留的字段
  otherAtomNeedReset?: any; // 需要一起重置的其他 Atom
};
```

### 返回值

```typescript
const [handleSearch, handleReset] = useFilterSearch(/* 参数 */);
```

- `handleSearch(values)`: 搜索处理函数，接受表单值作为参数
- `handleReset()`: 重置处理函数，清空所有过滤条件

## 使用方式

### 1. 基础用法

```typescript
import { useFilterSearch } from "hooks";
import { chemicalAtoms } from "atoms/basicInfo/chemical";

export const ChemicalFilter = () => {
  const atoms = chemicalAtoms;
  const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

  return (
    <Form onSubmit={handleSearch}>
      <Form.Input field="query" placeholder="关键字搜索" />
      <Form.Select field="type" placeholder="选择类型">
        {/* 选项 */}
      </Form.Select>

      <Button htmlType="submit">查询</Button>
      <Button onClick={handleReset}>重置</Button>
    </Form>
  );
};
```

### 2. 带有重置控制的用法

```typescript
import { useFilterSearch } from "hooks";
import { doubleGuardCcItemAtoms } from "atoms/doubleGuard/ccItem";
import { useResetAtom } from "jotai/utils";

export const DoubleGuardCcItemFilter = () => {
  const atoms = doubleGuardCcItemAtoms;
  const resetfilterAtom = useResetAtom(atoms.filter);

  // 重置时保留 departmentId 字段
  const [handleSearch, handleReset] = useFilterSearch(
    atoms.filter,
    ["departmentId"]  // 这些字段在重置时不会被清空
  );

  // 组件卸载时重置过滤器
  useEffect(() => {
    return () => {
      resetfilterAtom();
    };
  }, []);

  return (
    <Form onSubmit={handleSearch}>
      {/* 表单内容 */}
    </Form>
  );
};
```

### 3. 关联多个 Atom 的用法

```typescript
import { useFilterSearch } from "hooks";
import { equipmentManagementDetectionAtoms } from "atoms/equipmentManagement/detection";
import { otherRelatedAtom } from "atoms/other";

export const EquipmentManagementDetectionFilter = () => {
  const atoms = equipmentManagementDetectionAtoms;

  // 重置时同时重置关联的 Atom
  const [handleSearch, handleReset] = useFilterSearch(
    atoms.filter,
    [],                    // 无需保留字段
    otherRelatedAtom       // 重置时一起重置这个 Atom
  );

  return (
    <Form onSubmit={handleSearch}>
      {/* 表单内容 */}
    </Form>
  );
};
```

## 核心机制说明

### 1. 时间字段处理

Hook 内部定义了两种时间字段类型：

#### 单个时间字段（timeMaps）

这些字段会直接使用 `formatRFC3339` 格式化：

```typescript
const timeMaps = [
  "operationTimeLt",
  "operationTimeGte",
  "workTimeEndGt",
  "workTimeEndLt",
  "reportTimeGte",
  "reportTimeLte",
  "bookDateGte",
  "bookDateLte",
  "applyTimeGte",
  "applyTimeLt",
  "beginTimeGte",
  "beginTimeLt",
];
```

#### 时间范围字段（rangeTimeMaps）

这些字段会被拆分为 `xxxGte`、`xxxGt`、`xxxLte`、`xxxLt` 四个字段：

```typescript
const rangeTimeMaps = [
  "operationTime",
  "loginTime",
  "commitmentTime",
  "applyTime",
  "beginTime",
  "checkTime",
  "reportTime",
  "workEndTime",
  "taskBeginTime",
  "processBeginTime",
  "evaluationTime",
  "alarmTime",
];
```

**示例**：

```typescript
// 输入
const formValues = {
  reportTime: [startDate, endDate],
};

// Hook 自动转换为
const filterParams = {
  reportTimeGte: "2024-01-01T00:00:00Z",
  reportTimeGt: "2024-01-01T00:00:00Z",
  reportTimeLte: "2024-01-31T23:59:59Z",
  reportTimeLt: "2024-01-31T23:59:59Z",
};
```

### 2. 参数智能分类

Hook 会自动区分哪些参数属于 atom 属性，哪些属于 filter 对象：

```typescript
// atom 结构示例
const atom = {
  pageSize: 20,
  pageNumber: 1,
  filter: {
    type: "",
    status: "",
    // ... 其他过滤字段
  },
  query: "",
};

// 表单提交的 values
const formValues = {
  query: "搜索关键字",
  type: "chemical",
  status: "active",
  pageSize: 50, // 这个会被归类到 atom 属性
};

// Hook 处理后
setAtom({
  ...atom,
  pageNumber: 1, // 自动重置到第一页
  pageSize: 50, // atom 属性
  query: "搜索关键字", // atom 属性
  filter: {
    // filter 对象
    ...existingFilter,
    type: "chemical",
    status: "active",
  },
});
```

### 3. 重置机制

重置功能支持灵活的字段保留：

```typescript
const handleReset = () => {
  const filter = {};

  // 保留指定字段
  if (excludeKeysWhenReset?.length) {
    excludeKeysWhenReset.forEach((key: string) => {
      filter[key] = atom.filter[key];
    });
  }

  // 重置状态
  setAtom({
    pageSize: atom.pageSize, // 保留分页大小
    pageNumber: atom.pageNumber, // 保留当前页码
    filter, // 只保留指定字段
    query: "", // 清空搜索关键字
  });

  // 重置关联 Atom
  if (otherAtomNeedReset) resetOtherAtom();
};
```

## 最佳实践

### 1. Atom 结构规范

确保你的过滤 Atom 遵循标准结构：

```typescript
// atoms/yourModule/yourEntity.tsx
export const yourEntityFilterAtom = atom({
  pageSize: 20,
  pageNumber: 1,
  filter: {
    // 所有业务过滤字段
    type: "",
    status: "",
    dateRange: null,
    // ...
  },
  query: "",
});
```

### 2. 表单字段命名

- **查询关键字**：使用 `query` 字段
- **时间范围**：使用 rangeTimeMaps 中定义的字段名
- **其他过滤**：使用描述性的字段名

### 3. 组件卸载清理

对于临时页面或弹窗，建议在组件卸载时清理过滤状态：

```typescript
useEffect(() => {
  return () => {
    resetfilterAtom();
  };
}, []);
```

### 4. 错误处理

```typescript
const [handleSearch, handleReset] = useFilterSearch(atoms.filter);

const onSubmit = async (values) => {
  try {
    handleSearch(values);
  } catch (error) {
    console.error("搜索失败:", error);
    // 显示错误提示
  }
};
```

## 常见问题

### Q1: 为什么搜索后页码没有重置？

**A**: 检查 atom 结构是否包含 `pageNumber` 字段，Hook 会自动将其设为 1。

### Q2: 时间字段格式化不正确怎么办？

**A**: 确保时间字段名在 `timeMaps` 或 `rangeTimeMaps` 中，或者使用标准的时间字段命名。

### Q3: 重置时某些字段没有清空？

**A**: 检查是否将这些字段添加到了 `excludeKeysWhenReset` 参数中。

### Q4: Hook 必须在组件内部调用吗？

**A**: 是的，所有 React hooks 都必须在组件函数体内调用，不能在组件外部或条件语句中调用。

## 参考示例

完整的过滤组件实现可以参考：

- `src/pages/basicInfo/content/chemical/filter.tsx`
- `src/pages/doubleGuard/content/ccItem/filter.tsx`
- `src/pages/equipmentManagement/content/detection/filter.tsx`

## 相关文档

- [Jotai 状态管理文档](https://jotai.org/)
- [Semi UI Form 组件文档](https://semi.design/zh-CN/input/form)
- [formatRFC3339 时间格式化工具](../utils/time.md)
