import { PrimitiveAtom } from "jotai";
import { useResetAtom } from "jotai/utils";
import { useEffect, useRef } from "react";

/**
 * useRecordChangeReset
 * ---------------------
 * 当业务上下文（如详情记录 ID、路由参数等）发生变化时，自动重置传入的 Jotai atom。
 * 可用于过滤器等需要在“切换记录 / 切换 Tab”时清空状态的场景。
 *
 * @param atom       需要重置的 jotai PrimitiveAtom
 * @param contextKey 能唯一标识业务上下文的键，通常是记录 ID、路由路径等
 */
export const useRecordChangeReset = <T>(
  atom: PrimitiveAtom<T>,
  contextKey: string | number | undefined
) => {
  const reset = useResetAtom(atom);
  const prevKeyRef = useRef(contextKey);

  useEffect(() => {
    if (prevKeyRef.current !== contextKey) {
      reset();
      prevKeyRef.current = contextKey;
    }
  }, [contextKey, reset]);
};
