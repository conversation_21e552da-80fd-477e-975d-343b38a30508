import { useQuery } from "@tanstack/react-query";
import { getHiddenSettings } from "api";
import { useMemo } from "react";

export const useHiddenSettings = () => {
  const { data } = useQuery({
    queryKey: ["getHiddenSettings"],
    queryFn: () => {
      return getHiddenSettings();
    },
  });

  const hiddenSettings = useMemo(() => {
    return data?.data || {};
  }, [data]);

  return hiddenSettings;
};
