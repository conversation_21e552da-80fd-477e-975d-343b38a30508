import { IconDownload } from "@douyinfe/semi-icons";
import { Button, Dropdown, Toast } from "@douyinfe/semi-ui";
import { Column, ExportFormat, useExport } from "components/export";
import { FC } from "react";

// 导出配置类型
export interface ExportConfig {
  filename?: string;
  columns?: string[];
  formats?: ExportFormat[];
  entityName?: string;
}

interface ExportToolbarProps {
  data: any[];
  columns: any[];
  exportConfig?: ExportConfig;
  entityTitle?: string;
  apiFunction?: (params: any) => Promise<any>;
  apiParams?: any;
}

export const ExportToolbar: FC<ExportToolbarProps> = ({
  data,
  columns,
  exportConfig,
  entityTitle = "数据",
  apiFunction,
  apiParams = {},
}) => {
  const exportContext = useExport();

  // 处理数据导出
  const handleExport = async (format: ExportFormat) => {
    if (!data || data.length === 0) {
      Toast.warning("暂无数据可导出");
      return;
    }

    if (!exportContext) {
      Toast.error("导出功能未初始化");
      return;
    }

    console.debug("columns", columns);

    try {
      // 确定要导出的列
      const exportColumns = exportConfig?.columns
        ? columns.filter((col) =>
            exportConfig.columns!.includes(col.name || col.dataIndex)
          )
        : columns.filter((col) => col.name || col.dataIndex);

      // 转换为导出系统需要的列格式
      const formattedColumns: Column[] = exportColumns.map((col) => {
        return {
          header: col.label || col.title,
          field: col.name || col.dataIndex,
          dataIndex: col.name || col.dataIndex,
          renderText: (value: any) => {
            // 处理特殊类型的数据
            if (col.type === "entity" && value) {
              return value[col.key] || value.name || value;
            } else if (
              col.type === "enum" &&
              col.enumMap &&
              value !== null &&
              value !== undefined
            ) {
              const enumItem = col.enumMap.find(
                (item: any) => item.id === value
              );
              return enumItem?.name || value;
            } else if (col.type === "dic" && value) {
              return value.dicValue || value;
            } else if (col.type === "array" && Array.isArray(value)) {
              return value
                .map((item) => item.name || item.label || item)
                .join(", ");
            } else if (col.type === "date" || col.type === "datetime") {
              return value ? new Date(value).toLocaleString() : "";
            }
            return value || "";
          },
        };
      });

      // 创建导出用的API函数
      const exportApiFunction = apiFunction
        ? apiFunction
        : async () => Promise.resolve(data);

      // 使用导出系统
      await exportContext.exportToFile({
        format,
        contentType: "list",
        apiFn: exportApiFunction,
        params: apiParams,
        columns: formattedColumns,
        entityName: exportConfig?.entityName || entityTitle,
        fileName: exportConfig?.filename,
      });

      Toast.success(`${format.toUpperCase()}文件导出成功`);
    } catch (error) {
      console.error("导出失败:", error);
      Toast.error("导出失败，请重试");
    }
  };

  // 获取支持的导出格式
  const supportedFormats = exportConfig?.formats || ["excel", "csv"];

  // 如果导出功能未初始化，不显示导出按钮
  if (!exportContext) {
    return null;
  }

  // 如果只有一种格式，直接显示按钮
  if (supportedFormats.length === 1) {
    const format = supportedFormats[0];
    return (
      <Button
        icon={<IconDownload />}
        onClick={() => handleExport(format)}
        type="tertiary"
        size="small"
      >
        导出{format.toUpperCase()}
      </Button>
    );
  }

  // 多种格式，显示下拉菜单
  const dropdownItems = supportedFormats.map((format) => ({
    node: "item",
    name: `导出${format.toUpperCase()}`,
    onClick: () => handleExport(format),
  }));

  return (
    <Dropdown trigger="click" position="bottomLeft" menu={dropdownItems}>
      <Button icon={<IconDownload />} type="tertiary" size="small">
        导出数据
      </Button>
    </Dropdown>
  );
};
