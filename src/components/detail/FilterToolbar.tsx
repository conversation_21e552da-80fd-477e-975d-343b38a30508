import { Form } from "@douyinfe/semi-ui";
import {
  AreaSearch,
  DepartmentSearch,
  DepartmentUnitSearch,
  EmployeeSearch,
  PositionSearch,
  RoleSearch,
} from "components/search";
import { RestSelect } from "components/select";
import { useFilterSearch } from "hooks";
import { FC, useEffect, useRef, useState } from "react";

// 过滤列配置类型
export interface FilterColumn {
  field: string;
  label: string;
  component: string;
  props?: Record<string, any>;
  apiFieldMap?: {
    start?: string;
    end?: string;
  };
}

interface FilterToolbarProps {
  filterColumns: FilterColumn[];
  filterAtom: any;
}

// 组件映射表
const COMPONENT_MAP = {
  DepartmentSearch,
  DepartmentUnitSearch,
  RestSelect,
  EmployeeSearch,
  AreaSearch,
  PositionSearch,
  RoleSearch,
  Input: Form.Input,
  DatePicker: Form.DatePicker,
  Select: Form.Select,
};

export const FilterToolbar: FC<FilterToolbarProps> = ({
  filterColumns,
  filterAtom,
}) => {
  // 折叠状态：true = 收起，仅显示标题栏；false = 展开显示筛选表单
  const [isCollapsed, setIsCollapsed] = useState(false);

  const [handleSearch, handleReset] = useFilterSearch(filterAtom);
  const formRef = useRef<any>();

  // 切换折叠/展开
  const toggleCollapse = () => setIsCollapsed(!isCollapsed);

  // 当 FilterToolbar 卸载时自动重置过滤状态，避免残留筛选条件影响其他记录
  useEffect(() => {
    return () => {
      handleReset();
    };
  }, []);

  // 处理查询按钮点击
  const handleSubmit = () => {
    const formValues = formRef.current?.formApi?.getValues() || {};
    console.log("Filter form values:", formValues);
    handleSearch(formValues);
  };

  // 处理重置按钮点击
  const handleResetClick = () => {
    formRef.current?.formApi?.reset();
    handleReset();
  };

  const renderFilterField = (column: FilterColumn) => {
    const componentName = column.component;

    // 处理DatePicker组件
    if (componentName === "DatePicker") {
      const datePickerType = column.props?.type || "date";
      return (
        <Form.DatePicker
          key={column.field}
          field={column.field}
          type={datePickerType}
          placeholder={column.props?.placeholder || `请选择${column.label}`}
          showClear
          {...column.props}
        />
      );
    }

    // 处理Input组件
    if (componentName === "Input") {
      return (
        <Form.Input
          key={column.field}
          field={column.field}
          placeholder={column.props?.placeholder || `请输入${column.label}`}
          suffix={column.props?.suffix}
          showClear
          {...column.props}
        />
      );
    }

    // 处理Select组件 - 支持enum map到select
    if (componentName === "Select") {
      return (
        <Form.Select
          key={column.field}
          field={column.field}
          placeholder={column.props?.placeholder || `请选择${column.label}`}
          noLabel
          className="w-full"
          showClear
          {...column.props}
        >
          {column.props?.optionList?.map((item: any) => (
            <Form.Select.Option key={item.value} value={item.value}>
              {item.label}
            </Form.Select.Option>
          ))}
        </Form.Select>
      );
    }

    // 处理其他搜索组件
    const Component =
      COMPONENT_MAP[componentName as keyof typeof COMPONENT_MAP];
    if (!Component) {
      console.warn(`Unknown filter component: ${componentName}`);
      return null;
    }

    return (
      <Component
        key={column.field}
        field={column.field}
        label=""
        noLabel={true}
        placeholder={column.props?.placeholder || `请选择${column.label}`}
        {...column.props}
      />
    );
  };

  return (
    <div className="flex flex-col bg-white shadow rounded relative mb-4">
      {/* 标题栏 */}
      <div
        className="text-gray-900 text-opacity-90 text-base font-semibold leading-snug px-4 py-3 border-b border-gray-900/10 flex items-center cursor-pointer select-none"
        onClick={toggleCollapse}
      >
        <span className="flex-1">筛选条件</span>
        {/* 折叠图标 */}
        <i
          className={`ri-arrow-${isCollapsed ? "down" : "up"}-s-line text-lg`}
        />
      </div>

      {/* 表单内容：折叠时隐藏但不卸载，保留表单状态 */}
      <div className={isCollapsed ? "hidden" : "p-4 pr-0"}>
        <Form
          ref={formRef}
          layout="horizontal"
          className="grid grid-cols-4 gap-y-4 gap-x-4"
        >
          {filterColumns.map(renderFilterField)}

          <div className="flex gap-2">
            <button
              className="btn rounded btn-primary btn-sm"
              type="button"
              onClick={handleSubmit}
            >
              查&nbsp;&nbsp;询
            </button>
            <button
              className="btn btn-sm rounded"
              type="button"
              onClick={handleResetClick}
            >
              重&nbsp;&nbsp;置
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
};
