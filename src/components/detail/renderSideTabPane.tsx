import {
  Button,
  Empty,
  Spin,
  Table,
  Tag,
  Tooltip,
  Typography,
} from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import {
  SAFETY_ANALYSIS_JOBSTEP,
  VideoModal,
  renderFiles,
  renderImgs,
} from "components";
import dayjs from "dayjs";
import { useRecordChangeReset } from "hooks/useRecordChangeReset";
import { atom, useAtom } from "jotai";
import { find, isEmpty, propEq, type } from "ramda";
import { FC, useMemo } from "react";
import {
  formatDate,
  formatDateDay,
  formatNumToRate,
  millisecondsOfOnemonth,
} from "utils";
import { ExportConfig, ExportToolbar } from "./ExportToolbar";
import { FilterColumn, FilterToolbar } from "./FilterToolbar";

// 默认的过滤状态原子
const defaultFilterAtom = atom({ filter: {}, query: "" });

type schemeGroup = {
  groupName: string;
  list?: any;
  table?: any;
};

type RenderSideProps = {
  entity: string;
  entityTitle: string;
  scheme: Array<schemeGroup> | object;
  dataApi: any;
  params?: any;
  infoOrList: number;
  detailAtom: any;
  gridCol?: number;
  // 新增属性
  enableFilter?: boolean;
  filterAtom?: any;
  filterColumns?: FilterColumn[];
  enableExport?: boolean;
  exportConfig?: ExportConfig;
};

const EnumMap = {
  jobStep: SAFETY_ANALYSIS_JOBSTEP,
};

export const RenderSideTabPane: FC<RenderSideProps> = ({
  entity,
  entityTitle,
  scheme,
  dataApi,
  params,
  infoOrList,
  detailAtom,
  gridCol = 6,
  enableFilter,
  filterAtom,
  filterColumns,
  enableExport,
  exportConfig,
}) => {
  // console.log('scheme', scheme)
  // console.log('entityTitle', entityTitle)
  // console.log('detailAtom', detailAtom)

  // const isGroup = type(scheme) === 'Array'

  const [infoAtom, setInfoAtom] = useAtom(detailAtom);

  const effectiveFilterAtom = filterAtom || defaultFilterAtom;
  const [filterState] = useAtom(effectiveFilterAtom);

  // 使用封装的 Hook，在记录（infoAtom.id）变化时重置过滤状态
  useRecordChangeReset(effectiveFilterAtom, (infoAtom as any)?.id);

  // console.log('infoAtom', infoAtom)

  // 处理过滤参数的函数
  const processFilterParams = (
    filterState: any,
    filterColumns?: FilterColumn[]
  ) => {
    console.log("processFilterParams input:", { filterState, filterColumns });

    if (!filterColumns || (!filterState.filter && !filterState.query)) {
      console.log(
        "Early return from processFilterParams - no columns or filter"
      );
      return {};
    }

    const result: any = {};

    // 处理过滤参数 - 确保包装在filter对象中
    if (filterState.filter && Object.keys(filterState.filter).length > 0) {
      result.filter = { ...filterState.filter };

      // 处理特殊API字段映射（如日期范围）
      filterColumns.forEach((column) => {
        if (column.apiFieldMap && result.filter[column.field]) {
          const value = result.filter[column.field];
          if (Array.isArray(value) && value.length === 2) {
            result.filter[column.apiFieldMap.start!] = value[0];
            result.filter[column.apiFieldMap.end!] = value[1];
            delete result.filter[column.field];
          }
        }
      });
    }

    // 处理查询字段
    if (filterState.query) {
      result.query = filterState.query;
    }

    console.log("processFilterParams output:", result);
    return result;
  };

  const { data: dataApiRes, isLoading } = useQuery({
    queryKey: [`get${entity}`, (infoAtom as any)?.id, filterState],
    queryFn: () => {
      if (infoOrList === 1) {
        return dataApi((infoAtom as any)?.id);
      } else if (infoOrList === 2) {
        // 合并过滤参数到API调用中
        const filterParams = enableFilter
          ? processFilterParams(filterState, filterColumns)
          : {};

        // 根据params结构智能合并过滤参数
        let apiParams;
        if (params?.values) {
          // 如果params有values字段，则在values中合并过滤参数
          apiParams = {
            ...params,
            values: {
              ...params.values,
              ...filterParams,
            },
          };
        } else {
          // 否则直接合并到根级别
          apiParams = {
            ...params,
            ...filterParams,
          };
        }

        console.log("Final API params:", apiParams);
        return dataApi(apiParams);
      } else {
        console.error("-----------------infoOrList error-----------------");
      }
    },
    enabled: !!(infoAtom as any)?.id,
  });
  // console.log('dataApiRes', dataApiRes)

  const infoData = useMemo(() => {
    if (infoOrList === 1) {
      return dataApiRes?.data ?? {};
    } else if (infoOrList === 2) {
      const dataSource = (scheme as any)[0]?.table?.dataSource;

      const dataRes =
        dataSource && !isEmpty(dataSource)
          ? dataApiRes?.data?.[dataSource]
          : dataApiRes?.data;

      return dataRes ?? [];
    }
  }, [dataApiRes]);

  // console.log('infoData', infoData)

  /* return (
    <div className="flex gap-2" >
      {
        uri.map((o: string, i: number) => (
          // <img src={o} alt="" key={i} className="size-10 rounded-md" />
          <img src={o} alt="" key={i} className="max-w-96" />
        ))
      }
    </div>
  ) */

  const renderAtom = (item: any, index: number, record: any) => {
    // console.log('item', item, index)
    const label = item.label;
    let value = infoData?.[item.name];
    // console.log('label, value', label, value, type(value))
    /* if (type(value) === 'Object') {
      value = infoData[item.name]?.name || '-'
    }
    console.log('label, value', label, value, type(value)) */

    if (type(value) === "Array" && entity === "jobProcesses") {
      return (
        <>
          {value?.map((v: any, k: number) => (
            <>
              <div className="bg-zinc-100 p-4 text-end border-r border-b">
                {v?.name || "-"}:
              </div>
              <div className="col-span-2 p-4 border-r border-b">
                {v?.candidatePerson?.map((c: any, ckey: number) => (
                  <Tag key={ckey} size="large" style={{ marginRight: "1px" }}>
                    {c.name}
                  </Tag>
                ))}
              </div>
            </>
          ))}
        </>
      );
    }
    if (type(value) === "Array" || item?.type === "array") {
      return (
        <>
          {item?.padding ? (
            <>
              <div className="bg-zinc-100 p-4 text-end border-r border-b"></div>
              <div className="col-span-2 p-4 border-r border-b"></div>
            </>
          ) : null}
          <div className="bg-zinc-100 p-4 text-end border-r border-b">
            {label}:
          </div>
          <div className="col-span-5 p-4 border-r border-b">
            {item?.type === "image"
              ? // <img src={value} alt={label} className="w-32 h-32" />
                renderImgs(value)
              : item?.type === "file"
                ? renderFiles(value)
                : value?.map((c: any, ckey: number) => (
                    <Tag key={ckey} size="large" style={{ marginRight: "1px" }}>
                      {c?.name ?? c?.label ?? c?.content ?? "-"}
                    </Tag>
                  ))}
          </div>
        </>
      );
    }

    return (
      <>
        {item?.padding ? (
          <>
            <div className="bg-zinc-100 p-4 text-end border-r border-b"></div>
            <div className="col-span-2 p-4 border-r border-b"></div>
          </>
        ) : null}
        <div className="bg-zinc-100 p-4 text-end border-r border-b">
          {label}:
        </div>
        {item?.type === "image" ||
        item?.type === "file" ||
        item?.type === "video" ||
        item?.type === "text" ? (
          <div className="col-span-5 p-4 border-r border-b">
            {item?.type === "image" ? (
              <img src={value} alt={label} className="w-32 h-32" />
            ) : item?.type === "file" ? (
              <a href={value} target="_blank" rel="noreferrer">
                {" "}
                {label}{" "}
              </a>
            ) : item?.type === "video" ? (
              <VideoModal data={record} />
            ) : (
              value
            )}
          </div>
        ) : (
          <div className="col-span-2 p-4 border-r border-b">
            {item?.type === "dic"
              ? value?.dicValue
              : item?.type === "date"
                ? formatDateDay(value)
                : item?.type === "datetime"
                  ? formatDate(value)
                  : item?.type === "enum"
                    ? (find(propEq(value, "id"))(item.enumMap) as any)?.name
                    : item?.type === "entity"
                      ? item?.key
                        ? value?.[item.key]
                        : value?.name
                      : value}
          </div>
        )}
      </>
    );
  };

  const renderItem = (list: any[]) => {
    if (!list) {
      return null;
    }
    return (
      <>
        {list.map((item: any, index: number, record: any) =>
          renderAtom(item, index, record)
        )}
      </>
    );
  };

  // 生成表格表头
  const generateColumns = (columns: any[]) => {
    if (!columns) {
      return [];
    }
    const tmp: any[] = [];
    const renderCol = (text: any, record: any, col: any) => {
      let value = text;
      if (col.type === "entity") {
        value = text?.name ?? "-";
      } else if (col.type === "dic") {
        value = text?.dicValue ?? "-";
      } else if (col.type === "date") {
        value = formatDateDay(text);
      } else if (col.type === "datetime") {
        value = formatDate(text);
      } else if (col.type === "imagelist") {
        if (col.render === "old") {
          value = JSON.parse(text ?? "[]");
        }
      } else if (col.type === "image") {
      } else if (col.type === "filelist") {
      } else if (col.type === "file") {
      } else if (col.type === "text") {
      } else if (col.type === "enum") {
        // value = col.enumMap(col.name)
        const i = find(propEq(text, "id"))(col.enumMap);
        // value = i?.name;
        value = (i as any)?.color ? (
          <Tag color={(i as any)?.color}>{(i as any)?.name}</Tag>
        ) : (
          (i as any)?.name
        );
      } else if (col.type === "array") {
        if (col.render === "entity") {
          console.debug(record?.[col.name]);
          value = record?.[col.name]?.map((c: any) => c.name).join("、");
        }
      }

      return (
        <>
          {col.type === "button" ? (
            col.buttons.map((button: any, index: number) => {
              if (!button(record)) return null;
              return (
                <Button onClick={() => button(record)?.func(record)}>
                  {button(record)?.chnName}
                </Button>
              );
            })
          ) : col.type === "image" || col.type === "imagelist" ? (
            renderImgs(type(value) === "Array" ? value : [value])
          ) : col.type === "file" || col.type === "filelist" ? (
            renderFiles(type(value) === "Array" ? value : [value])
          ) : col.type === "video" ? (
            <VideoModal data={record} />
          ) : col.render == "rate" ? (
            formatNumToRate(value)
          ) : col.render === "expire" ? (
            <p>
              {dayjs(value).diff(dayjs()) < millisecondsOfOnemonth ? (
                <Tag color="red">{formatDateDay(value)}</Tag>
              ) : dayjs(value).diff(dayjs()) < 3 * millisecondsOfOnemonth ? (
                <Tag color="yellow">{formatDateDay(value)}</Tag>
              ) : (
                <Tag color="white">{formatDateDay(value)}</Tag>
              )}
            </p>
          ) : (
            <Tooltip content={value}>
              <p>{value}</p>
            </Tooltip>
          )}
        </>
      );
    };
    columns.forEach((col: any) => {
      tmp.push({
        title: col.label,
        dataIndex: col.name,
        render: (text: any, record: any, index: number) =>
          renderCol(text, record, col),
      });
    });
    return tmp;
  };

  const renderTable = (item: any) => {
    if (!item) return null;

    const columns = generateColumns(item?.columns || []);

    return (
      <>
        <Table
          className="rounded overflow-hidden"
          rowKey="id"
          columns={columns}
          dataSource={infoData}
          onHeaderRow={(columns: any, index: number) => {
            return {
              className: "text-gray-900 text-opacity-90 bg-gray-50",
            };
          }}
          headerStyle={{ color: "blue" }}
          pagination={false}
          size="small"
          // scroll={{ x: 1000 }}
          empty={<Empty description="暂无数据" />}
        />
      </>
    );
  };

  return (
    <div className="w-full overflow-y-auto flex flex-col gap-y-4 relative">
      {isLoading && (
        <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/60">
          <Spin size="large" />
        </div>
      )}
      {(scheme as any)?.map?.((o: any, i: number) => (
        <div className="w-full pt-4" key={i}>
          {o.groupName ? (
            <Typography.Title heading={5}>{o.groupName}</Typography.Title>
          ) : null}
          <div
            className={`grid grid-cols-${gridCol} rounded-lg overflow-hidden border border-b-0`}
            style={{
              gridTemplateColumns: `repeat(${gridCol}, minmax(0, 1fr))`,
            }}
          >
            {renderItem(o?.list)}
          </div>
          <div className={`mt-4`}>
            {/* 工具栏区域 - 包含过滤和导出功能 */}
            {(enableFilter || enableExport) && infoOrList === 2 && (
              <div className="flex flex-col gap-4 mb-4">
                {/* 过滤工具栏 */}
                {enableFilter && filterColumns && (
                  <FilterToolbar
                    filterColumns={filterColumns}
                    filterAtom={filterAtom || defaultFilterAtom}
                  />
                )}

                {/* 导出工具栏 */}
                {enableExport && exportConfig && o?.table && (
                  <div className="flex justify-end">
                    <ExportToolbar
                      data={infoData || []}
                      columns={o?.table?.columns || []}
                      exportConfig={exportConfig}
                      entityTitle={entityTitle}
                      apiFunction={dataApi}
                      apiParams={
                        params?.values
                          ? {
                              ...params,
                              values: {
                                ...params.values,
                                ...processFilterParams(
                                  filterState,
                                  filterColumns
                                ),
                              },
                            }
                          : {
                              ...params,
                              ...processFilterParams(
                                filterState,
                                filterColumns
                              ),
                            }
                      }
                    />
                  </div>
                )}
              </div>
            )}

            {renderTable(o?.table)}
          </div>
        </div>
      ))}
    </div>
  );
};
