// src/components/export/ExportProvider.tsx
import { Toast } from "@douyinfe/semi-ui";
import React, { createContext, useState } from "react";
import { listPageSizeWithoutPaging } from "utils";
import { exportFunctions } from "./defaultExportFunction";
import { ExportContextType, ExportOptions, ExportProviderProps } from "./types";

// 创建导出上下文
export const ExportContext = createContext<ExportContextType | undefined>(
  undefined
);

export const ExportProvider: React.FC<ExportProviderProps> = ({ children }) => {
  const [exportError, setExportError] = useState<string | null>(null);

  const exportToFile = async (options: ExportOptions) => {
    // 设置默认值
    const {
      format = "excel", // 默认格式为 Excel
      contentType = "list", // 默认内容类型为 list
      apiFn,
      params,
      columns,
      entityName,
      fileName,
    } = options;
    console.debug("exportToFile options", options);

    // const exportFunction = exportFunctions[format];
    const exportFunction = exportFunctions[format]?.[contentType];

    if (!exportFunction) {
      const errorMsg = `不支持的导出格式: ${format}`;
      Toast.error(errorMsg);
      setExportError(errorMsg);
      console.error(errorMsg);
      return;
    }

    // 清空先前的错误状态
    setExportError(null);

    try {
      // 1. 下载数据
      let realParams = {
        ...params,
        pageSize: listPageSizeWithoutPaging, // TODO: 一次性下载所有数据
        pageNumber: params?.pageNumber || 1,
      };
      const apiRes = await apiFn(realParams);
      if (!apiRes || !apiRes?.data) {
        const errorMsg = "导出数据出错，请检查";
        Toast.error(errorMsg);
        setExportError(errorMsg);
        console.error(errorMsg);
        return;
      }

      let data = apiRes.data.results;
      if (contentType === "object") {
        console.log("data is object, not use results");
        // TODO: 如果数据是对象类型，直接使用 data
      }

      columns.forEach((column) => {
        if (!column.header) {
          column.header = column.title;
        }
        if (!column.field) {
          column.field = column.dataIndex;
        }
        if (column.renderText) {
          data = data.map((item) => {
            item[column.field] = column?.renderText(item[column.field], item);
            return item;
          });
        } else {
          data = data.map((item) => {
            item[column.field] = item[column.field];
            return item;
          });
        }
      });

      // 2. 调用导出函数，格式化并导出数据
      exportFunction(data, columns, entityName, fileName);
    } catch (error) {
      const errorMsg = "数据下载或导出失败，请重试";
      Toast.error(errorMsg);
      setExportError(errorMsg);
      console.error(errorMsg, error);
    }
  };

  return (
    <ExportContext.Provider value={{ exportToFile, exportError }}>
      {children}
    </ExportContext.Provider>
  );
};
