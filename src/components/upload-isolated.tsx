import { IconDelete, IconUpload } from "@douyinfe/semi-icons";
import {
  Button,
  Form,
  Image,
  Upload as SemiUpload,
  Toast,
  useFormApi,
} from "@douyinfe/semi-ui";
import { upload_url } from "config";
import { type } from "ramda";
import { FC, useEffect, useRef, useState } from "react";

type IsolatedUploadProps = {
  formField: string;
  field: string;
  label: string;
  multiple?: boolean;
  accept?: string;
  listType?: "list" | "picture";
  singleAsMultiple?: boolean;
  type?: "img" | "file";
  arrayProcessType?: "array" | "string";
  onSuccess?: (res: any) => void;
  disabled?: boolean;
  isRequired?: boolean;
  children?: React.ReactNode;
  maxSize?: number;
  helpTextStyle?: React.CSSProperties;
  showHelpText?: boolean;
};

export const IsolatedUpload: FC<IsolatedUploadProps> = (props) => {
  const formApi = useFormApi();
  const [fileList, setFileList] = useState<any[]>([]);
  const instanceId = useRef(`isolated_upload_${props.formField}_${Date.now()}`);

  console.log(
    `IsolatedUpload instance ${instanceId.current} initialized for field: ${props.formField}`
  );

  // 初始化文件列表
  useEffect(() => {
    const currentFieldValue = formApi.getValue(props.formField);
    let uris = [];

    if (type(currentFieldValue) == "String") {
      try {
        uris = JSON.parse(currentFieldValue) || [];
      } catch (error) {
        console.log(error);
        uris = [];
      }
    } else if (type(currentFieldValue) == "Array") {
      uris = currentFieldValue || [];
    }

    const initialFileList = uris.map((uri, index) => ({
      uid: `${instanceId.current}_${index}`,
      name: uri?.split("/")?.pop() || `file_${index}`,
      status: "success",
      url: uri?.startsWith("http")
        ? uri
        : `${import.meta.env.VITE_BASE || ""}${uri}`,
    }));

    setFileList(initialFileList);
    console.log(
      `IsolatedUpload instance ${instanceId.current} initialized file list:`,
      initialFileList
    );
  }, [props.formField]);

  const updateFormField = (uris: string[]) => {
    console.log(
      `IsolatedUpload instance ${instanceId.current} updating field ${props.formField} with uris:`,
      uris
    );

    if (!props.arrayProcessType || props.arrayProcessType === "string") {
      formApi.setValue(props.formField, JSON.stringify(uris));
    } else if (props.arrayProcessType === "array") {
      formApi.setValue(props.formField, uris);
    }
  };

  const handleSuccess = (responseBody: any, file: any) => {
    console.log(
      `IsolatedUpload instance ${instanceId.current} handleSuccess:`,
      responseBody,
      file
    );

    const newUris = responseBody?.data?.uris || [];
    if (newUris.length === 0) return;

    // 获取当前字段值
    const currentFieldValue = formApi.getValue(props.formField);
    let existingUris = [];

    if (type(currentFieldValue) == "String") {
      try {
        existingUris = JSON.parse(currentFieldValue) || [];
      } catch (error) {
        existingUris = [];
      }
    } else if (type(currentFieldValue) == "Array") {
      existingUris = currentFieldValue || [];
    }

    if (props?.multiple === false) {
      existingUris = [];
    }

    const updatedUris = existingUris.concat(newUris);

    // 更新表单字段
    updateFormField(updatedUris);

    // 更新文件列表显示
    const newFileItem = {
      uid: `${instanceId.current}_${Date.now()}_${Math.random()}`,
      name: file.name,
      status: "success",
      url: `${import.meta.env.VITE_BASE || ""}${newUris[0]}`,
    };

    setFileList((prev) => {
      const updated =
        props?.multiple === false ? [newFileItem] : [...prev, newFileItem];
      console.log(
        `IsolatedUpload instance ${instanceId.current} updated file list:`,
        updated
      );
      return updated;
    });

    if (typeof props.onSuccess === "function") {
      props.onSuccess({
        ...file,
        uri: newUris[0],
        uris: updatedUris,
      });
    }
  };

  const handleRemove = (fileItem: any) => {
    console.log(
      `IsolatedUpload instance ${instanceId.current} handleRemove:`,
      fileItem
    );

    // 从文件列表中移除
    setFileList((prev) => {
      const updated = prev.filter((item) => item.uid !== fileItem.uid);
      console.log(
        `IsolatedUpload instance ${instanceId.current} updated file list after remove:`,
        updated
      );
      return updated;
    });

    // 获取要删除的URI
    let uriToRemove = "";
    try {
      const url = new URL(fileItem.url);
      uriToRemove = url.pathname;
    } catch (error) {
      uriToRemove = fileItem.url;
    }

    // 从表单字段中移除
    const currentFieldValue = formApi.getValue(props.formField);
    let currentUris = [];

    if (type(currentFieldValue) == "String") {
      try {
        currentUris = JSON.parse(currentFieldValue) || [];
      } catch (error) {
        currentUris = [];
      }
    } else if (type(currentFieldValue) == "Array") {
      currentUris = currentFieldValue || [];
    }

    const updatedUris = currentUris.filter((uri) => uri !== uriToRemove);
    updateFormField(updatedUris);
  };

  const renderFileItem = (file: any) => {
    if (props.listType === "picture") {
      return (
        <div
          key={file.uid}
          style={{
            display: "inline-block",
            margin: "8px",
            position: "relative",
          }}
        >
          <Image src={file.url} width={96} height={96} />
          <div
            style={{
              position: "absolute",
              top: 0,
              right: 0,
              background: "rgba(0,0,0,0.5)",
              borderRadius: "50%",
              width: "20px",
              height: "20px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              cursor: "pointer",
            }}
            onClick={() => handleRemove(file)}
          >
            <IconDelete style={{ color: "white", fontSize: "12px" }} />
          </div>
        </div>
      );
    } else {
      return (
        <div
          key={file.uid}
          style={{
            display: "flex",
            alignItems: "center",
            padding: "8px",
            border: "1px solid #d9d9d9",
            margin: "4px 0",
          }}
        >
          <span style={{ flex: 1 }}>{file.name}</span>
          <Button
            icon={<IconDelete />}
            type="tertiary"
            theme="borderless"
            size="small"
            onClick={() => handleRemove(file)}
          />
        </div>
      );
    }
  };

  const isImg = Boolean((props?.type ?? "img") === "img");

  return (
    <Form.Slot
      label={{
        text: props.label,
        required: props.isRequired,
        align: "right",
      }}
    >
      <div>
        {/* 显示已上传的文件 */}
        <div style={{ marginBottom: "8px" }}>
          {fileList.map(renderFileItem)}
        </div>

        {/* 上传按钮 */}
        {(!props.multiple || fileList.length === 0 || props.multiple) && (
          <SemiUpload
            action={upload_url}
            headers={{
              Authorization: `Bearer ${localStorage.getItem("token")}`,
            }}
            accept={isImg ? "image/*" : props.accept}
            maxSize={props.maxSize}
            onSizeError={(file) => Toast.error(`${file.name} 大小超过限制`)}
            onSuccess={handleSuccess}
            data={{
              type: isImg ? 1 : 2,
            }}
            showUploadList={false} // 不显示内置的文件列表
            multiple={props.multiple}
            disabled={props.disabled}
          >
            {props.children || (
              <Button icon={<IconUpload />} disabled={props.disabled}>
                {isImg ? "上传图片" : "上传文件"}
              </Button>
            )}
          </SemiUpload>
        )}
      </div>
    </Form.Slot>
  );
};
