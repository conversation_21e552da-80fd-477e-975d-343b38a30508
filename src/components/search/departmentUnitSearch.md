# DepartmentUnitSearch 组件说明

> 位于 `src/components/search/departmentUnitSearch.tsx`

## 组件功能

`DepartmentUnitSearch` 是一个通用筛选组件，根据传入的 `unitType` 枚举动态切换：

| unitType (UnitType) | 渲染组件         | 说明                                          |
| ------------------- | ---------------- | --------------------------------------------- |
| Department (1)      | DepartmentSearch | 内部部门树选择                                |
| Contractor (2)      | RestSelect       | 承包商下拉（数据源 useContractorListOptions） |

这样既兼容内部部门，又支持外协承包商的过滤需求。

## Props

| 名称          | 类型                            | 必填 | 说明                                                                                                                                                        |
| ------------- | ------------------------------- | ---- | ----------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `field`       | `string`                        | ✔️   | 表单字段名                                                                                                                                                  |
| `unitType`    | `UnitType \| null`              | ✔️   | 组织单元类型，决定渲染模式                                                                                                                                  |
| `placeholder` | `string`                        |      | 占位提示文字，默认“请选择部门/单位”                                                                                                                         |
| `disabled`    | `boolean`                       |      | 是否禁用                                                                                                                                                    |
| `isRequired`  | `boolean`                       |      | 是否为必填项，内部自动添加校验规则                                                                                                                          |
| `fieldKeyMap` | `{ [key in UnitType]: string }` |      | 可选。若不同 `unitType` 在后端对应的字段名不一致，可通过该映射指定，例如 `{ [UnitType.Department]: 'departmentId', [UnitType.Contractor]: 'contractorId' }` |

其余属性会透传到内部的 `DepartmentSearch` / `RestSelect` 组件。

## 使用示例

```tsx
import { DepartmentUnitSearch, UnitType } from "components/search";

<Form>
  {/* objectType 来自父级业务数据（如 plan.objectType） */}
  <DepartmentUnitSearch
    field="personUnit"
    unitType={plan.objectType === 3 ? UnitType.Contractor : UnitType.Department}
    fieldKeyMap={{
      [UnitType.Department]: "departmentId",
      [UnitType.Contractor]: "contractorId",
    }}
    placeholder="请选择部门/单位"
  />
</Form>;
```

## 依赖

- `DepartmentSearch` – 部门树选择组件
- `RestSelect` – 通用枚举下拉组件
- `useContractorListOptions` – 承包商列表 Hook，用于获取下拉数据

## 注意事项

1. **必须** 在使用处传入正确的 `unitType`，否则无法切换组件。
2. 当 `unitType === UnitType.Contractor` 时，组件会发起一次承包商列表请求；其他情况不会产生额外请求。
3. 可通过 `fieldKeyMap` 指定不同类型对应的后端字段名，默认使用 `field`。
4. 组件与业务无关，可在任何场景下复用，只要提供 `unitType`。
