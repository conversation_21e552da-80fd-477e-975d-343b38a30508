import { Form } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { getSubjectList } from "api";
import { subjectDataAtom } from "atoms";
import { convert } from "components/tree";
import { useRemoteSearch } from "hooks";
import { useAtom } from "jotai";
import { find, propEq } from "ramda";
import { FC, useEffect, useMemo, useState } from "react";

export type TrainingSubjectSearchProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  disabled?: boolean;
};

export const TrainingSubjectSearch: FC<TrainingSubjectSearchProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  disabled = false,
}) => {
  const [query, onSearch] = useRemoteSearch();
  const [subjectData, setSubjectData] = useAtom(subjectDataAtom);

  const [expandedKeys, setExpandedKeys] = useState<string[]>(["0"]);
  const { isLoading, isError, error, data, refetch } = useQuery({
    queryKey: ["getSubjectList"],
    queryFn: getSubjectList,
  });

  useEffect(() => {
    if (data?.data?.length) {
      setSubjectData(data?.data);
    }
  }, [data, setSubjectData]);

  // 转换函数 - 保留备用，但不使用
  function convert_deprecated(data: any): any[] {
    const result: any[] = [];
    const map: Record<string, any> = {};
    const hasIds: number[] = [];

    function traverse(items: any[], parentKey: string | null) {
      (items ?? []).forEach((_item: any) => {
        let item = _item;
        if (typeof item === "number") {
          item = find(propEq(item, "id"))(data.data);
        }

        const key = parentKey ? `${parentKey}-${item.id}` : String(item.id);

        const treeItem = {
          label: item.name,
          value: item.id,
          key,
          children: [],
        };

        map[key] = treeItem;

        if (item.children) {
          traverse(item.children, key);
        }
        if (hasIds.includes(item.id)) {
          return;
        }
        if (parentKey) {
          map[parentKey].children.push(treeItem);
        } else {
          result.push(treeItem);
        }
        hasIds.push(item.id);
      });
    }

    traverse(data.data, null);
    return result;
  }

  const treeData = useMemo(() => {
    if (data?.data?.length) {
      return [
        {
          label: "知识科目分类",
          value: 0,
          key: "0",
          children: convert(data),
        },
      ];
    }
    return [];
  }, [data]);

  useEffect(() => {
    const keys: string[] = [];
    function traverse(items: any[]) {
      (items ?? []).forEach((item: any) => {
        keys.push(item.key);
        if (item.children) {
          traverse(item.children);
        }
      });
    }
    traverse(treeData);
    setExpandedKeys(keys);
  }, [treeData]);

  return (
    <Form.TreeSelect
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      treeData={treeData}
      filterTreeNode
      expandAll
      expandedKeys={expandedKeys}
      placeholder={placeholder ?? "所属知识科目"}
      className="w-full"
      disabled={disabled}
      rules={[{ required: isRequired, message: "此为必填项!" }]}
      dropdownStyle={{ maxHeight: "600px", overflow: "auto" }}
    />
  );
};
