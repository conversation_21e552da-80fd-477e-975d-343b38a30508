import type { FieldList } from "components/enum/common";
import { DepartmentSearch } from "components/search";
import { RestSelect } from "components/select";
import { useContractorListOptions } from "hooks";
import { FC } from "react";

// 统一组织单元类型枚举，供外部调用方引用，避免魔法数字
export enum UnitType {
  Department = 1, // 内部部门
  Contractor = 2, // 承包商 / 外协单位
}

interface DepartmentUnitSearchProps {
  field: string;
  unitType: UnitType | null;
  placeholder?: string;
  disabled?: boolean;
  isRequired?: boolean;
  fieldKeyMap?: {
    [key in UnitType]: string;
  };
}

/**
 * DepartmentUnitSearch
 * 根据 `unitType` 的值动态选择筛选控件：
 *   UnitType.Department ➜ 渲染 `DepartmentSearch`（内部部门树）
 *   UnitType.Contractor ➜ 渲染 `RestSelect`（承包商下拉，数据源 useContractorListOptions）
 *
 * 调用方 **必须** 传入正确的 `unitType`，否则可能无法选到期望的数据源。
 */
export const DepartmentUnitSearch: FC<DepartmentUnitSearchProps> = ({
  field,
  unitType,
  placeholder = "请选择部门/单位",
  disabled = false,
  isRequired,
  fieldKeyMap,
}) => {
  const contractorOptions = useContractorListOptions();

  if (fieldKeyMap) {
    field = fieldKeyMap[unitType as keyof typeof fieldKeyMap];
  }

  if (unitType === UnitType.Contractor) {
    return (
      <RestSelect
        field={field}
        placeholder={placeholder}
        options={contractorOptions as FieldList}
        disabled={disabled}
        isRequired={isRequired}
      />
    );
  }

  return (
    <DepartmentSearch
      field={field}
      placeholder={placeholder}
      disabled={disabled}
      isRequired={isRequired}
    />
  );
};
