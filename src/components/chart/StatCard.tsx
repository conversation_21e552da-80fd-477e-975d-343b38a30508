import { useQuery } from "@tanstack/react-query";
import LoadingOrError from "components/LoadingOrError";
import { useMemo } from "react";
import { Link } from "react-router-dom";

export interface StatCardItem {
  label: string; // 卡片标题
  valueField: string; // 数据字段路径（支持嵌套）
  bgColor?: string; // 自定义背景色
  icon?: string; // 图标路径
  onClick?: () => void; // 自定义点击事件
  to?: string; // 路由跳转
  render?: (value: any, data: any) => React.ReactNode; // 自定义渲染
}

export interface StatCardProps {
  queryKey: any[]; // TanStack Query 缓存键
  queryFn: (params?: any) => Promise<any>; // 数据请求函数
  filter?: object; // 请求参数
  cards: StatCardItem[]; // 卡片配置列表
  columns?: number; // 列数（默认自动计算）
  height?: number; // 卡片高度（默认160px）
  gap?: number; // 间距（默认20px）
  className?: string; // 额外样式类
}

// 默认渐变色配置，与现有风格一致
const DEFAULT_CARD_COLORS = [
  "bg-gradient-to-r from-[#44DFDF] to-[#1EA0EA]",
  "bg-gradient-to-r from-[#25AFFE] to-[#AF62C7]",
  "bg-gradient-to-r from-[#F4C258] to-[#F99336]",
  "bg-gradient-to-r from-[#94DD64] to-[#0BCB96]",
  "bg-gradient-to-r from-[#FFB347] to-[#FFCC33]",
];

/**
 * 从嵌套对象中获取值，支持点号路径
 */
function getNestedValue(obj: any, path: string): any {
  return path.split(".").reduce((current, key) => current?.[key], obj);
}

/**
 * 通用统计卡片组件
 * 支持数据请求、灵活配置、多种交互方式
 */
export function StatCard({
  queryKey,
  queryFn,
  filter = {},
  cards,
  columns,
  height = 160,
  gap = 20,
  className = "",
}: StatCardProps) {
  // 数据请求
  const { data, isLoading } = useQuery({
    queryKey: [...queryKey, filter],
    queryFn: () => queryFn(filter),
  });

  // 自动计算列数
  const gridColumns = useMemo(() => {
    if (columns) return columns;
    const cardCount = cards.length;
    if (cardCount <= 3) return cardCount;
    if (cardCount <= 4) return 4;
    return 5;
  }, [columns, cards.length]);

  // 处理数据
  const processedData = useMemo(() => {
    return data?.data ?? {};
  }, [data]);

  // 渲染单个卡片
  const renderCard = (card: StatCardItem, index: number) => {
    const value = getNestedValue(processedData, card.valueField);
    const bgColor =
      card.bgColor || DEFAULT_CARD_COLORS[index % DEFAULT_CARD_COLORS.length];

    const cardContent = (
      <div
        className={`flex justify-between overflow-hidden rounded-lg ${bgColor} p-6 relative group ${card.to || card.onClick ? "cursor-pointer" : ""}`}
        style={{ height: `${height}px` }}
        onClick={card.onClick}
      >
        <div className="flex flex-col text-white justify-evenly">
          {/* 主数据，字体加粗加大 */}
          <p className="font-black text-[34px] group-hover:underline">
            {card.render ? card.render(value, processedData) : (value ?? 0)}
          </p>
          {/* 标题 */}
          <p className="text-lg">{card.label}</p>
        </div>
        {/* 图标 */}
        {card.icon && (
          <div className="flex items-center justify-center absolute top-[4px] right-0">
            <img src={card.icon} alt={card.label} />
          </div>
        )}
      </div>
    );

    // 如果有路由跳转，包装为Link
    if (card.to) {
      return (
        <Link key={card.label} to={card.to} className="block">
          {cardContent}
        </Link>
      );
    }

    return <div key={card.label}>{cardContent}</div>;
  };

  // 加载状态
  if (isLoading) {
    return (
      <div className={`${className}`} style={{ height: `${height}px` }}>
        <LoadingOrError />
      </div>
    );
  }

  return (
    <div
      className={`grid ${className}`}
      style={{
        gridTemplateColumns: `repeat(${gridColumns}, 1fr)`,
        gap: `${gap}px`,
      }}
    >
      {cards.map((card, index) => renderCard(card, index))}
    </div>
  );
}
