import { Tag } from "@douyinfe/semi-ui";
import {
  ISNORMAL_MAP,
  PERSONNEL_ALARM_STATUS_MAP,
  SENSOR_ALERTPRIORITY_MAP,
} from "components/enum";

// 报警详情数据接口
interface AlarmDetail {
  id: number;
  name: string;
  code: string;
  priority: number;
  unit: string;
  monitorTypeValue: {
    highAlarm: number;
    highHighAlarm: number;
    lowAlarm: number;
    lowLowAlarm: number;
    sampleValue: number;
  };
  alarmTime: string;
  alarmPivot: number;
  processPerson?: {
    processTime: string;
    normalTime: string;
  };
  alarmReasonTypeValue?: any;
  alarmMeasureTypeValue?: any;
  note?: string;
  alarmType: string;
  isNormal: number;
  status: number;
}

interface AlarmDetailCardProps {
  alarm: AlarmDetail;
}

/**
 * 报警详情卡片组件
 * 用于在 Popover 中展示报警的详细信息
 */
export const AlarmDetailCard = ({ alarm }: AlarmDetailCardProps) => {
  // 使用现有枚举映射获取显示信息
  const priorityItem = SENSOR_ALERTPRIORITY_MAP.find(
    (item) => item.id === alarm.priority
  );
  const statusItem = PERSONNEL_ALARM_STATUS_MAP.find(
    (item) => item.id === alarm.status
  );
  const normalItem = ISNORMAL_MAP.find((item) => item.id === alarm.isNormal);

  const priority = priorityItem || { name: "未知", color: "grey" };
  const status = statusItem
    ? {
        ...statusItem,
        color: statusItem.id === 1 ? "orange" : "green",
      }
    : { name: "未知", color: "grey" };
  const normal = normalItem || { name: "未知", color: "grey" };

  return (
    <div className="w-80 p-4 space-y-4">
      {/* 标题区域 */}
      <div className="border-b pb-3">
        <div className="flex items-center justify-between mb-2">
          <h4 className="text-lg font-semibold text-gray-800">报警详情</h4>
          <Tag color={priority.color as any} size="small">
            {priority.name}优先级
          </Tag>
        </div>
        <p className="text-sm text-gray-600">ID: {alarm.id}</p>
      </div>

      {/* 基本信息 */}
      <div className="space-y-3">
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <span className="text-gray-500">监测名称:</span>
            <p className="font-medium text-gray-800 mt-1">{alarm.name}</p>
          </div>
          <div>
            <span className="text-gray-500">监测编码:</span>
            <p className="font-medium text-gray-800 mt-1">{alarm.code}</p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <span className="text-gray-500">报警类型:</span>
            <p className="font-medium text-gray-800 mt-1">{alarm.alarmType}</p>
          </div>
          <div>
            <span className="text-gray-500">计量单位:</span>
            <p className="font-medium text-gray-800 mt-1">
              {alarm.unit || "--"}
            </p>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <span className="text-gray-500">报警时间:</span>
            <p className="font-medium text-gray-800 mt-1">
              {alarm.alarmTime
                ? new Date(alarm.alarmTime).toLocaleString("zh-CN")
                : "--"}
            </p>
          </div>
          <div>
            <span className="text-gray-500">当前状态:</span>
            <div className="mt-1">
              <Tag color={status.color as any} size="small">
                {status.name}
              </Tag>
              <Tag color={normal.color as any} size="small" className="ml-1">
                {normal.name}
              </Tag>
            </div>
          </div>
        </div>
      </div>

      {/* 监测数值 */}
      {alarm.monitorTypeValue && (
        <div className="border-t pt-3">
          <h5 className="text-sm font-medium text-gray-700 mb-2">监测数值</h5>
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="bg-red-50 p-2 rounded">
              <span className="text-red-600">高高报警:</span>
              <span className="font-medium ml-1">
                {alarm.monitorTypeValue.highHighAlarm}
              </span>
            </div>
            <div className="bg-orange-50 p-2 rounded">
              <span className="text-orange-600">高报警:</span>
              <span className="font-medium ml-1">
                {alarm.monitorTypeValue.highAlarm}
              </span>
            </div>
            <div className="bg-blue-50 p-2 rounded">
              <span className="text-blue-600">低报警:</span>
              <span className="font-medium ml-1">
                {alarm.monitorTypeValue.lowAlarm}
              </span>
            </div>
            <div className="bg-purple-50 p-2 rounded">
              <span className="text-purple-600">低低报警:</span>
              <span className="font-medium ml-1">
                {alarm.monitorTypeValue.lowLowAlarm}
              </span>
            </div>
          </div>
          <div className="mt-2 bg-gray-50 p-2 rounded text-xs">
            <span className="text-gray-600">采样值:</span>
            <span className="font-medium ml-1">
              {alarm.monitorTypeValue.sampleValue}
            </span>
          </div>
        </div>
      )}

      {/* 处理信息 */}
      {alarm.processPerson && (
        <div className="border-t pt-3">
          <h5 className="text-sm font-medium text-gray-700 mb-2">处理信息</h5>
          <div className="space-y-2 text-xs">
            <div>
              <span className="text-gray-500">处理时间:</span>
              <span className="ml-2">
                {alarm.processPerson.processTime || "--"}
              </span>
            </div>
            <div>
              <span className="text-gray-500">恢复正常时间:</span>
              <span className="ml-2">
                {alarm.processPerson.normalTime || "--"}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* 备注信息 */}
      {alarm.note && (
        <div className="border-t pt-3">
          <h5 className="text-sm font-medium text-gray-700 mb-2">备注</h5>
          <p className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
            {alarm.note}
          </p>
        </div>
      )}
    </div>
  );
};
