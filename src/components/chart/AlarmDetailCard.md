# AlarmDetailCard 报警详情卡片组件文档

## 1. 组件简介

`AlarmDetailCard` 是一个专门用于在 Popover 中展示报警详细信息的卡片组件。它提供了结构化的报警信息展示，包括基本信息、监测数值、处理状态等，适用于报警列表、时序分析等需要查看报警详情的场景。

### 核心特性

- **结构化展示**：分区域展示不同类型的报警信息
- **枚举映射**：复用系统现有枚举，确保显示一致性
- **响应式设计**：固定宽度，适配 Popover 容器
- **视觉层次**：使用颜色标签和分组布局提升可读性
- **条件显示**：根据数据可用性智能显示相关区块

---

## 2. Props 参数说明

### AlarmDetailCardProps

| 参数名 | 类型         | 说明           | 是否必填 |
| ------ | ------------ | -------------- | -------- |
| alarm  | AlarmDetail  | 报警详情数据   | 是       |

### AlarmDetail 接口

| 字段名               | 类型                    | 说明                     | 示例值                    |
| -------------------- | ----------------------- | ------------------------ | ------------------------- |
| id                   | number                  | 报警ID                   | 92233720368547760         |
| name                 | string                  | 监测名称                 | "温度传感器"              |
| code                 | string                  | 监测编码                 | "TEMP_001"                |
| priority             | number                  | 报警优先级 (1-3)         | 3                         |
| unit                 | string                  | 计量单位                 | "℃"                       |
| alarmTime            | string                  | 报警时间                 | "2024-01-15T10:30:00Z"    |
| alarmType            | string                  | 报警类型                 | "高温报警"                |
| isNormal             | number                  | 是否正常 (1=正常, 2=异常) | 2                         |
| status               | number                  | 处理状态 (1=未处理, 2=已处理) | 1                         |
| monitorTypeValue     | MonitorTypeValue        | 监测数值对象             | 见下表                    |
| processPerson        | ProcessPerson (可选)    | 处理信息                 | 见下表                    |
| note                 | string (可选)           | 备注信息                 | "需要立即处理"            |

### MonitorTypeValue 接口

| 字段名         | 类型   | 说明       |
| -------------- | ------ | ---------- |
| highAlarm      | number | 高报警值   |
| highHighAlarm  | number | 高高报警值 |
| lowAlarm       | number | 低报警值   |
| lowLowAlarm    | number | 低低报警值 |
| sampleValue    | number | 采样值     |

### ProcessPerson 接口

| 字段名      | 类型   | 说明         |
| ----------- | ------ | ------------ |
| processTime | string | 处理时间     |
| normalTime  | string | 恢复正常时间 |

---

## 3. 枚举映射说明

组件使用系统现有枚举确保显示一致性：

### 优先级映射 (SENSOR_ALERTPRIORITY_MAP)
来源：`components/enum/majorHazard.tsx`

| ID | 名称 | 颜色  | 说明     |
| -- | ---- | ----- | -------- |
| 1  | 普通 | green | 普通报警 |
| 2  | 重要 | yellow| 重要报警 |
| 3  | 紧急 | red   | 紧急报警 |

### 处理状态映射 (PERSONNEL_ALARM_STATUS_MAP)
来源：`components/enum/majorHazard.tsx`

| ID | 名称   | 组件内颜色 | 说明       |
| -- | ------ | ---------- | ---------- |
| 1  | 未处理 | orange     | 待处理状态 |
| 2  | 已处理 | green      | 已处理状态 |

### 正常状态映射 (ISNORMAL_MAP)
来源：`components/enum/system.tsx`

| ID | 名称 | 颜色  | 说明     |
| -- | ---- | ----- | -------- |
| 1  | 正常 | green | 正常状态 |
| 2  | 异常 | red   | 异常状态 |

---

## 4. 视觉设计

### 布局结构

```
┌─────────────────────────────────────┐
│ 标题区域                            │
│ ├─ 报警详情 + 优先级标签            │
│ └─ 报警ID                           │
├─────────────────────────────────────┤
│ 基本信息 (2列网格)                  │
│ ├─ 监测名称    ├─ 监测编码          │
│ ├─ 报警类型    ├─ 计量单位          │
│ └─ 报警时间    └─ 当前状态          │
├─────────────────────────────────────┤
│ 监测数值 (条件显示)                 │
│ ├─ 高高报警 ├─ 高报警               │
│ ├─ 低报警   ├─ 低低报警             │
│ └─ 采样值                           │
├─────────────────────────────────────┤
│ 处理信息 (条件显示)                 │
│ ├─ 处理时间                         │
│ └─ 恢复正常时间                     │
├─────────────────────────────────────┤
│ 备注信息 (条件显示)                 │
└─────────────────────────────────────┘
```

### 样式特点

- **固定宽度**：320px (w-80)，适配 Popover 容器
- **分区设计**：使用 border-t 分隔不同信息区域
- **颜色编码**：监测数值使用不同背景色区分类型
- **标签系统**：优先级和状态使用 Semi UI Tag 组件
- **响应式间距**：统一的 space-y-4 垂直间距

---

## 5. 使用示例

### 5.1 基本用法

```tsx
import { Popover } from "@douyinfe/semi-ui";
import { AlarmDetailCard } from "components/chart/AlarmDetailCard";

const AlarmList = ({ alarms }) => {
  return (
    <div>
      {alarms.map(alarm => (
        <Popover
          key={alarm.id}
          content={<AlarmDetailCard alarm={alarm} />}
          trigger="hover"
          position="rightTop"
          showArrow
        >
          <span className="text-blue-500 cursor-pointer">
            {alarm.id}
          </span>
        </Popover>
      ))}
    </div>
  );
};
```

### 5.2 在表格中使用

```tsx
const renderAlarmList = (alarmList: AlarmDetail[]) => {
  return (
    <div className="flex flex-wrap gap-1">
      {alarmList.map((alarm, index) => (
        <Popover
          key={alarm.id || index}
          content={<AlarmDetailCard alarm={alarm} />}
          trigger="hover"
          position="rightTop"
          showArrow
        >
          <span className="inline-block px-2 py-1 text-xs bg-blue-100 text-blue-600 rounded cursor-pointer hover:bg-blue-200 transition-colors">
            {alarm.id}
          </span>
        </Popover>
      ))}
    </div>
  );
};
```

### 5.3 完整的表格列配置

```tsx
const columns = [
  {
    title: "报警列表",
    dataIndex: "alarmList",
    render: (alarmList: AlarmDetail[]) => {
      if (!alarmList || alarmList.length === 0) {
        return <span className="text-gray-400">--</span>;
      }
      return renderAlarmList(alarmList);
    },
    align: "left" as const,
  },
];
```

---

## 6. 技术要点

### 6.1 枚举映射处理

```tsx
// 使用 find 方法查找对应枚举项
const priorityItem = SENSOR_ALERTPRIORITY_MAP.find(item => item.id === alarm.priority);

// 为缺少 color 属性的枚举添加颜色逻辑
const status = statusItem ? { 
  ...statusItem, 
  color: statusItem.id === 1 ? "orange" : "green" 
} : { name: "未知", color: "grey" };
```

### 6.2 条件渲染

```tsx
// 监测数值区域
{alarm.monitorTypeValue && (
  <div className="border-t pt-3">
    {/* 监测数值内容 */}
  </div>
)}

// 处理信息区域
{alarm.processPerson && (
  <div className="border-t pt-3">
    {/* 处理信息内容 */}
  </div>
)}
```

### 6.3 时间格式化

```tsx
// 安全的时间格式化
{alarm.alarmTime ? new Date(alarm.alarmTime).toLocaleString("zh-CN") : "--"}
```

### 6.4 类型安全

```tsx
// 使用 as any 处理 Semi UI Tag 的颜色类型限制
<Tag color={priority.color as any} size="small">
  {priority.name}优先级
</Tag>
```

---

## 7. 最佳实践

1. **数据验证**：始终检查数据可用性，提供默认值
2. **枚举复用**：优先使用系统现有枚举，保持一致性
3. **条件显示**：根据数据可用性智能显示相关区块
4. **视觉层次**：使用颜色和分组提升信息可读性
5. **响应式设计**：确保在不同容器中的显示效果

---

## 8. 注意事项

- 组件设计为 Popover 内容使用，不适合独立页面展示
- 依赖 Semi UI 的 Tag 组件，确保项目已安装
- 枚举映射依赖 `components/enum` 目录的相关文件
- 时间显示使用本地化格式，适配中文环境
- 颜色值可能需要 `as any` 类型断言以兼容 Semi UI 限制

---

如需更详细的使用示例或自定义配置，请随时告知！
