import { IconCalendar, IconClock } from "@douyinfe/semi-icons";

export interface TimePeriodCardProps {
  beginTime: string;
  endTime: string;
  title?: string;
  bgColor?: string;
  height?: number;
  className?: string;
  onClick?: () => void;
}

/**
 * 时间段显示卡片组件
 * 专门用于显示起止时间段，美观直观
 */
export function TimePeriodCard({
  beginTime,
  endTime,
  title = "峰值时间段",
  bgColor = "bg-gradient-to-r from-[#667eea] to-[#764ba2]",
  height = 160,
  className = "",
  onClick,
}: TimePeriodCardProps) {
  // 检查是否有有效数据
  const hasValidData = beginTime && endTime;

  // 格式化日期时间显示（中国习惯：MM/dd HH:mm:ss）
  const formatDateTime = (timeStr: string) => {
    if (!timeStr) return "--/-- --:--:--";
    try {
      const date = new Date(timeStr);
      const dateStr = date.toLocaleDateString("zh-CN", {
        month: "2-digit",
        day: "2-digit",
      });
      const timeOnlyStr = date.toLocaleTimeString("zh-CN", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: false,
      });
      return `${dateStr} ${timeOnlyStr}`;
    } catch {
      return timeStr;
    }
  };

  // 计算时间段长度（精确到秒）
  const calculateDuration = () => {
    if (!beginTime || !endTime) return "";
    try {
      const start = new Date(beginTime);
      const end = new Date(endTime);
      const diffMs = end.getTime() - start.getTime();
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
      const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
      const diffSeconds = Math.floor((diffMs % (1000 * 60)) / 1000);

      if (diffHours > 0) {
        return `${diffHours}小时${diffMinutes}分钟${diffSeconds}秒`;
      }
      if (diffMinutes > 0) {
        return `${diffMinutes}分钟${diffSeconds}秒`;
      }
      return `${diffSeconds}秒`;
    } catch {
      return "";
    }
  };

  const duration = calculateDuration();

  return (
    <div
      className={`flex flex-col justify-between overflow-hidden rounded-lg ${bgColor} p-6 relative group ${onClick ? "cursor-pointer" : ""} ${className}`}
      style={{ height: `${height}px` }}
      onClick={onClick}
    >
      {/* 上方：标题 */}
      <div className="flex items-center gap-2 text-white/90 text-lg">
        <IconClock size="small" />
        <span>{title}</span>
      </div>

      {/* 中间：主要时间显示区域 */}
      <div className="flex-1 flex flex-col justify-center text-white">
        {hasValidData ? (
          /* 有数据时显示时间段信息 - 三行展示 */
          <div className="text-center">
            {/* 第1行：开始日期时间 */}
            <div className="text-xl font-black leading-tight">
              {formatDateTime(beginTime)}
            </div>

            {/* 第2行："至" */}
            <div className="text-white/60 text-sm">至</div>

            {/* 第3行：结束日期时间 */}
            <div className="text-xl font-black leading-tight">
              {formatDateTime(endTime)}
            </div>
          </div>
        ) : (
          /* 无数据时显示"-"文案 */
          <div className="text-center">
            <div className="text-[34px] font-black text-white/90">-</div>
          </div>
        )}
      </div>

      {/* 底部：持续时长 - 只在有数据时显示 */}
      {hasValidData && duration && (
        <div className="flex items-center gap-2 text-white/80 text-base">
          <IconCalendar size="small" />
          <span>持续 {duration}</span>
        </div>
      )}

      {/* 悬浮效果 */}
      <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 rounded-lg"></div>
    </div>
  );
}
