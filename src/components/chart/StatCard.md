# StatCard 通用统计卡片组件文档

## 1. 组件简介

`StatCard` 是一个高度可复用的统计卡片组件，适用于首页、统计页等所有需要展示数值统计的业务场景。它统一了数据请求、样式、布局、交互等逻辑，极大减少了重复代码。

支持多种交互方式：纯展示、路由跳转、自定义点击事件等。

---

## 2. Props 参数说明

| 参数名    | 类型                          | 说明                                                          | 是否必填 |
| --------- | ----------------------------- | ------------------------------------------------------------- | -------- |
| queryKey  | any[]                         | 用于 react-query 的缓存 key，建议包含接口名和依赖参数         | 是       |
| queryFn   | (params?: any) => Promise<any> | 数据请求函数，返回 Promise，参数为 filter                     | 是       |
| filter    | object                        | 额外的请求参数（如 areaId、时间范围等），会自动合并到请求参数 | 否       |
| cards     | StatCardItem[]                | 卡片配置列表，定义每个卡片的标题、数据字段、样式、交互等      | 是       |
| columns   | number                        | 列数，默认根据卡片数量自动计算（≤3为实际数量，≤4为4列，否则5列） | 否       |
| height    | number                        | 卡片高度，默认160px                                           | 否       |
| gap       | number                        | 卡片间距，默认20px                                            | 否       |
| className | string                        | 额外的CSS类名                                                 | 否       |

### StatCardItem 说明

| 字段       | 类型                              | 说明                                                    |
| ---------- | --------------------------------- | ------------------------------------------------------- |
| label      | string                            | 卡片标题                                                |
| valueField | string                            | 数据字段路径，支持嵌套（如 "user.profile.name"）       |
| bgColor    | string                            | 自定义背景色（Tailwind类名），否则使用默认渐变色        |
| icon       | string                            | 图标路径，显示在卡片右上角                              |
| onClick    | () => void                        | 自定义点击事件                                          |
| to         | string                            | 路由跳转地址，使用React Router的Link组件                |
| render     | (value, data) => React.ReactNode  | 自定义渲染函数，可以格式化数值或添加特殊样式            |

---

## 3. 组件特性

- **统一数据请求**: 基于 TanStack Query，支持缓存、重试、错误处理
- **自动布局**: 根据卡片数量智能计算列数
- **灵活交互**: 支持纯展示、路由跳转、自定义点击事件
- **样式一致**: 使用统一的渐变色配置，保持视觉风格一致
- **类型安全**: 完整的 TypeScript 类型定义
- **嵌套字段**: 支持从复杂对象中提取嵌套字段值

---

## 4. 典型用法示例

### 4.1 基础用法（纯展示）

```tsx
<StatCard
  queryKey={["getAlarmDashStat"]}
  queryFn={getAlarmDashStat}
  cards={[
    { label: "监测指标数", valueField: "sensorNum" },
    { label: "运行中监测指标", valueField: "sensorActiveNum" },
    { label: "指标类型", valueField: "monitorTypeNum" },
    { label: "报警原因", valueField: "alarmReasonNum" },
    { label: "报警措施", valueField: "alarmMeasureNum" },
  ]}
  columns={5}
  height={160}
/>
```

### 4.2 带路由跳转的用法

```tsx
<StatCard
  queryKey={["getBasicStat"]}
  queryFn={getDashBasicStat}
  cards={[
    { 
      label: "重大危险源", 
      valueField: "majorHazardNum",
      to: "/basic/major-hazard",
      icon: "/images/monitor/card1.png"
    },
    { 
      label: "重点监管工艺", 
      valueField: "keyRegulatoryProcessNum",
      to: "/basic/dangerous-process",
      icon: "/images/monitor/card2.png"
    },
  ]}
  columns={4}
/>
```

### 4.3 带自定义点击事件的用法

```tsx
<StatCard
  queryKey={["getUserStat"]}
  queryFn={getUserStat}
  cards={[
    { 
      label: "在线用户", 
      valueField: "onlineUsers",
      onClick: () => {
        setFilter({ status: 'online' });
        navigate('/users');
      }
    },
    { 
      label: "离线用户", 
      valueField: "offlineUsers",
      onClick: () => {
        setFilter({ status: 'offline' });
        navigate('/users');
      }
    },
  ]}
/>
```

### 4.4 自定义渲染和样式

```tsx
<StatCard
  queryKey={["getFinanceStat"]}
  queryFn={getFinanceStat}
  cards={[
    { 
      label: "总收入", 
      valueField: "totalRevenue",
      render: (value) => `¥${(value / 10000).toFixed(1)}万`,
      bgColor: "bg-gradient-to-r from-green-400 to-green-600"
    },
    { 
      label: "增长率", 
      valueField: "growthRate",
      render: (value) => `${(value * 100).toFixed(1)}%`,
      bgColor: "bg-gradient-to-r from-blue-400 to-blue-600"
    },
  ]}
/>
```

### 4.5 嵌套字段访问

```tsx
<StatCard
  queryKey={["getComplexStat"]}
  queryFn={getComplexStat}
  cards={[
    { label: "用户姓名", valueField: "user.profile.name" },
    { label: "部门名称", valueField: "user.department.name" },
    { label: "项目数量", valueField: "projects.length" },
  ]}
/>
```

---

## 5. 默认渐变色配置

组件内置了5种渐变色，会自动循环使用：

1. `bg-gradient-to-r from-[#44DFDF] to-[#1EA0EA]` - 青蓝色
2. `bg-gradient-to-r from-[#25AFFE] to-[#AF62C7]` - 蓝紫色  
3. `bg-gradient-to-r from-[#F4C258] to-[#F99336]` - 橙黄色
4. `bg-gradient-to-r from-[#94DD64] to-[#0BCB96]` - 绿色
5. `bg-gradient-to-r from-[#FFB347] to-[#FFCC33]` - 金黄色

---

## 6. 迁移建议

### 从现有统计卡片组件迁移

1. **提取数据请求逻辑**: 将 useQuery 的 queryKey 和 queryFn 移到 StatCard 的 props 中
2. **配置卡片数组**: 将原有的数据映射逻辑转换为 cards 配置
3. **保持交互逻辑**: 将点击事件和路由跳转配置到对应的 card 项中
4. **移除重复代码**: 删除原有的样式、布局、状态管理代码

### 示例迁移对比

**迁移前**:
```tsx
// 大量重复的样式和布局代码
const { data, isLoading } = useQuery({...});
return (
  <div className="grid grid-cols-5 gap-5">
    {stats.map((item, idx) => (
      <div className={`...复杂的样式类...`}>
        <p>{item.value}</p>
        <p>{item.label}</p>
      </div>
    ))}
  </div>
);
```

**迁移后**:
```tsx
// 简洁的配置式调用
<StatCard
  queryKey={["getAlarmDashStat"]}
  queryFn={getAlarmDashStat}
  cards={[
    { label: "监测指标数", valueField: "sensorNum" },
    // ...其他卡片配置
  ]}
  columns={5}
/>
```

---

## 7. 注意事项

1. **数据结构**: 确保 queryFn 返回的数据结构中包含 data 字段
2. **字段路径**: valueField 支持点号分隔的嵌套路径
3. **交互冲突**: 同一个卡片不要同时设置 to 和 onClick
4. **性能优化**: 相同的 queryKey 会自动复用缓存
5. **样式覆盖**: 自定义 bgColor 会覆盖默认渐变色

---

如需更详细的使用示例或特殊需求支持，请随时告知！
