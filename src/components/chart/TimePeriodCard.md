# TimePeriodCard 时间段显示组件

专门用于显示起止时间段的卡片组件，提供美观直观的时间段展示方案。

## 功能特点

- 🕒 **时间格式化**: 自动格式化时间显示，支持时分秒和日期
- 📊 **持续时长计算**: 自动计算并显示时间段持续时长
- 🎨 **渐变背景**: 支持自定义渐变背景色
- 📱 **响应式设计**: 适配不同屏幕尺寸
- 🖱️ **交互支持**: 支持点击事件处理
- ⚡ **两种样式**: 提供详细版和紧凑版两种显示方案

## 组件类型

### 1. TimePeriodCard (详细版)

显示完整的时间信息，包括开始时间、结束时间、持续时长等。

```typescript
interface TimePeriodCardProps {
  beginTime: string; // 开始时间 (ISO 字符串)
  endTime: string; // 结束时间 (ISO 字符串)
  title?: string; // 卡片标题
  bgColor?: string; // 背景渐变色
  height?: number; // 卡片高度
  className?: string; // 额外样式类
  onClick?: () => void; // 点击事件
}
```

### 2. CompactTimePeriodCard (紧凑版)

更紧凑的显示方式，适合空间有限的场景。

## 使用示例

### 基础用法

```tsx
import { TimePeriodCard } from "components/chart/TimePeriodCard";

// 详细版 - 显示完整信息
<TimePeriodCard
  beginTime="2024-01-15T09:30:00"
  endTime="2024-01-15T11:45:00"
  title="报警峰值时间段"
  bgColor="bg-gradient-to-r from-[#F4C258] to-[#F99336]"
/>;
```

### 与 StatCard 组合使用

```tsx
import { StatCard } from "components/chart/StatCard";
import { TimePeriodCard } from "components/chart/TimePeriodCard";

// 在报警时序分析页面中的使用
<div className="grid grid-cols-3 gap-5">
  {/* 基础统计数据 */}
  <StatCard
    queryKey={["timing-stat"]}
    queryFn={getTimingStat}
    filter={filter}
    cards={[
      {
        label: "总报警数",
        valueField: "totalNum",
        bgColor: "bg-gradient-to-r from-[#44DFDF] to-[#1EA0EA]",
      },
      {
        label: "最大聚集报警数",
        valueField: "maxConcurrentNum",
        bgColor: "bg-gradient-to-r from-[#25AFFE] to-[#AF62C7]",
      },
    ]}
    columns={2}
  />

  {/* 峰值时间段 */}
  <TimePeriodCard
    beginTime={peakPeriod.beginTime}
    endTime={peakPeriod.endTime}
    title="报警峰值时间段"
    bgColor="bg-gradient-to-r from-[#F4C258] to-[#F99336]"
  />
</div>;
```

### 动态数据绑定

```tsx
// 从 API 获取数据并显示
const TimingStatDisplay = ({ filter }) => {
  const { data } = useQuery({
    queryKey: ["timing-stat", filter],
    queryFn: () => getTimingStat(filter),
  });

  const peakPeriod = data?.data?.peakPeriod;

  return (
    <div>
      {peakPeriod?.beginTime && peakPeriod?.endTime ? (
        <TimePeriodCard
          beginTime={peakPeriod.beginTime}
          endTime={peakPeriod.endTime}
          title="报警峰值时间段"
        />
      ) : (
        <div className="flex items-center justify-center bg-gray-100 rounded-lg h-40">
          <span className="text-gray-500">暂无峰值时间段数据</span>
        </div>
      )}
    </div>
  );
};
```

## 样式配置

### 预设渐变色

```typescript
// 蓝色系
"bg-gradient-to-r from-[#44DFDF] to-[#1EA0EA]";
"bg-gradient-to-r from-[#25AFFE] to-[#AF62C7]";

// 橙色系
"bg-gradient-to-r from-[#F4C258] to-[#F99336]";

// 绿色系
"bg-gradient-to-r from-[#94DD64] to-[#0BCB96]";

// 紫色系
"bg-gradient-to-r from-[#667eea] to-[#764ba2]";
```

### 自定义高度

```tsx
<TimePeriodCard
  height={200} // 自定义高度
  // ... 其他属性
/>
```

## 时间格式处理

组件会自动处理以下时间格式：

- **ISO 字符串**: `"2024-01-15T09:30:00"`
- **时间显示**: `09:30` (24小时制)
- **日期显示**: `01/15`
- **持续时长**: `2小时15分钟` 或 `45分钟`

## 最佳实践

1. **数据验证**: 使用前检查 `beginTime` 和 `endTime` 是否存在
2. **错误处理**: 提供数据为空时的占位显示
3. **响应式布局**: 配合 Grid 布局使用，确保在不同屏幕下的显示效果
4. **颜色一致性**: 与其他统计卡片保持颜色风格一致
5. **交互反馈**: 需要交互时添加 `onClick` 事件和悬浮效果

## 适用场景

- ✅ 报警峰值时间段显示
- ✅ 设备运行时间段
- ✅ 事件发生时间窗口
- ✅ 维护时间段展示
- ✅ 任何需要显示时间范围的场景

## 注意事项

- 确保传入的时间字符串格式正确
- 建议与 TanStack Query 配合使用进行数据管理
- 在数据加载时提供适当的加载状态
- 考虑时区问题，确保时间显示的准确性
