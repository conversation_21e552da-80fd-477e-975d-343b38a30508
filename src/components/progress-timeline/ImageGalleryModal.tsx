import {
  IconChevronLeft,
  IconChevronRight,
  IconClose,
} from "@douyinfe/semi-icons";
import { base_url } from "config";
import React, { useCallback, useEffect, useState } from "react";
import { ImageGalleryModalProps } from "./types";

/**
 * ImageGalleryModal component for displaying image gallery as a full-screen photo wall
 *
 * Features:
 * - Full-screen overlay with large image display
 * - Keyboard navigation (arrow keys, ESC)
 * - Mouse navigation (click arrows, wheel scroll)
 * - Image counter and navigation indicators
 * - Handles image loading errors gracefully
 * - Responsive design for different screen sizes
 */
export const ImageGalleryModal: React.FC<ImageGalleryModalProps> = ({
  visible,
  imageList,
  onClose,
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);

  // Process image URLs to ensure they have the correct base URL
  const processedImageList = imageList.map((imageUrl) => {
    // If the URL already starts with http/https, use it as is
    if (imageUrl?.startsWith?.("http")) {
      return imageUrl;
    }
    // Otherwise, prepend the base_url
    return `${base_url}${imageUrl}`;
  });

  // Reset current index when modal opens or image list changes
  useEffect(() => {
    if (visible) {
      setCurrentIndex(0);
    }
  }, [visible, imageList]);

  // Navigation functions
  const goToNext = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % processedImageList.length);
  }, [processedImageList.length]);

  const goToPrevious = useCallback(() => {
    setCurrentIndex((prev) =>
      prev === 0 ? processedImageList.length - 1 : prev - 1
    );
  }, [processedImageList.length]);

  const goToIndex = useCallback((index: number) => {
    setCurrentIndex(index);
  }, []);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (!visible) return;

      switch (event.key) {
        case "Escape":
          onClose();
          break;
        case "ArrowLeft":
          event.preventDefault();
          goToPrevious();
          break;
        case "ArrowRight":
          event.preventDefault();
          goToNext();
          break;
      }
    };

    if (visible) {
      document.addEventListener("keydown", handleKeyDown);
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [visible, onClose, goToPrevious, goToNext]);

  // Handle mouse wheel navigation
  const handleWheel = useCallback(
    (event: React.WheelEvent) => {
      event.preventDefault();
      if (event.deltaY > 0) {
        goToNext();
      } else {
        goToPrevious();
      }
    },
    [goToNext, goToPrevious]
  );

  if (!visible || !imageList || imageList.length === 0) {
    return <></>;
  }

  return (
    <>
      {/* Global styles for hiding scrollbars */}
      <style
        dangerouslySetInnerHTML={{
          __html: `
          .thumbnail-container::-webkit-scrollbar {
            display: none;
          }
        `,
        }}
      />
      <div
        className="fixed inset-0 z-50 bg-black bg-opacity-90 flex items-center justify-center"
        onClick={onClose}
        onWheel={handleWheel}
      >
        {/* Close button */}
        <button
          className="absolute top-4 right-4 z-10 p-2 text-white hover:text-gray-300 transition-colors"
          onClick={onClose}
          title="关闭 (ESC)"
        >
          <IconClose size="large" />
        </button>

        {/* Image counter */}
        <div className="absolute top-4 left-4 z-10 text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded">
          {currentIndex + 1} / {processedImageList.length}
        </div>

        {/* Main image container */}
        <div
          className="relative max-w-[90vw] max-h-[90vh] flex items-center justify-center"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Previous button */}
          {processedImageList.length > 1 && (
            <button
              className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-2 text-white hover:text-gray-300 transition-colors bg-black bg-opacity-50 rounded-full"
              onClick={goToPrevious}
              title="上一张 (←)"
            >
              <IconChevronLeft size="large" />
            </button>
          )}

          {/* Current image */}
          <img
            src={processedImageList[currentIndex]}
            alt={`图片 ${currentIndex + 1}`}
            className="max-w-full max-h-full object-contain"
            style={{
              maxWidth: "calc(90vw - 8rem)",
              maxHeight: "calc(90vh - 8rem)",
            }}
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.style.display = "none";
            }}
          />

          {/* Next button */}
          {processedImageList.length > 1 && (
            <button
              className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-2 text-white hover:text-gray-300 transition-colors bg-black bg-opacity-50 rounded-full"
              onClick={goToNext}
              title="下一张 (→)"
            >
              <IconChevronRight size="large" />
            </button>
          )}
        </div>

        {/* Thumbnail navigation */}
        {processedImageList.length > 1 && (
          <div
            className="thumbnail-container absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 max-w-[90vw] overflow-x-auto"
            style={{
              scrollbarWidth: "none" /* Firefox */,
              msOverflowStyle: "none" /* IE and Edge */,
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {processedImageList.map((imageUrl, index) => (
              <button
                key={index}
                className={`flex-shrink-0 w-12 h-12 rounded border-2 overflow-hidden transition-all ${
                  index === currentIndex
                    ? "border-white scale-110"
                    : "border-gray-500 hover:border-gray-300"
                }`}
                onClick={(e) => {
                  e.stopPropagation();
                  goToIndex(index);
                }}
                title={`跳转到第 ${index + 1} 张`}
              >
                <img
                  src={imageUrl}
                  alt={`缩略图 ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default ImageGalleryModal;
