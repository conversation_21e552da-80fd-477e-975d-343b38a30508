import { Primitive<PERSON>tom } from "jotai";

/**
 * Employee interface for candidate list
 */
export interface Employee {
  id: string;
  name: string;
  type?: number; // 1: internal, 2: contractor
  _type?: number; // 1: department, 2: work group, 3: contractor
  department?: {
    id: string;
    name: string;
  };
  contractor?: {
    id: string;
    name: string;
  };
}

/**
 * Finish item interface for completed approval records
 */
export interface FinishItem {
  approveTime: string;
  approveUser: {
    id: string;
    name: string;
    type?: number;
  };
  imageList: string[]; // Array of image URLs
}

/**
 * Subprocess item interface for individual steps within a progress item
 */
export interface SubprocessItem {
  name: string;
  status: number; // 1: incomplete/error, other: complete/success
  finishTime?: string;
  candidateList?: Employee[];
  finishList?: FinishItem[]; // Optional array of completed approval records
}

/**
 * Progress item interface for main progress steps
 */
export interface ProgressItem {
  id: string;
  name: string;
  status: number; // 1: incomplete/error, other: complete/success
  subprocessList: SubprocessItem[];
}

/**
 * Props interface for ProgressTimeline component
 */
export interface ProgressTimelineProps {
  data: ProgressItem[];
  isLoading?: boolean;
}

/**
 * Configuration interface for useProgressData hook
 */
export interface UseProgressDataConfig {
  apiFunction: (id: string | number) => Promise<any>;
  atom: PrimitiveAtom<any>;
  queryKey: string;
}

/**
 * Return type interface for useProgressData hook
 */
export interface UseProgressDataReturn {
  isLoading: boolean;
  data: ProgressItem[];
  error?: Error;
}

/**
 * API response interface for progress data
 */
export interface ProgressApiResponse {
  data: ProgressItem[];
}

/**
 * Timeline status render result interface
 */
export interface TimelineStatusResult {
  type: "error" | "success" | "warning" | "default" | "ongoing";
  dot: React.ReactElement;
}

/**
 * Atom info interface for job slice and appointment
 */
export interface AtomInfo {
  id: string | number;
  show: boolean;
}

/**
 * Props interface for FinishListDisplay component
 */
export interface FinishListDisplayProps {
  finishList: FinishItem[];
  onImageClick: (imageList: string[]) => void;
}

/**
 * Props interface for ImageGalleryModal component
 */
export interface ImageGalleryModalProps {
  visible: boolean;
  imageList: string[];
  onClose: () => void;
}
