import { FieldList } from "./common";

export const MAJORRISKORIGIN_CLASSIFY_ISACTIVE_MAP = [
  {
    id: 1,
    name: "是",
    color: "green",
  },
  {
    id: 2,
    name: "否",
    color: "red",
  },
];

export const MAJORRISKORIGIN_MENU_MAP = [
  {
    id: 1,
    name: "重大危险源",
  },
  {
    id: 2,
    name: "风险分区",
  },
  {
    id: 3,
    name: "特殊作业",
  },
  {
    id: 4,
    name: "视频监控",
  },
  {
    id: 5,
    name: "人员定位",
  },
  {
    id: 6,
    name: "车辆定位",
  },
  {
    id: 80,
    name: "其他",
  },
];

export const MAJOR_HAZARD_LEVEL_MAP: FieldList = [
  {
    id: 1,
    name: "一级",
    color: "red",
  },
  {
    id: 2,
    name: "二级",
    color: "orange",
  },
  {
    id: 3,
    name: "三级",
    color: "yellow",
  },
  {
    id: 4,
    name: "四级",
    color: "blue",
  },
  {
    id: 5,
    name: "未评估",
    color: "gray",
  },
];

export const MAJORHAZARD_GAS_TYPE = [
  {
    id: 1,
    name: "有毒气体",
  },
  {
    id: 2,
    name: "可燃气体",
  },
];

export const SENSOR_DATATYPE_MAP = [
  {
    id: 1,
    name: "实时值",
  },
  {
    id: 2,
    name: "状态码",
  },
];

export const SENSOR_ALERTPRIORITY_MAP = [
  {
    id: 1,
    name: "普通",
    color: "green",
  },
  {
    id: 2,
    name: "重要",
    color: "yellow",
  },
  {
    id: 3,
    name: "紧急",
    color: "red",
  },
];

export const PERSONNEL_ALARM_TYPE_MAP = [
  {
    id: 1,
    name: "闯入禁区",
  },
  {
    id: 2,
    name: "区域超员",
  },
  {
    id: 3,
    name: "区域滞留",
  },
  {
    id: 4,
    name: "区域过短",
  },
  {
    id: 5,
    name: "区域静止",
  },
  {
    id: 6,
    name: "SOS",
  },
  {
    id: 7,
    name: "闯入围栏",
  },
  {
    id: 8,
    name: "围栏脱岗",
  },
  {
    id: 10,
    name: "围栏超员",
  },
  {
    id: 13,
    name: "车辆超速",
  },
  {
    id: 14,
    name: "人员卡充电告警",
  },
  {
    id: 15,
    name: "区域缺员",
  },
  {
    id: 16,
    name: "区域脱岗",
  },
  {
    id: 17,
    name: "车辆路线偏移",
  },
  {
    id: 18,
    name: "违停告警",
  },
  {
    id: 19,
    name: "二道门超时",
  },
  {
    id: 23,
    name: "聚集告警",
  },
  {
    id: 24,
    name: "闯入装置区",
  },
];

export const PERSONNEL_ALARM_STATUS_MAP = [
  {
    id: 1,
    name: "未处理",
  },
  {
    id: 2,
    name: "已处理",
  },
];

export const ALARM_TYPE_MAP = [
  {
    id: 1,
    name: "翻越围栏",
  },
  {
    id: 2,
    name: "区域入侵",
  },
  {
    id: 3,
    name: "人员离岗",
  },
  {
    id: 5,
    name: "人员聚集",
  },
  {
    id: 6,
    name: "打电话",
  },
  {
    id: 7,
    name: "抽烟",
  },
  {
    id: 8,
    name: "未佩戴安全帽",
  },
  {
    id: 9,
    name: "未穿着工作服",
  },
  {
    id: 10,
    name: "明火识别",
  },
  {
    id: 11,
    name: "烟雾识别",
  },
];

export const ALARM_STATUS_MAP = [
  {
    id: 1,
    name: "待处理",
  },
  {
    id: 2,
    name: "核实",
  },
  {
    id: 3,
    name: "误报",
  },
];

export const MONITORUNIT_TYPE_MAP = [
  {
    id: 1,
    name: "生产装置",
  },
  {
    id: 2,
    name: "储罐",
  },
  {
    id: 3,
    name: "库房",
  },
  {
    id: 4,
    name: "危险化工工艺",
  },
  {
    id: 5,
    name: "有毒可燃气体",
  },
  {
    id: 6,
    name: "设备",
  },
];

export const SENSOR_ALARMTYPE_MAP = [
  {
    id: 1,
    name: "高报",
  },
  {
    id: 2,
    name: "低报",
  },
  {
    id: 3,
    name: "高高报",
  },
  {
    id: 4,
    name: "低低报",
  },
  {
    id: 5,
    name: "采集异常",
  },
];

export const SENSOR_STATUS_MAP = [
  {
    id: 1,
    name: "正常",
    color: "green",
  },
  {
    id: 2,
    name: "低报",
    color: "red",
  },
  {
    id: 3,
    name: "高报",
    color: "red",
  },
  {
    id: 4,
    name: "低低报",
    color: "red",
  },
  {
    id: 5,
    name: "高高报",
    color: "red",
  },
  {
    id: 6,
    name: "状态码异常",
    color: "red",
  },
];

export const SENSOR_REPORT_TYPE_MAP = [
  {
    id: 1,
    name: "中石化青岛研究院网关上报",
  },
];

export const VIDEO_TYPE_MAP: FieldList = [
  {
    id: 1,
    name: "中控室",
  },
  {
    id: 2,
    name: "办公室",
  },
  {
    id: 3,
    name: "装置区",
  },
  {
    id: 4,
    name: "罐区",
  },
  {
    id: 5,
    name: "库区",
  },
  {
    id: 6,
    name: "装卸区",
  },
  {
    id: 7,
    name: "固定动火区",
  },
  {
    id: 99,
    name: "其他",
  },
];
