import { InputNumber, Modal, Table, Toast } from "@douyinfe/semi-ui";
import { useQuery } from "@tanstack/react-query";
import { atom, useAtom, useAtomValue } from "jotai";
import { union } from "ramda";
import { FC, useCallback, useMemo, useState } from "react";

export const questionPickData = atom([]); // 选中的数据，与PickTable组件中的selectedRecord对应
export const questionPickDataKeys = atom([]); // 选中的数据的keys, 与PickTable组件中的keys对应

type QuestionTableModalProps = {
  children?: React.ReactNode;
  callback: (data: any) => void;
  visible: boolean;
  onClose: () => void;
  title: string;
  entity: string; // 用于queyrKey
  entityTitle: string;
  listApi: any;
  columnsAtom: any;
  filterAtom: any;
  keyColumn?: string;
  initialSelectedKeys: any;
  totalScore?: number; // 试卷总分
};

export const QuestionTableModal: FC<QuestionTableModalProps> = ({
  children,
  callback,
  visible,
  onClose,
  title,
  entity,
  entityTitle,
  listApi,
  columnsAtom,
  keyColumn,
  filterAtom,
  initialSelectedKeys,
  totalScore = 100, // 默认值100分
}) => {
  // const params = useParams<{ id: string, cid: string }>();
  const [selectedRecord, setSelectedRecord] = useAtom(questionPickData);
  const [keys, setKeys] = useAtom(questionPickDataKeys);
  const [score, setScore] = useState(0);
  const columns = useAtomValue(columnsAtom);
  const [filter, setFilter] = useAtom(filterAtom);

  // 计算建议的每题分值
  const suggestedScore = useMemo(() => {
    if (selectedRecord.length === 0 || !totalScore) return 0;
    return Math.floor(totalScore / selectedRecord.length);
  }, [selectedRecord.length, totalScore]);

  // 将分页参数从 filter 中提取出来用于 queryKey
  // const { pageNumber = 1, pageSize = 10, ...restFilter } = filter || {};

  const { isLoading, data, refetch } = useQuery({
    //props
    // 将 pageNumber 和 pageSize 直接放入 queryKey
    // queryKey: [entity, pageNumber, pageSize, restFilter],
    queryKey: [entity, filter],
    queryFn: () => {
      console.log("发起请求，当前filter:", filter); // 添加日志确认 filter 值
      // 确保 listApi 使用的是完整的 filter 对象
      return listApi({
        ...filter, // 传递完整的 filter 对象给 API
      });
    },
    // 保持数据直到新数据加载完成，可以减少闪烁
    keepPreviousData: true,
  });

  const dataSource = useMemo(() => {
    return data?.data ?? {};
  }, [data]);

  const result = useMemo(() => {
    const questions = dataSource?.results ?? [];

    return questions.map((item, index) => {
      item.questionId = item.id;
      return item;
    });
  }, [dataSource]);

  const handlePageChange = useCallback(
    (currentPage: number) => {
      // 创建新对象
      setFilter((prevFilter) => ({
        ...prevFilter,
        pageNumber: currentPage,
      }));
    },
    [setFilter] // 移除 filter 依赖，因为我们用 prevFilter
  );

  const handlePageSizeChange = useCallback(
    (newPageSize: number) => {
      console.debug("pageSize 变更为:", newPageSize);
      // 创建新对象，并重置页码
      setFilter((prevFilter) => ({
        ...prevFilter,
        pageSize: newPageSize,
        pageNumber: 1, // 重置为第一页
      }));
    },
    [setFilter] // 移除 filter 依赖
  );

  /* const rowSelection = {
    onSelect: (record, selected) => {
      if (selected) {
        setSelectedRecord([...selectedRecord, record])
      } else {
        setSelectedRecord(selectedRecord.filter(o => o.id !== record.id))
      }
    },
    onSelectAll: (selected, selectedRows, changeRows) => {
      if (selected) {
        setSelectedRecord([...selectedRecord, ...changeRows])
      } else {
        setSelectedRecord(selectedRecord.filter(o => !find(propEq('id', o.id))(changeRows)))
      }
    },
    selectedRowKeys: keys,
    onChange: (selectedRowKeys, selectedRows) => {
      setKeys(selectedRowKeys)
    }
  } */
  const rowSelection = {
    onSelect: (record, selected) => {
      console.log(`select row: ${selected}`, record);
    },
    onSelectAll: (selected, selectedRows) => {
      console.log(`select all rows: ${selected}`, selectedRows);
    },
    onChange: (selectedRowKeys, selectedRows) => {
      console.log(
        selectedRowKeys,
        selectedRows,
        "selectedRowKeys, selectedRow"
      );
      setKeys(selectedRowKeys);
      /* selectedRows.forEach(o => {
        o[keyColumn ?? 'id'] = o.id // 复制原始数据的ID到关联字段keyColumn
      }) */
      // setSelectedRecord(selectedRows);
      setSelectedRecord((prev) => {
        const remainRows = prev.filter((o) => selectedRowKeys.includes(o.id));
        console.log("remainRows", remainRows);

        const diffSelectedRows = selectedRows.filter((o) => {
          return remainRows.findIndex((r) => r.id === o.id) === -1;
        });
        console.log("diffSelectedRows", diffSelectedRows);

        const finalRes = union(remainRows, diffSelectedRows);
        // console.debug("finalRes", finalRes);
        return finalRes;
        // return [...remainRows, ...selectedRows];
      });
    },
    selectedRowKeys: keys.length ? keys : initialSelectedKeys,
  };

  const handleScoreChange = (value, e) => {
    setScore(value);
  };

  const handleSave = () => {
    if (score <= 0) {
      Toast.error("请填写每题分值");
      return;
    }

    // 先创建更新后的数据，然后再调用callback
    const updatedRecord = selectedRecord.map((item) => ({
      ...item,
      score: score,
    }));

    setSelectedRecord(updatedRecord);
    callback(updatedRecord);
  };

  return (
    <Modal
      title={entityTitle ?? title}
      visible={visible}
      onCancel={onClose}
      // onOk={handleSave}
      width={800}
      keepDOM
      maskClosable={false}
      footer={
        <div className="flex gap-2 justify-end">
          <button
            className="btn rounded btn-sm"
            onClick={() => {
              onClose();
            }}
          >
            取消
          </button>
          <button
            className="btn rounded btn-primary btn-sm"
            onClick={handleSave}
          >
            确定
          </button>
        </div>
      }
      centered
    >
      {children}
      <Table
        // className="rounded overflow-hidden"
        rowKey="id"
        columns={columns?.filter?.((o) => o.fixed || o.isShow)}
        dataSource={result}
        loading={isLoading}
        rowSelection={rowSelection}
        pagination={{
          // currentPage: pageNumber, // 使用从 filter 解构出来的 pageNumber
          // pageSize: pageSize, // 使用从 filter 解构出来的 pageSize
          currentPage: filter.pageNumber ?? 1, // 使用从 filter 解构出来的 pageNumber
          pageSize: filter.pageSize ?? 10, // 使用从 filter 解构出来的 pageSize
          total: dataSource?.totalCount ?? 0,
          popoverPosition: "topRight",
          pageSizeOpts: [10, 15, 20, 50],
          onChange: handlePageChange,
          onPageSizeChange: handlePageSizeChange,
          showSizeChanger: true,
          showQuickJumper: true,
          // showTotal: (total, range) => `共 ${total} 条`,
        }}
      />
      <div className="float-right">
        <InputNumber
          style={{ width: 280 }}
          addonBefore="填充每题分值"
          addonAfter="分"
          placeholder={
            suggestedScore > 0 ? `建议: ${suggestedScore}分` : "请输入分值"
          }
          value={score}
          onChange={handleScoreChange}
        />
        {suggestedScore > 0 && score === 0 && (
          <div className="text-xs text-gray-500 mt-1 flex items-center gap-2">
            建议每题 {suggestedScore} 分（基于选择 {selectedRecord.length} 题）
            <button
              className="btn btn-xs btn-outline"
              onClick={() => setScore(suggestedScore)}
            >
              使用建议值
            </button>
          </div>
        )}
      </div>
    </Modal>
  );
};
