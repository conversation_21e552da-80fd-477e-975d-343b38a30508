import {
  IconApartment,
  IconCloud,
  IconDesktop,
  IconFlag,
  IconHome,
  IconMapPinStroked,
} from "@douyinfe/semi-icons";
import React from "react";

/** Tailwind 600 色板映射（首字母 → 12 色循环） */
const COLOR_POOL = [
  "#14B8A6", // Teal
  "#0284C7", // Sky
  "#4338CA", // Indigo
  "#7E22CE", // Violet
  "#C026D3", // Fuchsia
  "#E11D48", // Rose / Red
  "#DC2626", // Red
  "#F97316", // Orange
  "#F59E0B", // Amber
  "#84CC16", // Lime
  "#10B981", // Emerald
  "#64748B", // Stone
] as const;

export type RegionLevel = "province" | "city" | "zone";
export type AuthProviderType = "system" | "dingtalk" | "landray";

/**
 * 根据省份拼音首字母返回固定色相（12 色循环）
 * @param initial 省份/地区拼音首字母，如 "Y"、"L"
 */
export const pickProvinceColor = (initial: string): string => {
  if (!initial) return COLOR_POOL[0];
  const idx = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".indexOf(initial.toUpperCase());
  if (idx === -1) return COLOR_POOL[0];
  return COLOR_POOL[idx % COLOR_POOL.length];
};

/**
 * 根据认证提供商首字母返回固定色相（12 色循环）
 * @param initial 认证提供商首字母，如 "S"（系统内置）、"D"（钉钉）、"L"（蓝凌OA）
 */
export const pickAuthProviderColor = (initial: string): string => {
  if (!initial) return COLOR_POOL[0];
  const idx = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".indexOf(initial.toUpperCase());
  if (idx === -1) return COLOR_POOL[0];
  return COLOR_POOL[idx % COLOR_POOL.length];
};

/**
 * 根据行政/场景层级或认证提供商类型返回推荐图标
 */
export const getIcon = (
  level: RegionLevel | AuthProviderType
): React.ReactNode => {
  switch (level) {
    case "province":
      return <IconCloud />;
    case "city":
      return <IconMapPinStroked />;
    case "zone":
      return <IconFlag />;
    case "system":
      return <IconDesktop />;
    case "dingtalk":
      return <IconHome />;
    case "landray":
      return <IconApartment />;
    default:
      return <IconFlag />;
  }
};

/**
 * 在保持色相不变的前提下，按 step(0,1,2,…) 提升亮度(每级 8%)。
 * 适用于同省份多插件的颜色区分。
 */
export const shadeColor = (hex: string, step: number = 0): string => {
  // clamp step
  const ratio = Math.max(0, step) * 0.08; // 每级 +8% 亮度
  // 将 hex 转为 r g b
  const num = parseInt(hex.replace("#", ""), 16);
  const r = (num >> 16) & 0xff;
  const g = (num >> 8) & 0xff;
  const b = num & 0xff;
  // 计算新的值，向 255 逼近
  const calc = (channel: number) =>
    Math.min(255, Math.round(channel + (255 - channel) * ratio));
  const nr = calc(r);
  const ng = calc(g);
  const nb = calc(b);
  const newHex = `#${((1 << 24) + (nr << 16) + (ng << 8) + nb).toString(16).slice(1).toUpperCase()}`;
  return newHex;
};

export { COLOR_POOL };
