# Region Plugins 插件框架 & 视觉规范

> 统一维护 **跨业务模块** 的“地区化上报插件”体系。
>
> 该框架已落地于：
>
> - 作业票隐蔽上报设置 `HiddenSWSettings`
> - 双重预防隐蔽上报设置 `HiddenDGSettings`
> - 设备能耗隐蔽上报设置 `HiddenEquipmentSettings`
>
> 下面内容分为两部分：
>
> 1. **插件框架** —— 如何定义 / 渲染 / 识别插件（所有业务共用）
> 2. **应用示例** —— 以上业务模块如何集成该框架（仅示例，供参考）

---

## Part Ⅰ – 插件框架

### 1. 目录结构

```
src/components/region_plugins/
├── utils.tsx          # 颜色 / 图标自动映射 & 公共工具
└── README.md          # ← 本文件
```

> 各业务模块自行维护 **`regionPluginMeta.tsx` + `regionPluginConfigForms.tsx`**：
>
> - `regionPluginMeta.tsx` – 单一数据源，声明所有插件元数据（`id / info / Form`）。
> - `regionPluginConfigForms.tsx` – 各地区表单实现（字段差异）。

### 2. 核心工具

| 工具函数                                 | 作用                                                     |
| ---------------------------------------- | -------------------------------------------------------- |
| `pickProvinceColor(initial)`             | 由**首字母**获取固定色相（12 色循环，Tailwind 600 级）。 |
| `shadeColor(baseHex, step)`              | 同省多插件时在同色相上提亮 _step×8%_ 以区分。            |
| `getIcon(level: 'province\|city\|zone')` | 按“行政层级 → 图标类别”返回 Semi Icon。                  |
| `COLOR_POOL`                             | 12 色固定色相池（Teal / Sky / … / Stone）。              |

所有工具已合并于 [`utils.tsx`](./utils.tsx)。

### 3. 视觉识别规范

| 维度 | 规则                                                              |
| ---- | ----------------------------------------------------------------- |
| 色相 | **首字母固定色相**：`A→Teal…J→Amber…` (26 字母对 12 色取模)。     |
| 亮度 | 同省多插件 → `shadeColor(base, step)` 每级 +8% 亮度区分。         |
| 图标 | `province→IconCloud` / `city→IconMapPinStroked` / `zone→IconFlag` |

代码示例：

```tsx
import {
  pickProvinceColor,
  shadeColor,
  getIcon,
} from "components/region_plugins/utils";

const COLOR = shadeColor(pickProvinceColor("J"), 2); // 江西第 3 个插件
const ICON = getIcon("zone"); // 园区级图标
```

### 4. 新增插件流程（通用）

1. **确定基本信息**
   ```text
   名称: "辽宁-阜新园区"  → 首字母 L
   行政层级: 园区         → zone
   reportType: 5          → 后端约定
   ```
2. **取色 / 图标**
   ```tsx
   color = pickProvinceColor("L"); // Lime #84CC16
   icon = getIcon("zone");
   ```
3. **实现表单** `regionPluginConfigForms.tsx`
4. **注册元数据** `regionPluginMeta.tsx`
5. **页面即自动渲染**（卡片 + 缓存 + 表单切换）。

> ⚠️ 同省已有插件？在第 2 步对 `color` 再调用 `shadeColor(base, step)`，并递增 `step` 即可。

### 5. 版本 / 更新约定

- **version**：功能变更才递增（`v1.1.0` → 字段新增 / 校验调整…）。
- **lastUpdate**：每次 PR 合并即刷新日期。

---

### 插件视觉识别统一规范 (2025-07-11)

> 解决配色/图标不统一、相似度过高的问题，并为后续新增插件提供可复用规则。

#### 1 设计目标

1. **快速辨识**：不同地区插件在 UI 上“一眼识别”，避免颜色、图标过于相似。
2. **统一规则**：新增/修改插件时，只需套用同一套映射公式，无需重新讨论。
3. **可扩展**：预留 12 色基础色 + N 级亮/暗梯度，足以覆盖全国 31 省份及后续园区。

#### 2 图标类别规则

| 行政/场景层级   | 推荐图标 (Semi Icons)                  | 说明                     |
| --------------- | -------------------------------------- | ------------------------ |
| 省级 / 跨市级   | `IconCloud`                            | 覆盖面广，抽象“云朵”符号 |
| 地级市 / 区域   | `IconMapPinStroked` / `IconNavigation` | 表示城市级定位 / 方向    |
| 县级 / 工业园区 | `IconFlag`                             | 旗帜代表具体据点、园区   |

> 若后续有“全国 / 跨省”级别，可新增 `IconGlobe`。若同一层级图标过多，可在同类图标上叠加首字母徽标作为二选方案。

#### 3 颜色映射规则

1. **固定色相池**（Tailwind 600 深度）

```
#14B8A6 Teal   #0284C7 Sky   #4338CA Indigo  #7E22CE Violet
#C026D3 Fuchsia #E11D48 Rose #DC2626 Red     #F97316 Orange
#F59E0B Amber  #84CC16 Lime  #10B981 Emerald #64748B Stone
```

2. **首字母 → 色相**

```
A→Teal  B→Sky  C→Indigo  D→Violet  E→Fuchsia  F→Rose  G→Red
H→Orange  J→Amber  L→Lime  N→Emerald  Q→Stone  其它依次循环
```

- 同一省份 **所有插件共享同色相**，形成“颜色 = 省份”心智；若一个省有多个插件，再通过 `shadeColor(base, step)` 在 **同色相** 上按 `step × 8%` 提升亮度区分，保证既能一眼看出“同省”又能区分不同园区/地市。

示例：`Amber` `#F59E0B` →
• `step 0` 省级色 `#F59E0B`
• `step 1` 地市级 `shadeColor(#F59E0B,1)` → `#FFB436`
• `step 2` 园区级 `shadeColor(#F59E0B,2)` → `#FFC966`
• `step 4` 园区级++ `shadeColor(#F59E0B,4)` → `#FFE1B0`

- 在代码层通过 `pickProvinceColor(initial)` 自动获取颜色（见 `components/region_plugins/utils.tsx`）。

#### 4 现有插件映射示例

| 地区                | 首字母 | 级别 | 图标                | 颜色                 |
| ------------------- | ------ | ---- | ------------------- | -------------------- |
| 云南                | Y      | 省级 | `IconCloud`         | `#14B8A6` Teal       |
| 江西·新干盐化城     | J      | 园区 | `IconFlag`          | `#F7AE32` Amber-L16% |
| 江西·万年凤巢工业区 | J      | 园区 | `IconFlag`          | `#F8BD59` Amber-L32% |
| 辽宁·盘锦           | L      | 市级 | `IconMapPinStroked` | `#84CC16` Lime       |

#### 5 开发落地步骤

1. **确定首字母** → `pickProvinceColor()` 得到固定颜色。
2. **确定层级** → 选取对应图标类别。
3. 在 `regionPluginMeta.tsx` 更新 `info.icon` / `info.color` / `info.lastUpdate`。
4. 若修改为新功能版本，再递增 `info.version`，否则仅更新时间。

#### 6 新增插件工作流

```
➊ 确认名称 → 获取拼音首字母
➋ 调用 pickProvinceColor(initial) 取色
➌ 判断行政层级 → 选 Icon
➍ 在 regionPluginMeta.tsx 追加元数据
➎ 编写/引入表单组件 (regionPluginConfigForms.tsx)
➏ 更新 README 的示例表（可选）
```

> **Tips**
>
> - 同省多个插件：图标可不同以区分园区、地市，颜色保持一致。
> - 若需强调差异，可在卡片背景使用 `opacity` 变化或加二级标识点。

---

#### 7 规则自动化细化说明

下面把两点细化说明——确保「图标类别」与「固定色相」都能按同一套规则自动决定，不再“临时拍脑袋”。

#### ❶ 图标类别自动判定

1. 根据 **行政层级** (省 / 地级市 / 县区&园区) 调用下表即可得推荐图标。
   | 层级 | 返回图标函数 | 图标 (Semi) |
   | ---- | ------------ | ----------- |
   | 省级 | `getIcon('province')` | `IconCloud` |
   | 市级 | `getIcon('city')` | `IconMapPinStroked` |
   | 园区/县级 | `getIcon('zone')` | `IconFlag` |

可在 `utils/icon.ts` 内封装：

```ts
import { IconCloud, IconFlag, IconMapPinStroked } from '@douyinfe/semi-icons'
export const getIcon = (level: 'province' | 'city' | 'zone') => {
  switch (level) {
    case 'province':
      return <IconCloud />
    case 'city':
      return <IconMapPinStroked />
    default:
      return <IconFlag />
  }
}
```

调用示例：

```tsx
icon: getIcon("city");
```

#### ❷ 固定色相自动映射

`components/region_plugins/utils.tsx` 已提供 `pickProvinceColor(initial)`：

```ts
color = pickProvinceColor("J"); // Amber
```

算法：取首字母在 26 字母表的索引，对 12 色池取模(`idx % 12`) → 永久固定。

若同一省需多色，请使用：

```ts
color = shadeColor(pickProvinceColor("J"), 1); // 江西第二个插件，+8% 亮度
```

> 这样“图标 & 颜色”完全可由**名称 + 行政层级**算出，后续插件接入只需：
>
> 1. 传入拼音首字母 (`Y` / `L` …)
> 2. 传入层级 (`province` / `city` / `zone`)
> 3. 其余字段仍手动填写版本、描述等。

## Part Ⅱ – 应用示例

> 以下仅展示集成点，完整代码请在对应业务目录内查看。

| 业务模块                           | 主要文件                                                                                   | 说明                                          |
| ---------------------------------- | ------------------------------------------------------------------------------------------ | --------------------------------------------- |
| HiddenSWSettings (作业票)          | [`regionPluginMeta.tsx`](../../pages/system/content/hiddenSWSettings/regionPluginMeta.tsx) | 首个落地模块，已有云/赣/辽 三省四插件。       |
| HiddenDGSettings (双重预防)        | [`content.tsx`](../../pages/system/content/hiddenDGSettings/content.tsx)                   | 使用同一卡片组件 & 缓存逻辑，仅表单字段不同。 |
| HiddenEquipmentSettings (设备能耗) | [`content.tsx`](../../pages/system/content/hiddenEquipmentSettings/content.tsx)            | 同上；示例插件“安徽安庆高新区”。              |

> 由于 **插件框架完全解耦业务字段**，上述模块只需：
>
> 1. 维护各自的 `regionPluginMeta.tsx` / `regionPluginConfigForms.tsx`
> 2. 引用公用工具 `pickProvinceColor / getIcon / shadeColor`
> 3. 引入统一的 `RegionPluginCard` 或自行定制 UI

### 业务自定义点

| 可自定义项 | 默认实现           | 说明                                   |
| ---------- | ------------------ | -------------------------------------- |
| 卡片 UI    | `RegionPluginCard` | 可根据业务风格替换（宽度 / 选中样式…） |
| 表单校验   | Semi Form `rules`  | 各业务自行配置，框架不做约束           |
| 缓存持久化 | React 状态内存     | 可扩展至 localStorage / IndexedDB 等   |

---

### HiddenSWSettings 插件体系使用文档

> 适用范围：作业票 **隐蔽上报设置**（HiddenSWSettings）模块
>
> 关键源码：
>
> | 角色         | 文件                                                           | 描述                                                       |
> | ------------ | -------------------------------------------------------------- | ---------------------------------------------------------- |
> | 插件清单     | [`regionPluginMeta.tsx`](./regionPluginMeta.tsx)               | _单一数据源_，集中维护所有插件元数据（`id / info / Form`） |
> | 表单组件集合 | [`regionPluginConfigForms.tsx`](./regionPluginConfigForms.tsx) | 各地区上报表单实现（云南 / 江西…）                         |
> | 卡片 UI      | [`RegionPluginCard.tsx`](./RegionPluginCard.tsx)               | 插件选择卡片，仅负责展示                                   |
> | 页面容器     | [`content.tsx`](./content.tsx)                                 | 上报插件配置中心主页面，渲染卡片 + 表单，处理缓存与提交    |
>
> 若需快速查看渲染效果，可在浏览器打开 **系统设置 → 隐蔽作业票设置** 页面。

> 代码中所有颜色 / 图标均引用 [`utils.tsx`](./utils.tsx) 内的 `pickProvinceColor / shadeColor / getIcon`。

---

## 1. 设计概览

1. **单一数据源**  
   所有插件信息统一声明于 `REGION_PLUGIN_META`（只读常量）。页面 UI、缓存初始化、提交数据合并逻辑全部通过遍历该清单自动生成→ **增删插件“一处改动”**。
2. **信息 / 表单分离**  
   `RegionPluginCard` 只负责展示 `info`（名称、颜色、图标…）；真正的业务字段全部写在具体表单组件中。
3. **缓存机制**  
   `content.tsx` 以 `id` 为 key 维护多份 `cachedValues`。切换插件时自动保存 / 恢复，避免表单内容丢失。
4. **动画与体验**  
   使用 `framer-motion` + Tailwind 过渡，保证插件切换平滑、滚动条隐藏但可滚动。

流程示意：

```mermaid
flowchart LR
  REGION_PLUGIN_META-- 遍历 -->Card[RegionPluginCard list]
  REGION_PLUGIN_META-- 遍历 -->Forms[各地区 Form]
  Card-- 点击切换 -->content.tsx
  content.tsx-- setValues/缓存 -->Forms
```

---

## 2. `RegionPluginMeta` 字段说明

| 字段               | 类型        | 作用                                     |
| ------------------ | ----------- | ---------------------------------------- |
| `id`               | `number`    | **reportType**，与后端保持一致，必须唯一 |
| `info.name`        | `string`    | 卡片标题                                 |
| `info.description` | `string`    | 鼠标悬浮/详情描述                        |
| `info.icon`        | `ReactNode` | 卡片左侧图标，推荐 Semi Icons            |
| `info.color`       | `string`    | 主题色（HEX/RGB），用于边框、选中态等    |
| `info.version`     | `string`    | 插件版本号                               |
| `info.lastUpdate`  | `string`    | 最近一次更新日期                         |
| `info.enabled`     | `boolean?`  | 是否启用，控制灰显 / 绿点                |
| `Form`             | `React.FC`  | 该地区专属表单组件                       |

> `REGION_PLUGIN_META` 声明后即 **只读 (`as const`)**，避免运行时被意外改写。

---

## 3. 新增插件步骤

> 以下示例：新增 **“贵州-赤水化工园区”**（reportType = 4，颜色由 `pickProvinceColor('G')` 自动映射，图标由 `getIcon('zone')` 自动选择）。

1. **编写表单组件**  
   在 `regionPluginConfigForms.tsx` 内追加：

   ```tsx
   export const GuizhouChishuiConfigContent: React.FC = () => {
     const formState = useFormState();
     if (formState?.values.reportType !== 4) return null;

     const gutter = 24;
     const rules = [{ required: true, message: "此为必填项" }];

     return (
       <motion.div /* 与现有模板保持一致 */>
         {/* 复制云南/江西模板并按需求调整字段 */}
       </motion.div>
     );
   };
   ```

2. **注册元数据**  
   打开 `regionPluginMeta.tsx`，在数组末尾追加：

   ```tsx
   import { getIcon, pickProvinceColor } from "components/region_plugins/utils";
   import { GuizhouChishuiConfigContent } from "./regionPluginConfigForms";

   export const REGION_PLUGIN_META = [
     /* …已有插件 */
     {
       id: 4,
       info: {
         name: "贵州-赤水化工园区",
         description: "配置贵州赤水化工园区特有的上报参数",
         icon: getIcon("zone"),
         color: pickProvinceColor("G"),
         version: "v1.0.0",
         lastUpdate: "2025-07-10",
         enabled: true,
       },
       Form: GuizhouChishuiConfigContent,
     },
   ] as const;
   ```

3. **完成！**
   - 页面卡片、表单自动出现；缓存键同步生成。
   - 若 API 需识别新的 `reportType`，请同时在后端或接口层对接。

---

## 4. 修改插件步骤

1. **微调表单字段**  
   直接在对应 `*ConfigContent` 组件中增删 `Form.Input` / `RadioGroup` 等字段。
2. **更新版本 / 颜色**  
   在 `regionPluginMeta.tsx` 中修改 `info.version` / `info.color` 等属性即可。
3. **禁用插件**  
   将 `info.enabled` 设为 `false`，卡片将视觉弱化（可按需调整 UI）。

> ⚠️ 变更字段时请同步检查后端接口 & 校验规则，避免提交参数缺失。

---

## 5. 常见问题 FAQ

| 问题            | 解决方案                                                               |
| --------------- | ---------------------------------------------------------------------- |
| 卡片点击无反应  | 确认新插件 `id` 是否唯一且 `Form` 正确导入                             |
| 提交数据缺字段  | 检查 `Form` 内是否 `rules` 必填、名称与后端字段一致                    |
| 颜色/图标不生效 | 样式受 Tailwind 覆盖，确保 `info.color` 为 6 位 HEX，`icon` 为合法 JSX |

---

## 6. 未来优化方向

- **公共 Section 抽取**：当前 3 个表单存在明显重复，可提炼 `BasicSettingsSection` 等组件。
- **更严格的类型**：为各地区表单定义 `interface`，替换 `Record<string, any>`。
- **自动化测试**：E2E 测试确保插件切换、缓存、提交链路完整。

> 如有疑问或发现 bug，请在 PR 中 @前端负责人。

---

### 近期 TODO

- [ ] 抽离公共 `RegionPluginCard` 至本目录
- [ ] 提供 `useRegionPlugins()` hook 简化模块集成
- [ ] Jest 单元测试：色相 / 图标映射稳定性

> 如有疑问或建议，欢迎在 PR / Issue 中讨论。
