import { IconDelete, IconUpload } from "@douyinfe/semi-icons";
import {
  Button,
  Form,
  Image,
  Toast,
  useFormApi,
  useFormState,
} from "@douyinfe/semi-ui";
import { upload_url } from "config";
import { type } from "ramda";
import { FC, useEffect, useRef, useState } from "react";

type EnhancedUploadProps = {
  formField: string;
  field: string;
  label: string;
  multiple?: boolean;
  accept?: string;
  listType?: "list" | "picture";
  singleAsMultiple?: boolean;
  type?: "img" | "file";
  arrayProcessType?: "array" | "string";
  onSuccess?: (res: any) => void;
  disabled?: boolean;
  isRequired?: boolean;
  children?: React.ReactNode;
  maxSize?: number;
  helpTextStyle?: React.CSSProperties;
  showHelpText?: boolean;
};

export const EnhancedUpload: FC<EnhancedUploadProps> = (props) => {
  const formApi = useFormApi();
  const formState = useFormState();
  const [fileList, setFileList] = useState<any[]>([]);
  const instanceId = useRef(
    `enhanced_upload_${props.formField}_${Date.now()}_${Math.random()}`
  );

  console.log(
    `EnhancedUpload instance ${instanceId.current} initialized for field: ${props.formField}`
  );

  // 初始化文件列表 - 响应表单数据变化
  useEffect(() => {
    // 优先检查 _upload 字段（由 filterEditData 创建），然后检查原始字段
    const uploadFieldValue = formApi.getValue(props.field);
    const originalFieldValue = formApi.getValue(props.formField);

    let newFileList = [];

    console.log(`EnhancedUpload instance ${instanceId.current} updating:`, {
      uploadField: props.field,
      uploadFieldValue,
      originalField: props.formField,
      originalFieldValue,
    });

    // 如果存在 _upload 字段数据（编辑模式下由 filterEditData 创建）
    if (
      uploadFieldValue &&
      Array.isArray(uploadFieldValue) &&
      uploadFieldValue.length > 0
    ) {
      newFileList = uploadFieldValue.map((fileItem: any, index: number) => ({
        uid: fileItem.uid || `${instanceId.current}_${index}`,
        name: fileItem.name || `file_${index}`,
        status: fileItem.status || "success",
        url: fileItem.url,
      }));
      console.log(
        `EnhancedUpload instance ${instanceId.current} using upload field data:`,
        newFileList
      );
    }
    // 否则检查原始字段数据（新增模式或运行时数据）
    else if (originalFieldValue) {
      let uris = [];

      if (type(originalFieldValue) == "String") {
        try {
          uris = JSON.parse(originalFieldValue) || [];
        } catch (error) {
          console.log(error);
          uris = [];
        }
      } else if (type(originalFieldValue) == "Array") {
        uris = originalFieldValue || [];
      }

      newFileList = uris.map((uri: string, index: number) => ({
        uid: `${instanceId.current}_${index}`,
        name: uri?.split("/")?.pop() || `file_${index}`,
        status: "success",
        url: uri?.startsWith("http")
          ? uri
          : `${import.meta.env.VITE_BASE || ""}${uri}`,
      }));
      console.log(
        `EnhancedUpload instance ${instanceId.current} using original field data:`,
        newFileList
      );
    }

    // 只有当文件列表真正发生变化时才更新
    if (JSON.stringify(newFileList) !== JSON.stringify(fileList)) {
      setFileList(newFileList);
      console.log(
        `EnhancedUpload instance ${instanceId.current} updated file list:`,
        newFileList
      );
    }
  }, [formState.values?.[props.formField], formState.values?.[props.field]]); // 监听两个字段的变化

  // 监听表单重置
  useEffect(() => {
    const currentFieldValue = formApi.getValue(props.formField);

    // 如果表单字段被清空（重置），清空文件列表
    if (
      !currentFieldValue ||
      (Array.isArray(currentFieldValue) && currentFieldValue.length === 0)
    ) {
      console.log(
        `EnhancedUpload instance ${instanceId.current} detected field reset, clearing file list`
      );
      setFileList([]);
    }
  }, [formState.values?.[props.formField]]);

  const updateFormField = (uris: string[]) => {
    console.log(
      `EnhancedUpload instance ${instanceId.current} updating fields:`,
      {
        formField: props.formField,
        field: props.field,
        uris,
      }
    );

    // 更新实际的表单字段
    if (!props.arrayProcessType || props.arrayProcessType === "string") {
      formApi.setValue(props.formField, JSON.stringify(uris));
      console.log(
        `Updated ${props.formField} with string:`,
        JSON.stringify(uris)
      );
    } else if (props.arrayProcessType === "array") {
      formApi.setValue(props.formField, uris);
      console.log(`Updated ${props.formField} with array:`, uris);
    }

    // 同时更新Form.Upload的字段，确保组件状态同步
    const fileObjects = uris.map((uri, index) => ({
      uid: `${instanceId.current}_${index}`,
      name: `file_${index}`,
      status: "success",
      url: `${import.meta.env.VITE_BASE || ""}${uri}`,
    }));
    formApi.setValue(props.field, fileObjects);
    console.log(`Updated ${props.field} with file objects:`, fileObjects);

    // 验证更新后的值
    setTimeout(() => {
      const formFieldValue = formApi.getValue(props.formField);
      const fieldValue = formApi.getValue(props.field);
      console.log(`Verification - ${props.formField}:`, formFieldValue);
      console.log(`Verification - ${props.field}:`, fieldValue);
    }, 100);
  };

  const handleSuccess = (responseBody: any, file: any) => {
    console.log(
      `EnhancedUpload instance ${instanceId.current} handleSuccess:`,
      responseBody,
      file
    );

    const newUris = responseBody?.data?.uris || [];
    if (newUris.length === 0) return;

    // 获取当前字段值
    const currentFieldValue = formApi.getValue(props.formField);
    let existingUris = [];

    if (type(currentFieldValue) == "String") {
      try {
        existingUris = JSON.parse(currentFieldValue) || [];
      } catch (error) {
        existingUris = [];
      }
    } else if (type(currentFieldValue) == "Array") {
      existingUris = currentFieldValue || [];
    }

    if (props?.multiple === false) {
      existingUris = [];
    }

    const updatedUris = existingUris.concat(newUris);

    // 更新表单字段
    updateFormField(updatedUris);

    // 更新文件列表显示
    const newFileItem = {
      uid: `${instanceId.current}_${Date.now()}_${Math.random()}`,
      name: file.name,
      status: "success",
      url: `${import.meta.env.VITE_BASE || ""}${newUris[0]}`,
    };

    setFileList((prev) => {
      const updated =
        props?.multiple === false ? [newFileItem] : [...prev, newFileItem];
      console.log(
        `EnhancedUpload instance ${instanceId.current} updated file list:`,
        updated
      );
      return updated;
    });

    if (typeof props.onSuccess === "function") {
      props.onSuccess({
        ...file,
        uri: newUris[0],
        uris: updatedUris,
      });
    }
  };

  const handleRemove = (file: any) => {
    console.log(
      `EnhancedUpload instance ${instanceId.current} removing file:`,
      file
    );

    // 从文件列表中移除
    const updatedFileList = fileList.filter((f) => f.uid !== file.uid);
    setFileList(updatedFileList);

    // 更新表单字段
    const updatedUris = updatedFileList.map((f) => {
      try {
        const url = new URL(f.url);
        return url.pathname;
      } catch (error) {
        return f.url;
      }
    });

    updateFormField(updatedUris);
  };

  const renderFileItem = (file: any) => {
    if (props.listType === "picture") {
      return (
        <div
          key={file.uid}
          style={{
            position: "relative",
            display: "inline-block",
            border: "1px solid #d9d9d9",
            borderRadius: "6px",
            overflow: "hidden",
            backgroundColor: "#fafafa",
          }}
        >
          <Image
            src={file.url}
            width={104}
            height={104}
            style={{
              objectFit: "cover",
              display: "block",
            }}
          />
          <div
            style={{
              position: "absolute",
              top: "6px",
              right: "6px",
              background: "rgba(0,0,0,0.6)",
              borderRadius: "50%",
              padding: "4px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <Button
              icon={<IconDelete />}
              type="tertiary"
              theme="borderless"
              size="small"
              style={{
                color: "white",
                width: "20px",
                height: "20px",
                padding: 0,
                minWidth: "20px",
              }}
              onClick={() => handleRemove(file)}
            />
          </div>
        </div>
      );
    } else {
      return (
        <div
          key={file.uid}
          style={{
            display: "flex",
            alignItems: "center",
            padding: "8px 12px",
            border: "1px solid #d9d9d9",
            borderRadius: "6px",
            backgroundColor: "#fafafa",
            marginBottom: "4px",
          }}
        >
          <span
            style={{
              flex: 1,
              fontSize: "14px",
              color: "rgba(0, 0, 0, 0.85)",
            }}
          >
            {file.name}
          </span>
          <Button
            icon={<IconDelete />}
            type="tertiary"
            theme="borderless"
            size="small"
            onClick={() => handleRemove(file)}
          />
        </div>
      );
    }
  };

  const isImg = Boolean((props?.type ?? "img") === "img");

  // 生成帮助文本
  const shouldShowHelpText = props.showHelpText !== false;
  const helpText = shouldShowHelpText
    ? `支持格式: ${props.accept || (isImg ? ".jpg,.png,.gif,.jpeg" : ".*")}${
        props.maxSize ? `, 大小限制: ${Math.round(props.maxSize / 1024)}MB` : ""
      }`
    : "";

  const defaultHelpTextStyle: React.CSSProperties = {
    fontSize: "12px",
    color: "#999",
    marginTop: "4px",
  };

  return (
    <div style={{ marginBottom: "16px" }}>
      {/* 标签 */}
      <div
        style={{
          marginBottom: "12px",
          fontSize: "14px",
          fontWeight: 500,
          color: "rgba(0, 0, 0, 0.85)",
        }}
      >
        {props.isRequired && (
          <span style={{ color: "#ff4d4f", marginRight: "4px" }}>*</span>
        )}
        {props.label}
      </div>

      {/* 内容区域 */}
      <div>
        {/* 显示已上传的文件 */}
        <div
          style={{
            marginBottom: "12px",
            display: fileList.length > 0 ? "flex" : "block",
            flexWrap: "wrap",
            gap: "8px",
            minHeight: fileList.length > 0 ? "auto" : "0",
          }}
        >
          {fileList.map(renderFileItem)}
        </div>

        {/* 上传按钮 */}
        {(!props.multiple || fileList.length === 0 || props.multiple) && (
          <div
            style={{
              marginBottom: "12px",
              paddingTop: "4px",
            }}
          >
            <div
              style={{ position: "relative" }}
              className="enhanced-upload-container"
            >
              <Form.Upload
                field={props.field}
                action={upload_url}
                headers={{
                  Authorization: `Bearer ${localStorage.getItem("token")}`,
                }}
                accept={isImg ? "image/*" : props.accept}
                maxSize={props.maxSize}
                onSizeError={(file: any) =>
                  Toast.error(`${file.name} 大小超过限制`)
                }
                onSuccess={handleSuccess}
                data={{
                  type: isImg ? 1 : 2,
                }}
                showUploadList={false} // 不显示内置的文件列表
                multiple={props.multiple}
                disabled={props.disabled}
              >
                {props.children || (
                  <Button
                    icon={<IconUpload />}
                    disabled={props.disabled}
                    type="primary"
                    theme="light"
                    style={{
                      height: "36px",
                      padding: "0 16px",
                      fontSize: "14px",
                    }}
                  >
                    {isImg ? "上传图片" : "上传文件"}
                  </Button>
                )}
              </Form.Upload>
              {/* 添加CSS来隐藏Form.Upload的自动标签 */}
              <style>{`
                .enhanced-upload-container .semi-form-field-label {
                  display: none !important;
                }
                .enhanced-upload-container .semi-form-field-main {
                  margin-top: 0 !important;
                }
              `}</style>
            </div>
          </div>
        )}

        {/* 帮助文本 */}
        {shouldShowHelpText && helpText && (
          <div style={{ ...defaultHelpTextStyle, ...props.helpTextStyle }}>
            {helpText}
          </div>
        )}
      </div>
    </div>
  );
};
