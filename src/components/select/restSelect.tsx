import { Form } from "@douyinfe/semi-ui";
import { FC } from "react";
import { v4 as uuidv4 } from "uuid";
import type { FieldList } from "../enum";

export type RestSelectProps = {
  field: string;
  placeholder?: string;
  label?: string;
  isRequired?: boolean;
  options: FieldList;
  disabled?: boolean;
  multiple?: boolean;
};

export const RestSelect: FC<RestSelectProps> = ({
  field,
  placeholder,
  label,
  isRequired,
  options,
  disabled = false,
  multiple = false,
}) => {
  return (
    <Form.Select
      field={field}
      noLabel={!Boolean(label)}
      label={label ?? ""}
      placeholder={placeholder ?? ""}
      disabled={disabled}
      className="w-full"
      rules={[{ required: isRequired, message: "此为必填项!" }]}
      multiple={multiple}
    >
      {options?.map((o) => (
        <Form.Select.Option value={parseInt(o?.id) ?? 0} key={uuidv4()}>
          {o?.name ?? ""}
          {o?.label ? (
            <span className="text-sm text-slate-400 ml-1">{o?.label}</span>
          ) : null}
        </Form.Select.Option>
      ))}
    </Form.Select>
  );
};
