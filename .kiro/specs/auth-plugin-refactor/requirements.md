# 需求文档

## 介绍

此功能涉及重构现有的hiddenAuth组件，使其使用类似于hiddenSWSettings组件的插件化架构。当前实现基于loginType值使用条件渲染，但我们希望将其抽象为更易维护的插件系统，以便更容易添加新的认证提供商并更好地分离关注点。

## 需求

### 需求 1

**用户故事：** 作为开发者，我希望认证配置使用插件化架构，以便添加新的认证提供商更容易，代码更易维护。

#### 验收标准

1. 当hiddenAuth组件加载时，系统应该在类似hiddenSWSettings的水平滚动界面中显示认证插件
2. 当用户选择认证插件时，系统应该显示该插件特定的配置表单
3. 当在插件之间切换时，系统应该使用缓存为每个插件保留表单值
4. 当提交表单时，系统应该合并所有插件配置并提交到API

### 需求 2

**用户故事：** 作为系统管理员，我希望通过统一界面配置不同的认证提供商，以便高效管理认证设置。

#### 验收标准

1. 当系统加载时，应该显示可用的认证插件（系统内置、钉钉）
2. 当我选择系统内置插件时，系统应该显示最少的配置选项
3. 当我选择钉钉插件时，系统应该显示钉钉特定的配置字段
4. 当我配置第三方认证时，系统应该显示额外的同步配置选项

### 需求 3

**用户故事：** 作为开发者，我希望每个认证插件都是自包含的，以便代码模块化且更易维护。

#### 验收标准

1. 当创建认证插件时，每个插件应该有自己的元数据，包括名称、描述、图标和颜色
2. 当实现插件表单时，每个插件应该有自己的React组件用于配置
3. 当添加新的认证提供商时，开发者只需要向插件元数据数组添加条目
4. 当渲染插件时，它们应该遵循一致的接口模式

### 需求 4

**用户故事：** 作为用户，我希望认证配置界面在视觉上一致且直观，以便我能够轻松理解和配置认证设置。

#### 验收标准

1. 当查看认证插件时，它们应该显示为带有图标和颜色的水平滚动芯片
2. 当选择插件时，应该用适当的样式进行视觉高亮显示
3. 当在插件之间切换时，应该有平滑的动画和过渡效果
4. 当界面加载时，应该显示适当的加载状态和错误处理
