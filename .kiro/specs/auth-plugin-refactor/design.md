# 设计文档

## 概述

本设计将hiddenAuth组件重构为插件化架构，参考hiddenSWSettings的成功模式。新架构将认证提供商抽象为独立的插件，每个插件包含自己的元数据、配置表单和业务逻辑。这种设计提高了代码的可维护性、可扩展性和模块化程度。

## 架构

### 插件化架构模式

```
HiddenAuthContent (主容器)
├── AuthPluginMeta (插件元数据定义)
├── Plugin Selection UI (插件选择界面)
├── Form Caching System (表单缓存系统)
├── Plugin Form Renderer (插件表单渲染器)
└── Unified Submit Handler (统一提交处理器)
```

### 核心组件关系

1. **AuthPluginMeta**: 定义所有认证插件的元数据和表单组件
2. **HiddenAuthContent**: 主容器组件，管理插件切换和表单状态
3. **Individual Plugin Forms**: 每个认证提供商的独立表单组件
4. **Caching Layer**: 在插件切换时保持表单数据的缓存层

## 组件和接口

### 1. 插件元数据接口

```typescript
interface AuthPluginInfo {
  readonly name: string;
  readonly description: string;
  readonly icon: React.ReactNode;
  readonly color: string;
  readonly version: string;
  readonly lastUpdate: string;
  readonly enabled?: boolean;
}

interface AuthPluginMeta {
  readonly id: number;
  readonly info: AuthPluginInfo;
  readonly Form: React.FC;
}
```

### 2. 插件元数据定义

```typescript
import {
  getIcon,
  pickAuthProviderColor,
} from "components/region_plugins/utils";

export const AUTH_PLUGIN_META: ReadonlyArray<AuthPluginMeta> = [
  {
    id: 1, // 系统内置
    info: {
      name: "系统内置",
      description: "使用系统内置的用户认证方式",
      icon: getIcon("system"),
      color: pickAuthProviderColor("S"),
      version: "v1.0.0",
      lastUpdate: "2025-01-29",
      enabled: true,
    },
    Form: SystemBuiltinAuthForm,
  },
  {
    id: 2, // 钉钉
    info: {
      name: "钉钉认证",
      description: "使用钉钉企业认证登录",
      icon: getIcon("dingtalk"),
      color: pickAuthProviderColor("D"),
      version: "v1.2.0",
      lastUpdate: "2025-01-29",
      enabled: true,
    },
    Form: DingTalkAuthForm,
  },
];
```

### 3. 主容器组件结构

```typescript
export const HiddenAuthContent = ({ readonly = false, ...restProps }) => {
  // 状态管理
  const [currentLoginType, setCurrentLoginType] = useState<number>(1);
  const [cachedValues, setCachedValues] = useState<Record<number, FormValues>>({});
  const [isFormVisible, setIsFormVisible] = useState(false);

  // 插件切换处理
  const handlePluginSelect = (pluginId: number) => {
    // 保存当前表单值
    // 切换到新插件
    // 恢复缓存的表单值
  };

  // 统一提交处理
  const handleSubmit = (values: FormValues) => {
    // 合并所有插件的缓存值
    // 提交到API
  };

  return (
    // 插件选择UI + 表单渲染区域
  );
};
```

### 4. 独立插件表单组件

#### 系统内置认证表单

```typescript
const SystemBuiltinAuthForm: React.FC = () => {
  return (
    <Form.Section text="系统内置认证配置">
      <Row gutter={24}>
        <Col span={24}>
          <div className="text-gray-500 text-sm p-4 bg-gray-50 rounded">
            系统内置认证无需额外配置，用户将使用系统内部的用户名密码进行登录。
          </div>
        </Col>
      </Row>
    </Form.Section>
  );
};
```

#### 钉钉认证表单

```typescript
const DingTalkAuthForm: React.FC = () => {
  const rules = [{ required: true, message: "此为必填项" }];

  return (
    <>
      <Form.Section text="钉钉应用配置">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Input label="钉钉应用agentId" field="dingtalkAgentId" rules={rules} />
          </Col>
          <Col span={12}>
            <Form.Input label="钉钉应用AppKey" field="dingtalkAppKey" rules={rules} />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input label="钉钉应用AppSecret" field="dingtalkAppSecret" rules={rules} />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input label="钉钉网页端回调地址" field="dingtalkRedirectUri" rules={rules} />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Input label="钉钉App端回调地址" field="dingtalkRedirectMobileUri" rules={rules} />
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Input label="钉钉移动网页端回调地址" field="dingtalkRedirectWapUri" rules={rules} />
          </Col>
        </Row>
      </Form.Section>

      <Form.Section text="第三方认证信息同步配置">
        <Row gutter={24}>
          <Col span={12}>
            <Form.RadioGroup label="离职同步是否打开" field="statusIsOn" rules={rules}>
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>{item.name}</Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
          <Col span={12}>
            <Form.RadioGroup label="账户同步是否打开" field="copyIsOn" rules={rules}>
              {ISON_TYPE_MAP.map((item) => (
                <Form.Radio key={item.id} value={item.id}>{item.name}</Form.Radio>
              ))}
            </Form.RadioGroup>
          </Col>
        </Row>
      </Form.Section>
    </>
  );
};
```

## 数据模型

### 表单数据结构

```typescript
interface AuthFormValues {
  loginType: number;

  // 钉钉相关字段
  dingtalkAgentId?: string;
  dingtalkAppKey?: string;
  dingtalkAppSecret?: string;
  dingtalkRedirectUri?: string;
  dingtalkRedirectMobileUri?: string;
  dingtalkRedirectWapUri?: string;

  // 第三方同步配置
  statusIsOn?: number;
  copyIsOn?: number;
}
```

### 缓存数据结构

```typescript
type FormValues = Record<string, any>;
type CachedValues = Record<number, FormValues>; // pluginId -> formValues
```

## 错误处理

### 1. 表单验证错误

- 在插件切换前验证当前表单
- 显示具体的字段错误信息
- 自动滚动到错误字段位置

### 2. API请求错误

- 显示友好的错误提示
- 保持表单数据不丢失
- 提供重试机制

### 3. 插件加载错误

- 优雅降级到基础功能
- 显示插件不可用状态
- 记录错误日志

## 测试策略

### 1. 单元测试

- 插件元数据配置测试
- 表单组件渲染测试
- 缓存机制测试
- 表单验证测试

### 2. 集成测试

- 插件切换流程测试
- 表单提交流程测试
- API交互测试
- 错误处理测试

### 3. 用户界面测试

- 插件选择交互测试
- 表单填写和验证测试
- 响应式布局测试
- 动画和过渡效果测试

## 实现细节

### 1. 文件结构

```
src/pages/system/content/hiddenAuth/
├── content.tsx (主组件)
├── authPluginMeta.tsx (插件元数据)
├── components/
│   ├── SystemBuiltinAuthForm.tsx
│   └── DingTalkAuthForm.tsx (包含同步配置)
└── types.ts (类型定义)
```

### 2. 样式和动画

- 使用与hiddenSWSettings相同的样式模式
- 实现平滑的插件切换动画
- 保持视觉一致性和用户体验
- 复用region_plugins/utils中的getIcon和颜色选择函数

### 3. 性能优化

- 懒加载插件表单组件
- 优化表单缓存机制
- 减少不必要的重新渲染

### 4. 可扩展性考虑

- 插件接口标准化
- 支持动态插件注册
- 预留扩展点用于未来功能
