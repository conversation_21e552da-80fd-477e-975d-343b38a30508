# 实施计划

- [x] 1. 设置路由常量和基础结构

  - 在 utils/routerConstants.ts 中定义蓝凌OA SSO回调路由常量
  - 将回调路由添加到不需要登录的路由列表中
  - _需求: 1.1, 5.4_

- [x] 2. 创建SSO工具函数

  - 创建 utils/ssoUtils.ts 文件
  - 实现UUID提取函数 extractUUIDFromURL
  - 实现UUID验证函数 validateUUID
  - 编写单元测试验证工具函数的正确性
  - _需求: 2.1, 4.1_

- [x] 3. 实现蓝凌OA登录API调用

  - 在 api 目录中创建 sso.ts 文件
  - 实现 postLandrayLogin 函数调用 /v1/system/lanling_login 接口
  - 定义 TypeScript 接口类型 LandrayLoginRequest 和 LandrayLoginResponse
  - 确保API调用遵循现有的请求格式和错误处理模式
  - _需求: 2.2, 2.3, 4.2_

- [x] 4. 创建SSO回调页面组件

  - 创建 src/pages/login/landrayOASSOCallback.tsx 组件
  - 实现URL参数解析和UUID提取逻辑
  - 集成API调用和认证状态管理
  - 实现加载状态显示（参考钉钉登录页面的Spin组件）
  - _需求: 2.1, 2.4, 2.5_

- [x] 5. 集成认证状态管理

  - 在SSO回调组件中集成现有的useAuth和useLocalStorage hooks
  - 实现成功认证后的用户数据存储（username, token, userInfo, refreshToken, expireTime）
  - 确保认证流程与普通登录保持一致
  - 实现认证成功后的页面重定向
  - _需求: 2.4, 2.5, 4.3_

- [x] 6. 实现错误处理机制

  - 在SSO回调组件中实现各种错误场景的处理
  - 处理缺失UUID的情况（显示"无效的SSO请求"错误）
  - 处理API错误响应（显示后端返回的错误消息）
  - 处理网络错误（显示通用网络错误消息）
  - 提供返回登录页面的选项
  - _需求: 3.1, 3.2, 3.3, 3.4_

- [x] 7. 添加路由配置

  - 在 App.tsx 中添加蓝凌OA SSO回调路由
  - 确保路由配置正确且不需要认证即可访问
  - 测试路由是否正确响应回调请求
  - _需求: 5.4_

- [x] 8. 增强配置表单显示回调URL

  - 修改 src/pages/system/content/hiddenAuth/components/LandrayOAAuthForm.tsx
  - 添加只读的回调URL显示字段
  - 实现一键复制回调URL功能
  - 添加帮助文本指导管理员在蓝凌OA中配置此地址
  - _需求: 1.1, 5.1, 5.2, 5.3_

- [x] 9. 更新登录页面导出

  - 在 src/pages/login/index.tsx 中导出新的SSO回调组件
  - 确保组件可以被正确导入和使用
  - _需求: 4.1_

- [ ] 10. 编写集成测试

  - 测试完整的SSO流程：从回调URL到成功登录
  - 测试各种错误场景的处理
  - 测试UUID提取和验证逻辑
  - 测试API调用和认证状态管理
  - _需求: 4.4_

- [ ] 11. 测试和验证
  - 使用模拟的UUID参数测试回调URL
  - 验证成功认证后的用户状态和页面重定向
  - 测试错误场景的用户体验
  - 验证配置表单中回调URL的显示和复制功能
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 3.1, 3.2, 3.3, 3.4, 5.1, 5.2, 5.3_
