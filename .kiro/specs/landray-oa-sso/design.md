# 设计文档

## 概述

蓝凌OA单点登录功能将集成到现有的认证系统中，提供无缝的用户体验。该功能包括配置管理、回调URL处理、UUID参数提取、后端API调用和错误处理。设计遵循现有的认证模式，重用现有的状态管理和路由结构。

## 架构

### 高层架构

```mermaid
graph TB
    A[用户在蓝凌OA系统] -->|认证成功后重定向| B[SSO回调页面]
    B -->|提取UUID| C[UUID验证器]
    C -->|调用登录API| D[后端认证服务]
    D -->|返回认证结果| E[认证处理器]
    E -->|成功| F[主应用界面]
    E -->|失败| G[错误处理页面]

    H[管理员配置] -->|保存设置| I[配置存储]
    I -->|提供回调URL| B
```

### 组件架构

```mermaid
graph TB
    A[LandrayOAAuthForm] -->|配置管理| B[配置存储]
    C[蓝凌OA系统] -->|回调| D[SSO回调路由]
    D -->|URL解析| E[UUID提取器]
    E -->|API调用| F[认证服务]
    F -->|状态管理| G[Auth Provider]
    G -->|重定向| H[应用主界面]

    I[错误处理器] -->|显示错误| J[错误页面/模态框]
    F -->|失败时| I
```

## 组件和接口

### 1. 配置组件增强

**LandrayOAAuthForm.tsx** 需要显示固定的回调URL路径：

```typescript
interface LandrayOAConfig {
  appId: string;
  token: string;
  baseUrl: string;
  queryUri: string;
  queryTimeout: number;
  maxLevel: number;
  statusIsOn: boolean;
  copyIsOn: boolean;
  departmentIsOn: boolean;
  // 不需要存储callbackUrl，因为路径是固定的
}
```

### 2. SSO回调处理器

**LandrayOASSOCallback.tsx** - 新组件处理SSO回调：

```typescript
interface SSOCallbackProps {
  onSuccess?: (user: any) => void;
  onError?: (error: string) => void;
}

interface SSOCallbackState {
  loading: boolean;
  error: string | null;
  uuid: string | null;
}
```

### 3. UUID提取工具

**utils/ssoUtils.ts** - 工具函数：

```typescript
interface URLParams {
  uuid?: string;
}

export const extractUUIDFromURL = (url: string): string | null;
export const validateUUID = (uuid: string): boolean;
export const buildCallbackURL = (baseUrl: string): string;
```

### 4. 认证API集成

**api/sso.ts** - SSO相关API调用：

```typescript
interface LandrayLoginRequest {
  uuid: string;
}

interface LandrayLoginResponse {
  code: number;
  message: string;
  data: {
    authToken: string;
    expireTime: string;
    refreshToken: string;
    employee: {
      id: number;
      name: string;
      employeeId: string;
    };
  };
}

export const postLandrayLogin = (request: LandrayLoginRequest): Promise<LandrayLoginResponse>;
```

### 5. 路由配置

在 **utils/routerConstants.ts** 中定义路由常量：

```typescript
export const LandrayOASSOCallbackRoute = "/auth/landray/callback";

// 添加到不需要登录的路由列表
export const notNeedLoginRoutes = [
  // ... 现有路由
  LandrayOASSOCallbackRoute,
];
```

在 **App.tsx** 中添加SSO回调路由：

```typescript
{
  path: LandrayOASSOCallbackRoute,
  element: <LandrayOASSOCallback />,
  name: "蓝凌OA单点登录回调",
}
```

## 数据模型

### 1. 配置数据模型

```typescript
interface LandrayOAConfiguration {
  // 现有字段
  appId: string;
  token: string;
  baseUrl: string;
  queryUri: string;
  queryTimeout: number;
  maxLevel: number;
  statusIsOn: boolean;
  copyIsOn: boolean;
  departmentIsOn: boolean;

  // 不需要存储callbackUrl，路径在 routerConstants.ts 中定义
}
```

### 2. SSO状态模型

```typescript
interface SSOState {
  isProcessing: boolean;
  error: string | null;
  uuid: string | null;
  redirectUrl: string | null;
}
```

### 3. 认证响应模型

重用现有的认证响应模型，确保与普通登录保持一致：

```typescript
interface AuthenticationResult {
  authToken: string;
  expireTime: string;
  refreshToken: string;
  employee: {
    id: number;
    name: string;
    employeeId: string;
  };
}
```

## 错误处理

### 1. 错误类型定义

```typescript
enum SSOErrorType {
  MISSING_UUID = "MISSING_UUID",
  INVALID_UUID = "INVALID_UUID",
  API_ERROR = "API_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
  CONFIGURATION_ERROR = "CONFIGURATION_ERROR",
}

interface SSOError {
  type: SSOErrorType;
  message: string;
  details?: any;
}
```

### 2. 错误处理策略

- **缺失UUID**: 显示"无效的SSO请求"错误，提供返回登录页面链接
- **API错误**: 显示后端返回的具体错误消息
- **网络错误**: 显示通用网络错误消息，提供重试选项
- **配置错误**: 显示配置相关错误，引导管理员检查设置

### 3. 错误显示方式

支持两种错误显示方式：

- **错误页面**: 全屏错误页面，适用于严重错误
- **模态对话框**: 轻量级错误提示，适用于可恢复错误

## 测试策略

### 1. 单元测试

- **UUID提取函数测试**: 测试各种URL格式的UUID提取
- **配置验证测试**: 测试配置字段的验证逻辑
- **错误处理测试**: 测试各种错误场景的处理
- **API调用测试**: 模拟API响应测试认证流程

### 2. 集成测试

- **完整SSO流程测试**: 从回调URL到成功登录的完整流程
- **错误场景测试**: 测试各种失败场景的处理
- **配置管理测试**: 测试配置的保存和加载

### 3. 端到端测试

- **真实环境测试**: 与蓝凌OA系统的实际集成测试
- **用户体验测试**: 测试用户从蓝凌OA到系统的完整体验
- **错误恢复测试**: 测试错误情况下的用户恢复路径

### 4. 测试数据

```typescript
import { LandrayOASSOCallbackRoute } from "utils/routerConstants";

// 测试用例数据
const testCases = {
  validUUID: "550e8400-e29b-41d4-a716-************",
  invalidUUID: "invalid-uuid",
  validCallbackURL: `https://example.com${LandrayOASSOCallbackRoute}?uuid=550e8400-e29b-41d4-a716-************`,
  invalidCallbackURL: `https://example.com${LandrayOASSOCallbackRoute}`,
  apiSuccessResponse: {
    code: 0,
    message: "success",
    data: {
      authToken: "token123",
      expireTime: "2024-12-31T23:59:59Z",
      refreshToken: "refresh123",
      employee: {
        id: 1,
        name: "张三",
        employeeId: "EMP001",
      },
    },
  },
  apiErrorResponse: {
    code: 1001,
    message: "用户不存在",
    data: null,
  },
};
```

## 实现细节

### 1. 配置表单增强

在现有的 `LandrayOAAuthForm.tsx` 中添加蓝凌OA登录地址和回调URL字段：

```typescript
import { LandrayOASSOCallbackRoute } from 'utils/routerConstants';

// 添加回调URL显示字段
<Form.Input
  label="回调地址"
  field="callbackUrl"
  value={`${window.location.origin}${LandrayOASSOCallbackRoute}`}
  disabled={true}
  helpText="请将此地址配置到蓝凌OA系统中"
  suffix={
    <Button
      theme="borderless"
      icon={<IconCopy />}
      onClick={() => {
        navigator.clipboard.writeText(`${window.location.origin}${LandrayOASSOCallbackRoute}`);
        Toast.success('回调地址已复制到剪贴板');
      }}
    />
  }
/>
```

### 2. 路由集成

在现有路由结构中添加SSO回调路由，确保不需要认证即可访问：

```typescript
// 在 utils/routerConstants.ts 中添加
export const LandrayOASSOCallbackRoute = "/auth/landray/callback";

// 在 notNeedLoginRoutes 中添加
export const notNeedLoginRoutes = [
  // ... 现有路由
  LandrayOASSOCallbackRoute,
];
```

### 3. 状态管理集成

重用现有的认证状态管理，确保SSO登录后的状态与普通登录一致：

```typescript
// 在 SSO 回调组件中
const { login, setToken } = useAuth();
const { setItemWithKey } = useLocalStorage();

// 成功认证后
setItemWithKey(userInfoNameInLocalStorage, {
  username: response.data.employee.name,
  token: response.data.authToken,
  userInfo: response.data.employee,
  refreshToken: response.data.refreshToken,
  expireTime: response.data.expireTime,
});

login(response.data);
setToken(response.data.authToken);
```

### 4. 错误处理集成

使用现有的错误处理模式，确保一致的用户体验：

```typescript
// 使用现有的 Toast 组件显示错误
Toast.error({
  content: error.message,
  duration: 5,
});

// 使用现有的错误页面组件
<NotFound
  statusCode={401}
  message="SSO认证失败"
  autoRedirect={false}
  showBackToLogin={true}
/>
```
