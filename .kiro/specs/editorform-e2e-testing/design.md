# EditorForm E2E测试方案技术设计

## 🏗️ 架构概览

### 整体架构

```
E2E测试架构
├── 测试框架层 (Playwright + TypeScript)
├── 页面对象层 (Page Objects)
├── 测试数据层 (Fixtures + Factories)
├── 测试用例层 (Test Specs)
├── 工具支持层 (Utilities + Helpers)
└── 报告输出层 (Reports + Screenshots)
```

### 技术栈选择

- **测试框架**: Playwright (跨浏览器、稳定性好、TypeScript支持)
- **编程语言**: TypeScript (类型安全、IDE支持好)
- **测试模式**: Page Object Pattern (提高可维护性)
- **数据管理**: Factory Pattern (灵活的测试数据生成)
- **报告工具**: Playwright内置报告 + Allure (可视化报告)

## 📁 项目结构设计

```
e2e/
├── config/
│   ├── playwright.config.ts          # Playwright配置
│   ├── test-data.ts                  # 测试数据配置
│   └── environments.ts               # 环境配置
├── pages/
│   ├── FormEditorPage.ts             # 表单编辑器页面对象
│   ├── ComponentLibraryPage.ts       # 组件库页面对象
│   ├── PropertyPanelPage.ts          # 属性面板页面对象
│   └── PreviewPage.ts                # 预览页面对象
├── fixtures/
│   ├── formTemplates.ts              # 表单模板数据
│   ├── componentData.ts              # 组件测试数据
│   └── userScenarios.ts              # 用户场景数据
├── tests/
│   ├── basic/                        # 基础功能测试
│   ├── components/                   # 组件操作测试
│   ├── properties/                   # 属性配置测试
│   ├── data-flow/                    # 数据流测试
│   ├── save-load/                    # 保存加载测试
│   └── preview-export/               # 预览导出测试
├── utils/
│   ├── test-helpers.ts               # 测试辅助函数
│   ├── selectors.ts                  # 选择器常量
│   └── assertions.ts                 # 自定义断言
└── reports/                          # 测试报告输出
```

### 🎯 为什么E2E测试独立成一级目录？

根据项目实际情况和最佳实践，**E2E测试建议放在项目根目录的独立 `e2e/` 目录下**，而不是放在 `src/` 或 `src/test/` 目录中。这个设计有以下重要考虑：

1. **测试性质不同**

   - **单元测试** (`src/test/`): 测试单个组件/函数的内部逻辑
   - **E2E测试** (`e2e/`): 测试完整的用户工作流程，跨越多个组件和页面

2. **技术栈差异**

   - **单元测试**: 使用 Vitest + React Testing Library + jsdom
   - **E2E测试**: 使用 Playwright + 真实浏览器环境

3. **执行环境不同**

   - **单元测试**: 在Node.js环境中模拟DOM
   - **E2E测试**: 需要启动完整的应用服务器和真实浏览器

4. **配置文件分离**
   - **单元测试**: `vitest.config.ts`
   - **E2E测试**: `playwright.config.ts`

#### 📊 当前项目结构对比

```
当前项目结构:
├── src/test/           # 单元测试 (Vitest)
│   ├── factories/
│   ├── utils/
│   └── setup.ts
├── docs/testing/       # 测试文档
└── vitest.config.ts    # 单元测试配置

建议的E2E结构:
├── e2e/               # E2E测试 (Playwright) - 新增
│   ├── config/
│   ├── pages/
│   ├── fixtures/
│   ├── tests/
│   ├── utils/
│   └── reports/
├── playwright.config.ts  # E2E测试配置 - 新增
└── src/test/          # 保持现有单元测试结构
```

#### 🔧 推荐的目录组织策略

```
项目根目录/
├── e2e/                          # E2E测试独立目录
│   ├── config/
│   │   ├── playwright.config.ts  # Playwright配置
│   │   ├── test-data.ts          # 测试数据配置
│   │   └── environments.ts       # 环境配置
│   ├── pages/                    # Page Object模式
│   │   ├── FormEditorPage.ts
│   │   ├── ComponentLibraryPage.ts
│   │   └── PropertyPanelPage.ts
│   ├── fixtures/                 # 测试数据
│   ├── tests/                    # 测试用例
│   │   ├── basic/
│   │   ├── components/
│   │   └── integration/
│   ├── utils/                    # E2E测试工具
│   └── reports/                  # 测试报告
├── src/test/                     # 保持现有单元测试
└── docs/testing/                 # 测试文档
```

#### ✅ 这种结构的优势

1. **职责清晰**: 单元测试和E2E测试完全分离
2. **配置独立**: 各自有独立的配置文件和依赖
3. **执行分离**: 可以独立运行不同类型的测试
4. **维护简单**: 不同测试类型的代码不会相互干扰
5. **CI/CD友好**: 可以在CI中分别配置不同的测试阶段

#### 🚀 实施建议

基于spec文档的设计，建议按以下步骤实施：

1. **第一阶段**: 在项目根目录创建 `e2e/` 目录
2. **第二阶段**: 安装Playwright并配置基础架构
3. **第三阶段**: 实现Page Object模式和测试用例
4. **第四阶段**: 集成到CI/CD流程

这种结构设计符合现代前端项目的最佳实践，确保E2E测试与单元测试的清晰分离和独立管理。

## 🎭 Page Object设计

### FormEditorPage类设计

```typescript
export class FormEditorPage {
  // 页面元素定位器
  private readonly componentLibrary: Locator;
  private readonly canvasArea: Locator;
  private readonly propertyPanel: Locator;
  private readonly toolbar: Locator;

  // 基础操作方法
  async goto(): Promise<void>;
  async waitForLoad(): Promise<void>;

  // 组件操作方法
  async dragComponentToCanvas(componentType: string): Promise<void>;
  async selectComponent(componentId: string): Promise<void>;
  async deleteComponent(componentId: string): Promise<void>;

  // 属性配置方法
  async setComponentProperty(property: string, value: any): Promise<void>;
  async getComponentProperty(property: string): Promise<string>;

  // 保存加载方法
  async saveConfiguration(name: string): Promise<void>;
  async loadConfiguration(name: string): Promise<void>;

  // 预览导出方法
  async openPreview(): Promise<void>;
  async exportConfiguration(): Promise<object>;
}
```

### 组件选择器策略

```typescript
// 使用data-testid确保选择器稳定性
export const SELECTORS = {
  // 主要区域
  FORM_EDITOR: '[data-testid="form-editor"]',
  COMPONENT_LIBRARY: '[data-testid="component-library"]',
  CANVAS_AREA: '[data-testid="canvas-area"]',
  PROPERTY_PANEL: '[data-testid="property-panel"]',

  // 组件类型
  COMPONENT_INPUT: '[data-testid="component-input"]',
  COMPONENT_SELECT: '[data-testid="component-select"]',
  COMPONENT_TEXTAREA: '[data-testid="component-textarea"]',

  // 操作按钮
  SAVE_BTN: '[data-testid="save-btn"]',
  LOAD_BTN: '[data-testid="load-btn"]',
  PREVIEW_BTN: '[data-testid="preview-btn"]',
  EXPORT_BTN: '[data-testid="export-btn"]',
} as const;
```

## 🧪 测试数据设计

### 表单模板工厂

```typescript
export class FormTemplateFactory {
  static createUserInfoForm(): FormTemplate {
    return {
      title: "用户信息表",
      components: [
        { type: "input", props: { label: "姓名", required: true } },
        { type: "email", props: { label: "邮箱" } },
        {
          type: "select",
          props: { label: "部门", options: ["技术部", "产品部"] },
        },
      ],
    };
  }

  static createFeedbackForm(): FormTemplate {
    return {
      title: "反馈表单",
      components: [
        {
          type: "radio",
          props: { label: "满意度", options: ["满意", "一般", "不满意"] },
        },
        { type: "textarea", props: { label: "建议", rows: 4 } },
      ],
    };
  }
}
```

### 测试场景数据

```typescript
export const TEST_SCENARIOS = {
  BASIC_FORM_CREATION: {
    name: "基础表单创建",
    steps: [
      { action: "dragComponent", component: "input" },
      { action: "setProperty", property: "label", value: "用户名" },
      { action: "setProperty", property: "required", value: true },
    ],
  },

  COMPLEX_FORM_WORKFLOW: {
    name: "复杂表单工作流",
    steps: [
      { action: "createMultipleComponents" },
      { action: "configureProperties" },
      { action: "arrangeLayout" },
      { action: "saveConfiguration" },
      { action: "previewForm" },
    ],
  },
} as const;
```

## 🔧 核心功能实现

### 拖拽操作实现

```typescript
async dragComponentToCanvas(componentType: string, position?: { x: number, y: number }) {
  const component = this.page.locator(`[data-testid="component-${componentType}"]`);
  const canvas = this.page.locator('[data-testid="canvas-area"]');

  // 等待元素可见
  await component.waitFor({ state: 'visible' });
  await canvas.waitFor({ state: 'visible' });

  // 执行拖拽操作
  if (position) {
    await component.dragTo(canvas, { targetPosition: position });
  } else {
    await component.dragTo(canvas);
  }

  // 验证组件已添加
  await this.page.waitForSelector(`[data-component-type="${componentType}"]`);
}
```

### 属性配置实现

```typescript
async setComponentProperty(property: string, value: any) {
  const propertyInput = this.page.locator(`[data-testid="prop-${property}"]`);

  // 根据属性类型选择不同的操作方式
  const inputType = await propertyInput.getAttribute('type');

  switch (inputType) {
    case 'checkbox':
      if (value) await propertyInput.check();
      else await propertyInput.uncheck();
      break;
    case 'select':
      await propertyInput.selectOption(value);
      break;
    default:
      await propertyInput.fill(value.toString());
  }

  // 等待属性更新生效
  await this.page.waitForTimeout(100);
}
```

## 📊 测试执行策略

### 并行执行配置

```typescript
// playwright.config.ts
export default defineConfig({
  testDir: "./e2e/tests",
  fullyParallel: true,
  workers: process.env.CI ? 2 : 4,
  retries: process.env.CI ? 2 : 0,

  use: {
    baseURL: "http://localhost:3000",
    trace: "on-first-retry",
    screenshot: "only-on-failure",
    video: "retain-on-failure",
  },

  projects: [
    { name: "chromium", use: { ...devices["Desktop Chrome"] } },
    { name: "firefox", use: { ...devices["Desktop Firefox"] } },
    { name: "webkit", use: { ...devices["Desktop Safari"] } },
  ],
});
```

### 测试分组策略

```typescript
// 按功能模块分组
describe("EditorForm - 基础功能", () => {
  test("页面加载和初始化", async ({ page }) => {
    /* ... */
  });
  test("组件库显示正确", async ({ page }) => {
    /* ... */
  });
});

describe("EditorForm - 组件操作", () => {
  test("拖拽添加组件", async ({ page }) => {
    /* ... */
  });
  test("组件选中和删除", async ({ page }) => {
    /* ... */
  });
});

// 按复杂度分组
describe("EditorForm - 冒烟测试", () => {
  test("基本工作流程", async ({ page }) => {
    /* ... */
  });
});

describe("EditorForm - 完整测试", () => {
  test("复杂表单构建流程", async ({ page }) => {
    /* ... */
  });
});
```

## 🚀 CI/CD集成设计

### GitHub Actions工作流

```yaml
name: E2E Tests
on: [push, pull_request]

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: "18"

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright
        run: npx playwright install --with-deps

      - name: Start application
        run: npm run dev &

      - name: Wait for application
        run: npx wait-on http://localhost:3000

      - name: Run E2E tests
        run: npx playwright test

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
```

## 📈 监控和报告

### 测试报告配置

```typescript
// 生成详细的HTML报告
reporter: [
  ["html", { outputFolder: "playwright-report" }],
  ["json", { outputFile: "test-results.json" }],
  ["junit", { outputFile: "test-results.xml" }],
];
```

### 质量指标监控

- 测试通过率趋势
- 测试执行时间监控
- 失败用例分析
- 覆盖率统计报告

这个设计方案提供了完整的E2E测试架构，确保editorForm组件的质量和稳定性。
