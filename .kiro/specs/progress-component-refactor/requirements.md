# 需求文档

## 介绍

此功能涉及重构两个几乎相同的React组件（`ProgressRecordTab` 和 `AppointmentProgressRecordTab`），这两个组件用于显示作业进度时间线。组件存在大量重复代码，应该整合为可重用组件以提高可维护性并减少技术债务。

## 需求

### 需求 1

**用户故事：** 作为开发者，我希望消除进度组件之间的代码重复，以便代码库更易维护且保持一致性。

#### 验收标准

1. 当检查当前代码库时，系统应识别出 `ProgressRecordTab` 和 `AppointmentProgressRecordTab` 包含几乎相同的逻辑
2. 当重构完成时，系统应消除重复代码同时保持相同的功能
3. 当使用重构后的组件时，它们应完全按照之前的方式显示进度时间线

### 需求 2

**用户故事：** 作为开发者，我希望有可重用的进度组件，以便将来类似功能可以更高效地实现。

#### 验收标准

1. 当创建新的进度时间线功能时，开发者应能够重用通用组件
2. 当创建通用组件时，它们应通过参数接受不同的数据源
3. 当使用组件时，它们应支持不同的API函数和atom配置

### 需求 3

**用户故事：** 作为开发者，我希望有清晰的关注点分离，以便数据获取和UI渲染逻辑得到适当分离。

#### 验收标准

1. 当检查重构后的代码时，数据获取逻辑应分离到自定义hook中
2. 当检查重构后的代码时，UI渲染逻辑应分离到专用组件中
3. 当测试组件时，每个部分应能够独立测试

### 需求 4

**用户故事：** 作为用户，我希望现有功能保持不变，以便重构不会破坏当前功能。

#### 验收标准

1. 当使用重构后的组件时，所有现有功能应完全相同地工作
2. 当查看进度时间线时，UI应与之前完全相同
3. 当与组件交互时，所有行为应保持不变
4. 当组件加载数据时，它们应使用与之前相同的API调用和状态管理
