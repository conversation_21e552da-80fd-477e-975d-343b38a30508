# 实施计划

- [x] 1. 创建类型定义和接口

  - 在 `src/components/progress-timeline/types.ts` 中定义所有必要的TypeScript接口
  - 定义 `ProgressItem`、`SubprocessItem`、`ProgressTimelineProps` 等类型
  - 定义 `UseProgressDataConfig` 和 `UseProgressDataReturn` 接口
  - _需求: 需求3.1, 需求2.2_

- [x] 2. 实现 useProgressData 自定义Hook

  - 在 `src/hooks/useProgressData.ts` 中创建数据获取Hook
  - 实现参数化的API调用逻辑，支持不同的apiFunction和atom
  - 使用 `@tanstack/react-query` 处理数据获取和缓存
  - 使用 `jotai` 管理atom状态
  - 实现数据标准化处理，确保返回一致的数据格式
  - 添加错误处理和加载状态管理
  - _需求: 需求3.1, 需求2.2, 需求1.2_

- [x] 3. 创建 ProgressTimeline 通用组件

  - 在 `src/components/progress-timeline/index.tsx` 中创建UI组件
  - 实现 `renderStatus` 函数，处理状态图标和样式映射
  - 实现 `CardContent` 函数，渲染进度项内容和子进程时间线
  - 使用 Semi Design 的 Timeline 组件构建UI结构
  - 处理时间格式化和员工名称渲染
  - 实现加载状态和空数据的优雅处理
  - 确保样式与原组件完全一致
  - _需求: 需求3.2, 需求4.2, 需求1.2_

- [x] 4. 更新导出文件

  - 在 `src/hooks/index.tsx` 中导出新的 `useProgressData` Hook
  - 在 `src/components/index.tsx` 中导出新的 `ProgressTimeline` 组件
  - 确保新组件可以被其他模块正确导入
  - _需求: 需求2.1_

- [x] 5. 重构 ProgressRecordTab 组件

  - 修改 `src/pages/ticket/content/ticketList/tabs/jobProgress.tsx`
  - 移除重复的数据获取逻辑，使用 `useProgressData` Hook
  - 移除重复的UI渲染逻辑，使用 `ProgressTimeline` 组件
  - 配置Hook参数：`getJobSliceProgress` API函数和 `jobSliceInfoAtom`
  - 保持组件导出名称和外部接口不变
  - _需求: 需求1.1, 需求4.1, 需求4.4_

- [x] 6. 重构 AppointmentProgressRecordTab 组件

  - 修改 `src/pages/ticket/content/jobAppointment/tabs/jobProgress.tsx`
  - 移除重复的数据获取逻辑，使用 `useProgressData` Hook
  - 移除重复的UI渲染逻辑，使用 `ProgressTimeline` 组件
  - 配置Hook参数：`getAppointmentProgress` API函数和 `jobAppointmentInfoAtom`
  - 保持组件导出名称和外部接口不变
  - _需求: 需求1.1, 需求4.1, 需求4.4_

- [ ] 7. 创建单元测试

  - 为 `useProgressData` Hook 创建测试文件
  - 测试不同API函数和atom配置的正确工作
  - 测试加载状态、错误处理和数据转换
  - 为 `ProgressTimeline` 组件创建测试文件
  - 测试不同数据格式的渲染结果
  - 测试状态图标、时间格式化和员工名称显示
  - _需求: 需求3.3_

- [ ] 8. 验证重构结果
  - 运行现有测试套件，确保没有破坏性变更
  - 手动测试两个重构后的组件，验证功能完全一致
  - 检查UI显示、交互行为和数据加载是否与原组件相同
  - 验证错误处理和边界情况的正确处理
  - _需求: 需求4.1, 需求4.2, 需求4.3_
