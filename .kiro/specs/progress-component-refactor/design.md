# 设计文档

## 概述

本设计旨在重构两个几乎相同的进度展示组件，通过创建可重用的通用组件来消除代码重复。设计遵循关注点分离原则，将数据获取逻辑和UI渲染逻辑分离到不同的模块中。

## 架构

### 当前架构问题

- `ProgressRecordTab` 和 `AppointmentProgressRecordTab` 包含95%相同的代码
- 数据获取和UI渲染逻辑耦合在同一个组件中
- 缺乏可重用性，未来类似功能需要重复编写相同逻辑

### 目标架构

```
┌─────────────────────────────────────┐
│           业务组件层                 │
│  ProgressRecordTab                  │
│  AppointmentProgressRecordTab       │
└─────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────┐
│           Hook层                    │
│        useProgressData              │
└─────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────┐
│           UI组件层                  │
│       ProgressTimeline              │
└─────────────────────────────────────┘
```

## 组件和接口

### 1. useProgressData Hook

**职责：** 封装进度数据获取逻辑

**接口设计：**

```typescript
interface UseProgressDataConfig {
  apiFunction: (id: string) => Promise<any>;
  atom: PrimitiveAtom<any>;
  queryKey: string;
}

interface UseProgressDataReturn {
  isLoading: boolean;
  data: any[];
  error?: Error;
}

function useProgressData(config: UseProgressDataConfig): UseProgressDataReturn;
```

**实现要点：**

- 使用 `@tanstack/react-query` 进行数据获取
- 使用 `jotai` 管理状态
- 支持参数化配置不同的API函数和atom
- 返回标准化的数据格式

### 2. ProgressTimeline 组件

**职责：** 渲染进度时间线UI

**接口设计：**

```typescript
interface ProgressItem {
  id: string;
  name: string;
  status: number;
  subprocessList: SubprocessItem[];
}

interface SubprocessItem {
  name: string;
  status: number;
  finishTime?: string;
  candidateList?: any[];
}

interface ProgressTimelineProps {
  data: ProgressItem[];
  isLoading?: boolean;
}

function ProgressTimeline(props: ProgressTimelineProps): JSX.Element;
```

**实现要点：**

- 接收标准化的数据格式
- 包含 `renderStatus` 状态渲染逻辑
- 包含 `CardContent` 内容渲染逻辑
- 使用 Semi Design 的 Timeline 组件
- 支持加载状态显示

### 3. 重构后的业务组件

**ProgressRecordTab 重构：**

```typescript
export const ProgressRecordTab = () => {
  const { data, isLoading } = useProgressData({
    apiFunction: getJobSliceProgress,
    atom: jobSliceInfoAtom,
    queryKey: "getJobSliceProgress"
  });

  return <ProgressTimeline data={data} isLoading={isLoading} />;
};
```

**AppointmentProgressRecordTab 重构：**

```typescript
export const AppointmentProgressRecordTab = () => {
  const { data, isLoading } = useProgressData({
    apiFunction: getAppointmentProgress,
    atom: jobAppointmentInfoAtom,
    queryKey: "getAppointmentProgress"
  });

  return <ProgressTimeline data={data} isLoading={isLoading} />;
};
```

## 数据模型

### 输入数据格式

```typescript
interface ApiResponse {
  data: ProgressItem[];
}

interface ProgressItem {
  id: string;
  name: string;
  status: number; // 1: 未完成/错误, 其他: 完成/成功
  subprocessList: SubprocessItem[];
}

interface SubprocessItem {
  name: string;
  status: number;
  finishTime?: string;
  candidateList?: Employee[];
}
```

### 状态映射

- `status === 1`: 错误状态，显示警告图标和红色文本
- `status !== 1`: 成功状态，显示勾选图标

## 错误处理

### Hook层错误处理

- 使用 React Query 的内置错误处理机制
- 返回错误状态供组件使用
- 支持重试机制

### 组件层错误处理

- 显示加载状态
- 处理空数据情况
- 优雅降级显示

### 数据验证

- 验证API返回数据格式
- 处理缺失字段的默认值
- 类型安全检查

## 测试策略

### 单元测试

**useProgressData Hook测试：**

- 测试不同API函数的调用
- 测试不同atom的状态管理
- 测试加载和错误状态
- 测试数据转换逻辑

**ProgressTimeline组件测试：**

- 测试不同数据格式的渲染
- 测试状态图标的正确显示
- 测试时间格式化
- 测试员工名称渲染
- 测试空数据处理

### 集成测试

- 测试Hook和组件的协作
- 测试完整的数据流
- 测试用户交互

### 回归测试

- 确保重构后功能完全一致
- 测试现有页面的正常工作
- 性能对比测试

## 文件结构

```
src/
├── hooks/
│   ├── useProgressData.ts          # 新增：数据获取Hook
│   └── index.tsx                   # 更新：导出新Hook
├── components/
│   ├── progress-timeline/
│   │   ├── index.tsx              # 新增：进度时间线组件
│   │   └── types.ts               # 新增：类型定义
│   └── index.tsx                  # 更新：导出新组件
└── pages/
    ├── ticket/content/ticketList/tabs/
    │   └── jobProgress.tsx        # 重构：使用新Hook和组件
    └── ticket/content/jobAppointment/tabs/
        └── jobProgress.tsx        # 重构：使用新Hook和组件
```

## 迁移策略

### 阶段1：创建通用组件

- 创建 `useProgressData` Hook
- 创建 `ProgressTimeline` 组件
- 添加类型定义

### 阶段2：重构现有组件

- 重构 `ProgressRecordTab`
- 重构 `AppointmentProgressRecordTab`
- 更新导出文件

### 阶段3：测试和验证

- 运行测试套件
- 手动验证功能一致性
- 性能测试

### 向后兼容性

- 保持现有组件的导出名称不变
- 保持组件的外部接口不变
- 确保样式和行为完全一致
