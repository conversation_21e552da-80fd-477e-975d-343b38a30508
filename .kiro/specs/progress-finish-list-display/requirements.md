# 需求文档

## 介绍

此功能涉及在现有的进度时间线组件中添加finishList显示功能。根据新的接口文档，当finishList不为空时，需要与candidateList同级显示已完成的审批记录，包括审批时间、审批人员和相关图片的查看功能。

## 需求

### 需求 1

**用户故事：** 作为用户，我希望在进度时间线中看到已完成的审批记录，以便了解每个步骤的完成情况和审批人员。

#### 验收标准

1. 当API返回的subprocessList中包含finishList字段且不为空时，系统应在candidateList同级显示finishList字段内容
2. 当显示finishList时，每一项应显示为一行，格式为"{approveTime字段内容}: {approveUser.name字段内容}完成[icon]"
3. 当finishList字段为空或不存在时，系统应正常显示原有的candidateList字段内容

### 需求 2

**用户故事：** 作为用户，我希望能够查看审批记录相关的图片，以便了解审批的详细信息。

#### 验收标准

1. 当点击finishList项中的图标时，系统应显示照片墙浮层
2. 当显示照片墙时，内容应为该审批记录的imageList字段内容
3. 当imageList字段为空时，点击应有适当的提示或不显示点击效果

### 需求 3

**用户故事：** 作为开发者，我希望新功能能够复用现有的ProgressTimeline组件，以便保持代码的一致性和可维护性。

#### 验收标准

1. 当实现新功能时，应扩展现有的ProgressTimeline组件而不是创建新组件
2. 当更新组件时，应保持向后兼容性，不影响现有功能
3. 当处理数据时，应更新相关的TypeScript类型定义

### 需求 4

**用户故事：** 作为用户，我希望finishList的显示样式与现有界面保持一致，以便获得统一的用户体验。

#### 验收标准

1. 当显示finishList字段内容时，样式应与现有的candidateList字段内容保持一致的设计风格
2. 当显示审批时间时，应使用与系统其他部分一致的时间格式
3. 当显示图标时，应选择合适的图标并保持与系统整体风格一致
