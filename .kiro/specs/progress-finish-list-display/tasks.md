# 实施计划

- [x] 1. 更新类型定义

  - 在 `src/components/progress-timeline/types.ts` 中添加 `FinishItem` 接口定义
  - 更新 `SubprocessItem` 接口，添加可选的 `finishList` 字段
  - 添加 `FinishListDisplayProps` 和 `ImageGalleryModalProps` 接口定义
  - 确保所有新类型都正确导出
  - _需求: 需求3.3_

- [x] 2. 创建 FinishListDisplay 组件

  - 在 `src/components/progress-timeline/FinishListDisplay.tsx` 中创建组件
  - 实现finishList数据的渲染，格式为"{approveTime}: {approveUser.name}完成[icon]"
  - 使用Tailwind CSS类名进行样式设置
  - 实现点击事件处理，当imageList不为空时触发onImageClick回调
  - 当imageList为空时不显示点击图标
  - 添加适当的错误边界处理
  - _需求: 需求1.1, 需求1.2, 需求4.1, 需求4.3_

- [x] 3. 创建 ImageGalleryModal 组件

  - 在 `src/components/progress-timeline/ImageGalleryModal.tsx` 中创建组件
  - 使用Semi Design的Modal组件实现照片墙浮层
  - 支持图片列表的展示和预览功能
  - 实现键盘ESC关闭功能
  - 添加图片加载失败的错误处理
  - 使用响应式设计适配不同屏幕尺寸
  - _需求: 需求2.1, 需求2.2, 需求2.3_

- [x] 4. 更新 ProgressTimeline 组件

  - 修改 `src/components/progress-timeline/index.tsx` 中的 `CardContent` 函数
  - 添加状态管理用于控制ImageGalleryModal的显示
  - 在Timeline.Item的extra部分集成FinishListDisplay组件
  - 实现handleImageClick事件处理函数
  - 在CardContent中添加ImageGalleryModal组件
  - 确保向后兼容性，不影响现有功能
  - _需求: 需求1.1, 需求1.3, 需求3.1, 需求3.2_

- [x] 5. 更新组件导出

  - 在 `src/components/progress-timeline/index.tsx` 中导出新的子组件
  - 确保新的类型定义正确导出
  - 验证组件可以被外部正确导入使用
  - _需求: 需求3.2_

- [ ] 6. 创建单元测试

  - 为 `FinishListDisplay` 组件创建测试文件
  - 测试正常数据渲染、空数据处理和点击事件
  - 为 `ImageGalleryModal` 组件创建测试文件
  - 测试模态框显示隐藏、图片列表渲染和键盘事件
  - 为更新后的 `ProgressTimeline` 组件添加测试用例
  - 测试finishList功能的集成和数据流
  - _需求: 需求3.3_

- [ ] 7. 验证功能实现

  - 手动测试两个进度组件页面的finishList显示功能
  - 验证审批记录的格式显示是否正确
  - 测试图片点击和照片墙浮层功能
  - 验证空数据和边界情况的处理
  - 确保现有功能没有受到影响
  - 检查样式和用户体验的一致性
  - _需求: 需求1.1, 需求1.2, 需求1.3, 需求2.1, 需求2.2, 需求2.3, 需求4.1, 需求4.2, 需求4.3_
