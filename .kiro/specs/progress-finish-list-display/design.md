# 设计文档

## 概述

本设计旨在扩展现有的ProgressTimeline组件，添加finishList显示功能。当API返回的数据中包含finishList字段时，在现有的candidateList同级显示已完成的审批记录，包括审批时间、审批人员信息，并支持点击查看相关图片的照片墙功能。

## 架构

### 当前架构

```
┌─────────────────────────────────────┐
│           业务组件层                 │
│  ProgressRecordTab                  │
│  AppointmentProgressRecordTab       │
└─────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────┐
│           Hook层                    │
│        useProgressData              │
└─────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────┐
│           UI组件层                  │
│       ProgressTimeline              │
└─────────────────────────────────────┘
```

### 扩展后架构

```
┌─────────────────────────────────────┐
│           业务组件层                 │
│  ProgressRecordTab                  │
│  AppointmentProgressRecordTab       │
└─────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────┐
│           Hook层                    │
│        useProgressData              │
└─────────────────────────────────────┘
                  │
                  ▼
┌─────────────────────────────────────┐
│           UI组件层                  │
│       ProgressTimeline              │
│    ├── FinishListDisplay            │
│    └── ImageGalleryModal            │
└─────────────────────────────────────┘
```

## 组件和接口

### 1. 扩展的数据类型

**更新SubprocessItem接口：**

```typescript
export interface FinishItem {
  approveTime: string;
  approveUser: {
    id: string;
    name: string;
    type?: number;
  };
  imageList: string[]; // 图片URL数组
}

export interface SubprocessItem {
  name: string;
  status: number;
  finishTime?: string;
  candidateList?: Employee[];
  finishList?: FinishItem[]; // 新增：已完成审批记录
}
```

### 2. FinishListDisplay 组件

**职责：** 渲染已完成的审批记录列表

**接口设计：**

```typescript
interface FinishListDisplayProps {
  finishList: FinishItem[];
  onImageClick: (imageList: string[]) => void;
}

function FinishListDisplay(props: FinishListDisplayProps): JSX.Element;
```

**实现要点：**

- 渲染格式："{approveTime}: {approveUser.name}完成[icon]"
- 使用合适的完成图标（如IconCheckboxTick或IconCheck）
- 支持点击触发图片查看
- 当imageList为空时，不显示点击图标

### 3. ImageGalleryModal 组件

**职责：** 显示照片墙浮层

**接口设计：**

```typescript
interface ImageGalleryModalProps {
  visible: boolean;
  imageList: string[];
  onClose: () => void;
}

function ImageGalleryModal(props: ImageGalleryModalProps): JSX.Element;
```

**实现要点：**

- 使用Semi Design的Modal组件
- 支持图片预览和缩放
- 支持键盘导航（ESC关闭）
- 响应式设计，适配不同屏幕尺寸

### 4. 更新的ProgressTimeline组件

**扩展CardContent函数：**

```typescript
const CardContent = (item: ProgressItem): JSX.Element => {
  const [imageModalVisible, setImageModalVisible] = useState(false);
  const [currentImageList, setCurrentImageList] = useState<string[]>([]);

  const handleImageClick = (imageList: string[]) => {
    if (imageList && imageList.length > 0) {
      setCurrentImageList(imageList);
      setImageModalVisible(true);
    }
  };

  return (
    <div className="flex flex-col">
      <h2>{item.name}</h2>
      <Timeline mode="left">
        {(item?.subprocessList ?? []).map((subprocess: SubprocessItem, index: number) => (
          <Timeline.Item
            time={
              subprocess.status === 1
                ? ""
                : `完成时间: ${formatDate(subprocess.finishTime as any) || "未定义"}`
            }
            {...renderStatus(subprocess.status)}
            extra={
              <>
                {subprocess.status === 1 ? (
                  <p className="text-red-400">
                    未完成, 等待
                    {renderEmployeeSimpleNames(subprocess.candidateList || []).join(",")}
                    &nbsp;处理
                  </p>
                ) : null}
                {/* 新增：显示finishList */}
                {subprocess.finishList && subprocess.finishList.length > 0 && (
                  <FinishListDisplay
                    finishList={subprocess.finishList}
                    onImageClick={handleImageClick}
                  />
                )}
              </>
            }
            key={index}
          >
            {subprocess.name}
          </Timeline.Item>
        ))}
      </Timeline>

      {/* 新增：图片查看模态框 */}
      <ImageGalleryModal
        visible={imageModalVisible}
        imageList={currentImageList}
        onClose={() => setImageModalVisible(false)}
      />
    </div>
  );
};
```

## 数据模型

### 输入数据格式

```typescript
interface ApiResponse {
  data: ProgressItem[];
}

interface ProgressItem {
  id: string;
  name: string;
  status: number;
  subprocessList: SubprocessItem[];
}

interface SubprocessItem {
  name: string;
  status: number;
  finishTime?: string;
  candidateList?: Employee[];
  finishList?: FinishItem[]; // 新增字段
}

interface FinishItem {
  approveTime: string; // 审批时间，格式如 "2024-01-15 14:30:00"
  approveUser: {
    id: string;
    name: string;
    type?: number;
  };
  imageList: string[]; // 图片URL数组
}
```

### 数据处理逻辑

- 检查每个subprocess的finishList字段
- 格式化approveTime显示格式
- 验证imageList的有效性
- 处理空数据的边界情况

## UI设计

### FinishList显示样式

使用Tailwind CSS类名：

```typescript
// FinishListDisplay组件样式
const finishListItemClasses =
  "flex items-center my-1 p-2 bg-gray-50 rounded cursor-pointer transition-colors hover:bg-gray-100";
const finishListItemDisabledClasses =
  "flex items-center my-1 p-2 bg-gray-50 rounded opacity-60";
const finishListTextClasses = "flex-1 text-sm text-gray-600";
const finishListIconClasses = "ml-2 text-green-600";
```

### 图片查看模态框

- 使用Semi Design的Modal组件
- 支持图片轮播和缩放
- 响应式布局
- 优雅的加载和错误处理

## 错误处理

### 数据验证

- 验证finishList数组的有效性
- 检查approveTime格式
- 验证approveUser对象结构
- 处理imageList为空或无效的情况

### 用户交互错误

- 图片加载失败的处理
- 网络错误的重试机制
- 模态框关闭的异常处理

### 边界情况

- finishList为空数组
- approveTime为null或undefined
- imageList包含无效URL
- 用户权限不足查看图片

## 测试策略

### 单元测试

**FinishListDisplay组件测试：**

- 测试正常数据的渲染
- 测试空数据的处理
- 测试点击事件的触发
- 测试禁用状态的显示

**ImageGalleryModal组件测试：**

- 测试模态框的显示和隐藏
- 测试图片列表的渲染
- 测试键盘事件处理
- 测试响应式布局

### 集成测试

- 测试ProgressTimeline与新组件的集成
- 测试数据流的完整性
- 测试用户交互的端到端流程

### 视觉回归测试

- 确保新功能不影响现有样式
- 测试不同屏幕尺寸的显示效果
- 验证图标和颜色的一致性

## 文件结构

```
src/
├── components/
│   ├── progress-timeline/
│   │   ├── index.tsx              # 更新：添加finishList支持
│   │   ├── types.ts               # 更新：添加新的类型定义
│   │   ├── FinishListDisplay.tsx  # 新增：已完成记录显示组件
│   │   └── ImageGalleryModal.tsx  # 新增：图片查看模态框组件
│   └── index.tsx                  # 更新：导出新组件
└── pages/
    ├── ticket/content/ticketList/tabs/
    │   └── jobProgress.tsx        # 无需修改：自动支持新功能
    └── ticket/content/jobAppointment/tabs/
        └── jobProgress.tsx        # 无需修改：自动支持新功能
```

## 实现策略

### 阶段1：扩展类型定义

- 更新SubprocessItem接口
- 添加FinishItem接口
- 更新相关类型导出

### 阶段2：创建子组件

- 实现FinishListDisplay组件
- 实现ImageGalleryModal组件
- 添加必要的样式

### 阶段3：集成到ProgressTimeline

- 更新CardContent函数
- 添加状态管理
- 处理事件交互

### 阶段4：测试和优化

- 单元测试和集成测试
- 性能优化
- 用户体验优化

### 向后兼容性

- 保持现有API接口不变
- 新字段为可选字段
- 不影响现有功能的正常使用
- 渐进式增强的设计理念
