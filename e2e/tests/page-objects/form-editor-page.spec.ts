import { expect, test } from "@playwright/test";
import { FormEditorPage } from "../../pages/FormEditorPage";
import { AuthHelpers } from "../../utils/test-helpers";

test.describe("FormEditorPage Page Object 验证", () => {
  let formEditorPage: FormEditorPage;

  test.beforeEach(async ({ page }) => {
    // 使用真实API登录，从环境变量获取用户名密码，后端已配置固定验证码用于测试
    await AuthHelpers.realApiLogin(page);

    formEditorPage = new FormEditorPage(page);
    await formEditorPage.goto();
    await formEditorPage.waitForEditorLoad();
  });

  test("应该成功加载表单编辑器页面", async () => {
    // 验证页面是否已加载
    expect(await formEditorPage.isLoaded()).toBe(true);

    // 验证组件库是否可见
    expect(await formEditorPage.componentLibrary.isVisible()).toBe(true);

    // 验证工具栏是否可见
    expect(await formEditorPage.isToolbarVisible()).toBe(true);
  });

  test("应该能够显示组件库中的所有组件", async () => {
    // 获取可见的组件
    const visibleComponents =
      await formEditorPage.componentLibrary.getVisibleComponents();

    // 验证至少有基础组件
    expect(visibleComponents.length).toBeGreaterThan(0);

    // 验证包含基本的输入组件
    expect(visibleComponents).toContain("input");
  });

  test("应该能够添加输入框组件到画布", async () => {
    // 获取初始组件数量
    const initialCount = await formEditorPage.getCanvasComponentCount();

    // 拖拽输入框组件到画布
    await formEditorPage.dragComponentToCanvas("INPUT");

    // 验证组件数量增加
    const newCount = await formEditorPage.getCanvasComponentCount();
    expect(newCount).toBe(initialCount + 1);
  });

  test("应该能够选择和删除画布中的组件", async () => {
    // 先添加一个组件
    await formEditorPage.dragComponentToCanvas("INPUT");

    // 获取组件数量
    const countAfterAdd = await formEditorPage.getCanvasComponentCount();
    expect(countAfterAdd).toBeGreaterThan(0);

    // 选择并删除第一个组件
    await formEditorPage.selectCanvasComponent(0);
    await formEditorPage.deleteCanvasComponent(0);

    // 验证组件被删除
    const countAfterDelete = await formEditorPage.getCanvasComponentCount();
    expect(countAfterDelete).toBe(countAfterAdd - 1);
  });

  test("应该能够保存表单配置", async () => {
    // 添加一个组件
    await formEditorPage.dragComponentToCanvas("INPUT");

    // 保存表单
    await formEditorPage.saveForm();

    // 验证保存完成（草稿标识消失）
    await formEditorPage.waitForSaveComplete();
    expect(await formEditorPage.hasUnsavedChanges()).toBe(false);
  });

  test("应该能够重置表单配置", async () => {
    // 添加一个组件
    await formEditorPage.dragComponentToCanvas("INPUT");

    // 验证有组件
    expect(await formEditorPage.getCanvasComponentCount()).toBeGreaterThan(0);

    // 重置表单
    await formEditorPage.resetForm();

    // 验证组件被清空
    expect(await formEditorPage.getCanvasComponentCount()).toBe(0);
  });

  test("应该能够使用布局控制功能", async () => {
    // 验证可以添加列
    expect(await formEditorPage.componentLibrary.canAddColumn()).toBe(true);

    // 添加一列
    await formEditorPage.componentLibrary.addColumn();

    // 验证可以删除列
    expect(await formEditorPage.componentLibrary.canRemoveColumn()).toBe(true);

    // 删除一列
    await formEditorPage.componentLibrary.removeColumn();
  });

  test("应该能够获取组件库状态", async () => {
    const libraryState =
      await formEditorPage.componentLibrary.getLibraryState();

    // 验证状态结构
    expect(libraryState).toHaveProperty("isVisible");
    expect(libraryState).toHaveProperty("title");
    expect(libraryState).toHaveProperty("visibleComponents");
    expect(libraryState).toHaveProperty("canAddColumn");
    expect(libraryState).toHaveProperty("canRemoveColumn");

    // 验证基本状态
    expect(libraryState.isVisible).toBe(true);
    expect(libraryState.visibleComponents.length).toBeGreaterThan(0);
  });

  test("应该能够检查特定组件是否存在", async () => {
    // 检查基础组件是否存在
    expect(await formEditorPage.componentLibrary.hasComponent("INPUT")).toBe(
      true
    );
    expect(await formEditorPage.componentLibrary.hasComponent("TEXTAREA")).toBe(
      true
    );

    // 检查组件是否可见
    expect(
      await formEditorPage.componentLibrary.isComponentVisible("INPUT")
    ).toBe(true);
  });

  test("应该能够获取组件按钮文本", async () => {
    const inputButtonText =
      await formEditorPage.componentLibrary.getComponentButtonText("INPUT");
    expect(inputButtonText).toContain("输入框");

    const textareaButtonText =
      await formEditorPage.componentLibrary.getComponentButtonText("TEXTAREA");
    expect(textareaButtonText).toContain("多行输入");
  });

  test("应该能够等待组件可点击", async () => {
    // 等待输入框组件可点击
    await formEditorPage.componentLibrary.waitForComponentClickable("INPUT");

    // 验证组件确实可点击
    expect(
      await formEditorPage.componentLibrary.isComponentEnabled("INPUT")
    ).toBe(true);
  });
});

test.describe("ComponentLibraryPage 独立测试", () => {
  let formEditorPage: FormEditorPage;

  test.beforeEach(async ({ page }) => {
    // 先登录，确保有权限访问表单编辑器页面
    await AuthHelpers.login(page, "admin", "admin123");

    formEditorPage = new FormEditorPage(page);
    await formEditorPage.goto();
    await formEditorPage.waitForEditorLoad();
  });

  test("应该能够获取所有组件项", async () => {
    const componentItems =
      await formEditorPage.componentLibrary.getAllComponentItems();
    expect(componentItems.length).toBeGreaterThan(0);
  });

  test("应该能够根据文本查找组件", async () => {
    const inputComponent =
      await formEditorPage.componentLibrary.findComponentByText("添加输入框");
    expect(inputComponent).not.toBeNull();

    const nonExistentComponent =
      await formEditorPage.componentLibrary.findComponentByText("不存在的组件");
    expect(nonExistentComponent).toBeNull();
  });

  test("应该能够获取组件按钮样式类", async () => {
    const buttonClass =
      await formEditorPage.componentLibrary.getComponentButtonClass("INPUT");
    expect(buttonClass).toContain("btn");
  });
});

test.describe("PropertyPanelPage 基础测试", () => {
  let formEditorPage: FormEditorPage;

  test.beforeEach(async ({ page }) => {
    // 先登录，确保有权限访问表单编辑器页面
    await AuthHelpers.login(page, "admin", "admin123");

    formEditorPage = new FormEditorPage(page);
    await formEditorPage.goto();
    await formEditorPage.waitForEditorLoad();
  });

  test("应该能够显示属性面板", async () => {
    expect(await formEditorPage.propertyPanel.isVisible()).toBe(true);
  });

  test("应该能够获取属性面板状态", async () => {
    const panelState = await formEditorPage.propertyPanel.getPanelState();

    expect(panelState).toHaveProperty("isVisible");
    expect(panelState).toHaveProperty("title");
    expect(panelState).toHaveProperty("visibleProperties");

    expect(panelState.isVisible).toBe(true);
  });
});
