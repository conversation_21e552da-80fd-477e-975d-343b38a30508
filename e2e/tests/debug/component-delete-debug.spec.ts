import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../../utils/test-helpers';
import { FormEditorPage } from '../../pages/FormEditorPage';

test.describe('组件删除调试测试', () => {
  test('调试组件删除的正确方式', async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);
    
    // 初始化表单编辑器页面对象
    const formEditorPage = new FormEditorPage(page);
    
    // 导航到表单编辑器页面
    await formEditorPage.goto();
    await formEditorPage.waitForEditorLoad();
    
    console.log('✅ 页面加载完成，开始调试组件删除');
    
    // 1. 获取初始组件数量
    const initialCount = await formEditorPage.getCanvasComponentCount();
    console.log('🔍 初始组件数量:', initialCount);
    
    // 2. 添加一个新组件
    console.log('🔍 添加新组件...');
    await formEditorPage.dragComponentToCanvas("INPUT");
    await page.waitForTimeout(2000);
    
    const afterAddCount = await formEditorPage.getCanvasComponentCount();
    console.log('🔍 添加后组件数量:', afterAddCount);
    
    // 3. 获取所有组件
    const components = await formEditorPage.getCanvasComponents();
    console.log('🔍 组件数量:', components.length);
    
    if (components.length > 0) {
      const lastComponent = components[components.length - 1];
      
      // 4. 点击选中最后一个组件
      console.log('🔍 选中最后一个组件...');
      await lastComponent.click();
      await page.waitForTimeout(1000);
      
      // 5. 检查选中后的状态
      const componentHTML = await lastComponent.innerHTML();
      console.log('🔍 选中组件的HTML:', componentHTML.substring(0, 200));
      
      // 6. 检查是否有删除按钮出现
      const deleteButtons = await page.locator('button:has-text("删除"), [title*="删除"], .delete-btn, .remove-btn').all();
      console.log('🔍 删除按钮数量:', deleteButtons.length);
      
      for (let i = 0; i < deleteButtons.length; i++) {
        const buttonText = await deleteButtons[i].textContent();
        const buttonTitle = await deleteButtons[i].getAttribute('title');
        const buttonClass = await deleteButtons[i].getAttribute('class');
        console.log(`  删除按钮 ${i + 1}: text="${buttonText}" title="${buttonTitle}" class="${buttonClass}"`);
      }
      
      // 7. 检查右键菜单
      console.log('🔍 尝试右键点击组件...');
      await lastComponent.click({ button: 'right' });
      await page.waitForTimeout(1000);
      
      const contextMenus = await page.locator('.context-menu, .right-click-menu, [role="menu"]').all();
      console.log('🔍 右键菜单数量:', contextMenus.length);
      
      for (let i = 0; i < contextMenus.length; i++) {
        const menuContent = await contextMenus[i].textContent();
        console.log(`  右键菜单 ${i + 1}: "${menuContent}"`);
      }
      
      // 8. 尝试Delete键删除
      console.log('🔍 尝试Delete键删除...');
      await page.keyboard.press('Delete');
      await page.waitForTimeout(2000);
      
      const afterDeleteCount = await formEditorPage.getCanvasComponentCount();
      console.log('🔍 Delete键后组件数量:', afterDeleteCount);
      
      // 9. 尝试Backspace键删除
      console.log('🔍 尝试Backspace键删除...');
      await lastComponent.click(); // 重新选中
      await page.keyboard.press('Backspace');
      await page.waitForTimeout(2000);
      
      const afterBackspaceCount = await formEditorPage.getCanvasComponentCount();
      console.log('🔍 Backspace键后组件数量:', afterBackspaceCount);
      
      // 10. 检查工具栏是否有删除按钮
      const toolbar = page.locator('.form-sandbox__operation');
      const toolbarButtons = await toolbar.locator('button').all();
      console.log('🔍 工具栏按钮数量:', toolbarButtons.length);
      
      for (let i = 0; i < Math.min(toolbarButtons.length, 10); i++) {
        const buttonText = await toolbarButtons[i].textContent();
        const buttonTitle = await toolbarButtons[i].getAttribute('title');
        console.log(`  工具栏按钮 ${i + 1}: text="${buttonText}" title="${buttonTitle}"`);
      }
      
      // 11. 检查是否有删除图标
      const deleteIcons = await page.locator('[class*="delete"], [class*="remove"], [class*="trash"]').all();
      console.log('🔍 删除图标数量:', deleteIcons.length);
      
      for (let i = 0; i < Math.min(deleteIcons.length, 5); i++) {
        const iconClass = await deleteIcons[i].getAttribute('class');
        const iconParent = await deleteIcons[i].locator('..').first().textContent();
        console.log(`  删除图标 ${i + 1}: class="${iconClass}" parent="${iconParent?.substring(0, 50)}"`);
      }
      
      // 12. 尝试双击删除
      console.log('🔍 尝试双击删除...');
      await lastComponent.dblclick();
      await page.waitForTimeout(2000);
      
      const afterDoubleClickCount = await formEditorPage.getCanvasComponentCount();
      console.log('🔍 双击后组件数量:', afterDoubleClickCount);
    }
    
    // 13. 截图保存调试信息
    await page.screenshot({ path: 'e2e/reports/debug-component-delete.png', fullPage: true });
    console.log('📸 已保存调试截图: e2e/reports/debug-component-delete.png');
  });
});
