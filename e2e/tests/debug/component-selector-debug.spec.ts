import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../../utils/test-helpers';
import { FormEditorPage } from '../../pages/FormEditorPage';

test.describe('组件选择器调试测试', () => {
  test('调试组件库中的按钮选择器', async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);
    
    // 初始化表单编辑器页面对象
    const formEditorPage = new FormEditorPage(page);
    
    // 导航到表单编辑器页面
    await formEditorPage.goto();
    await formEditorPage.waitForEditorLoad();
    
    console.log('✅ 页面加载完成，开始调试组件选择器');
    
    // 1. 检查页面上所有的按钮
    const allButtons = await page.locator('button').all();
    console.log('🔍 页面上的按钮数量:', allButtons.length);
    
    for (let i = 0; i < Math.min(allButtons.length, 10); i++) {
      const buttonText = await allButtons[i].textContent();
      const buttonClass = await allButtons[i].getAttribute('class');
      console.log(`  按钮 ${i + 1}: "${buttonText}" (class: ${buttonClass})`);
    }
    
    // 2. 检查是否有包含"输入"的按钮
    const inputButtons = await page.locator('button:has-text("输入")').all();
    console.log('🔍 包含"输入"的按钮数量:', inputButtons.length);
    
    for (let i = 0; i < inputButtons.length; i++) {
      const buttonText = await inputButtons[i].textContent();
      console.log(`  输入按钮 ${i + 1}: "${buttonText}"`);
    }
    
    // 3. 检查是否有data-component-type属性的元素
    const dataComponentElements = await page.locator('[data-component-type]').all();
    console.log('🔍 有data-component-type属性的元素数量:', dataComponentElements.length);
    
    for (let i = 0; i < dataComponentElements.length; i++) {
      const componentType = await dataComponentElements[i].getAttribute('data-component-type');
      const tagName = await dataComponentElements[i].evaluate(el => el.tagName);
      console.log(`  组件元素 ${i + 1}: ${tagName} (type: ${componentType})`);
    }
    
    // 4. 检查组件库区域的内容
    const componentLibrary = page.locator('.bg-white.rounded-md.h-full.pb-4');
    const isLibraryVisible = await componentLibrary.isVisible();
    console.log('🔍 组件库是否可见:', isLibraryVisible);
    
    if (isLibraryVisible) {
      const libraryContent = await componentLibrary.textContent();
      console.log('🔍 组件库内容片段:', libraryContent?.substring(0, 500));
      
      // 检查组件库内的按钮
      const libraryButtons = await componentLibrary.locator('button').all();
      console.log('🔍 组件库内的按钮数量:', libraryButtons.length);
      
      for (let i = 0; i < Math.min(libraryButtons.length, 5); i++) {
        const buttonText = await libraryButtons[i].textContent();
        const buttonClass = await libraryButtons[i].getAttribute('class');
        console.log(`  组件库按钮 ${i + 1}: "${buttonText}" (class: ${buttonClass})`);
      }
    }
    
    // 5. 检查是否有特定的组件类型
    const possibleSelectors = [
      'button:has-text("输入框")',
      'button:has-text("文本框")',
      'button:has-text("INPUT")',
      'button:has-text("input")',
      '[data-type="INPUT"]',
      '[data-component="INPUT"]',
      '.component-input',
      '.input-component'
    ];
    
    console.log('🔍 测试可能的选择器:');
    for (const selector of possibleSelectors) {
      try {
        const elements = await page.locator(selector).all();
        if (elements.length > 0) {
          console.log(`  ✅ 找到选择器: ${selector} (${elements.length}个元素)`);
          const firstElementText = await elements[0].textContent();
          console.log(`    第一个元素文本: "${firstElementText}"`);
        } else {
          console.log(`  ❌ 未找到选择器: ${selector}`);
        }
      } catch (error) {
        console.log(`  ❌ 选择器错误: ${selector} - ${error.message}`);
      }
    }
    
    // 6. 截图保存调试信息
    await page.screenshot({ path: 'e2e/reports/debug-component-selectors.png', fullPage: true });
    console.log('📸 已保存调试截图: e2e/reports/debug-component-selectors.png');
  });
});
