import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../../utils/test-helpers';
import { FormEditorPage } from '../../pages/FormEditorPage';

test.describe('页面加载调试测试', () => {
  test('调试表单编辑器页面加载过程', async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);
    console.log('✅ 登录完成');
    
    // 初始化表单编辑器页面对象
    const formEditorPage = new FormEditorPage(page);
    
    // 导航到表单编辑器页面
    console.log('🔍 开始导航到表单编辑器页面');
    await formEditorPage.goto();
    console.log('✅ 页面导航完成');
    
    // 分步检查页面元素
    console.log('🔍 检查页面元素...');
    
    // 1. 检查画布区域
    const canvasArea = page.locator('.form-sandbox__content');
    console.log('🔍 等待画布区域...');
    try {
      await canvasArea.waitFor({ state: 'visible', timeout: 10000 });
      console.log('✅ 画布区域已加载');
    } catch (error) {
      console.log('❌ 画布区域加载失败:', error.message);
      
      // 检查页面上实际存在的元素
      const allElements = await page.locator('*').all();
      console.log('🔍 页面元素数量:', allElements.length);
      
      // 检查是否有form相关的元素
      const formElements = await page.locator('[class*="form"]').all();
      console.log('🔍 form相关元素数量:', formElements.length);
      
      // 输出页面HTML结构（前1000字符）
      const pageContent = await page.content();
      console.log('🔍 页面内容片段:', pageContent.substring(0, 1000));
    }
    
    // 2. 检查工具栏
    const toolbar = page.locator('.form-sandbox__operation');
    console.log('🔍 等待工具栏...');
    try {
      await toolbar.waitFor({ state: 'visible', timeout: 10000 });
      console.log('✅ 工具栏已加载');
    } catch (error) {
      console.log('❌ 工具栏加载失败:', error.message);
    }
    
    // 3. 检查组件库
    const componentLibrary = page.locator('.bg-white.rounded-md.h-full.pb-4');
    console.log('🔍 等待组件库...');
    try {
      await componentLibrary.waitFor({ state: 'visible', timeout: 10000 });
      console.log('✅ 组件库已加载');
    } catch (error) {
      console.log('❌ 组件库加载失败:', error.message);
    }
    
    // 4. 检查页面URL
    const currentUrl = page.url();
    console.log('🔍 当前页面URL:', currentUrl);
    
    // 5. 检查页面标题
    const pageTitle = await page.title();
    console.log('🔍 页面标题:', pageTitle);
    
    // 6. 等待网络空闲
    console.log('🔍 等待网络空闲...');
    try {
      await page.waitForLoadState('networkidle', { timeout: 15000 });
      console.log('✅ 网络空闲');
    } catch (error) {
      console.log('❌ 网络空闲超时:', error.message);
    }
    
    // 最终验证
    const isCanvasVisible = await canvasArea.isVisible();
    const isToolbarVisible = await toolbar.isVisible();
    const isComponentLibraryVisible = await componentLibrary.isVisible();
    
    console.log('📊 最终状态:');
    console.log('  - 画布区域可见:', isCanvasVisible);
    console.log('  - 工具栏可见:', isToolbarVisible);
    console.log('  - 组件库可见:', isComponentLibraryVisible);
    
    // 如果所有元素都可见，测试通过
    if (isCanvasVisible && isToolbarVisible && isComponentLibraryVisible) {
      console.log('✅ 页面加载成功');
    } else {
      console.log('❌ 页面加载不完整');
      
      // 截图保存调试信息
      await page.screenshot({ path: 'e2e/reports/debug-page-load.png', fullPage: true });
      console.log('📸 已保存调试截图: e2e/reports/debug-page-load.png');
    }
  });

  test('测试简化的页面加载', async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);
    
    // 直接导航到页面，不使用FormEditorPage的复杂逻辑
    console.log('🔍 直接导航到表单编辑器页面');
    await page.goto('/special_work/config/form_config/1');
    
    // 等待页面基本加载
    await page.waitForLoadState('domcontentloaded');
    console.log('✅ DOM内容已加载');
    
    // 简单检查页面是否有内容
    const bodyText = await page.locator('body').textContent();
    const hasContent = bodyText && bodyText.length > 100;
    
    console.log('🔍 页面内容长度:', bodyText?.length || 0);
    console.log('🔍 页面有内容:', hasContent);
    
    // 检查是否被重定向
    const currentUrl = page.url();
    const isOnFormEditor = currentUrl.includes('form_config');
    
    console.log('🔍 当前URL:', currentUrl);
    console.log('🔍 在表单编辑器页面:', isOnFormEditor);
    
    expect(isOnFormEditor).toBe(true);
    expect(hasContent).toBe(true);
  });
});
