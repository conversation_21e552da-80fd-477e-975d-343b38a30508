import { test, expect } from '@playwright/test';
import { AuthHelpers } from '../../utils/test-helpers';
import { FormEditorPage } from '../../pages/FormEditorPage';

test.describe('画布结构调试测试', () => {
  test('调试画布DOM结构和拖拽阻挡问题', async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);
    
    // 初始化表单编辑器页面对象
    const formEditorPage = new FormEditorPage(page);
    
    // 导航到表单编辑器页面
    await formEditorPage.goto();
    await formEditorPage.waitForEditorLoad();
    
    console.log('✅ 页面加载完成，开始调试画布结构');
    
    // 1. 检查画布区域的DOM结构
    const canvasArea = page.locator('.form-sandbox__content');
    const canvasHTML = await canvasArea.innerHTML();
    console.log('🔍 画布区域HTML结构:');
    console.log(canvasHTML.substring(0, 1000));
    
    // 2. 检查是否有阻挡元素
    const overlayElements = await page.locator('div[style*="absolute"]').all();
    console.log('🔍 绝对定位元素数量:', overlayElements.length);
    
    for (let i = 0; i < Math.min(overlayElements.length, 5); i++) {
      const style = await overlayElements[i].getAttribute('style');
      const className = await overlayElements[i].getAttribute('class');
      const zIndex = await overlayElements[i].evaluate(el => window.getComputedStyle(el).zIndex);
      console.log(`  元素 ${i + 1}: z-index=${zIndex}, class="${className}", style="${style}"`);
    }
    
    // 3. 检查z-index高的元素
    const highZIndexElements = await page.locator('[style*="z-10"], [class*="z-10"]').all();
    console.log('🔍 高z-index元素数量:', highZIndexElements.length);
    
    for (let i = 0; i < highZIndexElements.length; i++) {
      const element = highZIndexElements[i];
      const tagName = await element.evaluate(el => el.tagName);
      const className = await element.getAttribute('class');
      const textContent = await element.textContent();
      console.log(`  高z-index元素 ${i + 1}: ${tagName} class="${className}" text="${textContent?.substring(0, 50)}"`);
    }
    
    // 4. 检查画布中的组件（尝试不同的选择器）
    const possibleComponentSelectors = [
      '[data-testid^="canvas-component-"]',
      '.form-component',
      '.canvas-component', 
      '[data-component-id]',
      '.form-sandbox__content > div',
      '.form-sandbox__content [draggable]',
      '.form-sandbox__content .component'
    ];
    
    console.log('🔍 测试可能的组件选择器:');
    for (const selector of possibleComponentSelectors) {
      try {
        const elements = await page.locator(selector).all();
        console.log(`  ✅ 选择器 "${selector}": ${elements.length}个元素`);
        
        if (elements.length > 0) {
          const firstElement = elements[0];
          const tagName = await firstElement.evaluate(el => el.tagName);
          const className = await firstElement.getAttribute('class');
          const textContent = await firstElement.textContent();
          console.log(`    第一个元素: ${tagName} class="${className}" text="${textContent?.substring(0, 30)}"`);
        }
      } catch (error) {
        console.log(`  ❌ 选择器错误 "${selector}": ${error.message}`);
      }
    }
    
    // 5. 检查组件按钮是否可以点击
    const inputButton = page.locator('button[comp-type="input"]');
    const isButtonVisible = await inputButton.isVisible();
    const isButtonEnabled = await inputButton.isEnabled();
    console.log('🔍 输入框按钮状态:');
    console.log(`  可见: ${isButtonVisible}`);
    console.log(`  启用: ${isButtonEnabled}`);
    
    if (isButtonVisible) {
      const buttonBox = await inputButton.boundingBox();
      console.log(`  位置: x=${buttonBox?.x}, y=${buttonBox?.y}, width=${buttonBox?.width}, height=${buttonBox?.height}`);
    }
    
    // 6. 检查画布区域是否可以接收拖拽
    const canvasBox = await canvasArea.boundingBox();
    console.log('🔍 画布区域状态:');
    console.log(`  位置: x=${canvasBox?.x}, y=${canvasBox?.y}, width=${canvasBox?.width}, height=${canvasBox?.height}`);
    
    // 7. 尝试简单的点击操作
    console.log('🔍 尝试点击组件按钮...');
    try {
      await inputButton.click();
      console.log('✅ 组件按钮点击成功');
      
      // 等待一下看是否有变化
      await page.waitForTimeout(2000);
      
      // 再次检查画布中的组件
      for (const selector of possibleComponentSelectors) {
        try {
          const elements = await page.locator(selector).all();
          if (elements.length > 0) {
            console.log(`  点击后 "${selector}": ${elements.length}个元素`);
          }
        } catch (error) {
          // 忽略错误
        }
      }
    } catch (error) {
      console.log('❌ 组件按钮点击失败:', error.message);
    }
    
    // 8. 截图保存调试信息
    await page.screenshot({ path: 'e2e/reports/debug-canvas-structure.png', fullPage: true });
    console.log('📸 已保存调试截图: e2e/reports/debug-canvas-structure.png');
  });
});
