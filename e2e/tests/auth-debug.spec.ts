import { expect, test } from "@playwright/test";
import { AuthHelpers } from "../utils/test-helpers";

test.describe("认证调试测试", () => {
  test("调试路由守卫行为", async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);

    // 调试：检查localStorage中的数据
    const authData = await page.evaluate(() => {
      const userInfo = localStorage.getItem("vr-userinfo");
      return userInfo ? JSON.parse(userInfo) : null;
    });
    console.log("🔍 localStorage认证数据:", authData);

    // 尝试访问需要认证的页面（关于页面，简单页面）
    console.log("🔍 尝试访问 /system/about");

    // 监听页面导航事件
    page.on("framenavigated", (frame) => {
      console.log("🔍 页面导航到:", frame.url());
    });

    await page.goto("/system/about", { timeout: 30000 });

    // 立即检查localStorage是否还存在
    const authDataAfterGoto = await page.evaluate(() => {
      const userInfo = localStorage.getItem("vr-userinfo");
      return userInfo ? JSON.parse(userInfo) : null;
    });
    console.log("🔍 goto后立即检查localStorage:", authDataAfterGoto);

    // 等待一段时间观察重定向
    await page.waitForTimeout(3000);

    // 检查当前URL
    const currentUrl = page.url();
    console.log("🔍 当前URL:", currentUrl);

    // 如果被重定向到登录页面，检查URL参数
    if (currentUrl.includes("/login")) {
      const url = new URL(currentUrl);
      const msg = url.searchParams.get("msg");
      console.log("🔍 登录页面消息:", decodeURIComponent(msg || ""));

      // 检查控制台日志，看看路由守卫的调试信息
      const logs = await page.evaluate(() => {
        // 尝试获取一些调试信息
        const userInfo = localStorage.getItem("vr-userinfo");
        const data = userInfo ? JSON.parse(userInfo) : null;
        return {
          hasUserInfo: !!data,
          hasToken: !!data?.token,
          hasUserInfoId: !!data?.userInfo?.id,
          userId: data?.userInfo?.id,
          employeeId: data?.userInfo?.employeeId,
          userName: data?.userInfo?.name,
          tokenPrefix: data?.token?.substring(0, 20),
        };
      });
      console.log("🔍 认证状态检查:", logs);

      // 检查页面控制台日志
      const consoleLogs = await page.evaluate(() => {
        // 返回一些可能有用的信息
        return {
          pathname: window.location.pathname,
          localStorage: Object.keys(localStorage),
        };
      });
      console.log("🔍 页面状态:", consoleLogs);
    }

    console.log("🔍 认证调试测试完成，当前URL:", currentUrl);
  });

  test("测试白名单路径访问", async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);

    // 尝试访问白名单路径（登录页面应该总是可以访问）
    console.log("🔍 尝试访问白名单路径 /login");
    await page.goto("/login", { timeout: 30000 });

    await page.waitForTimeout(2000);

    const currentUrl = page.url();
    console.log("🔍 访问登录页面后的URL:", currentUrl);

    // 登录页面应该可以正常访问
    expect(currentUrl).toContain("/login");
  });

  test("测试根路径访问", async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);

    // 尝试访问根路径
    console.log("🔍 尝试访问根路径 /");
    await page.goto("/", { timeout: 30000 });

    await page.waitForTimeout(3000);

    const currentUrl = page.url();
    console.log("🔍 访问根路径后的URL:", currentUrl);
  });
});
