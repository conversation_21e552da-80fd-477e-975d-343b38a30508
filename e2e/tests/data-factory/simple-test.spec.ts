import { test, expect } from '@playwright/test';
import { ComponentDataFactory, COMPONENT_TYPES } from '../../fixtures';

test.describe('简单测试数据工厂验证', () => {
  test('应该能够创建输入框组件', () => {
    const inputComponent = ComponentDataFactory.createInputComponent({
      label: '测试输入框',
      required: true,
    });

    expect(inputComponent).toHaveProperty('compId');
    expect(inputComponent).toHaveProperty('compName');
    expect(inputComponent).toHaveProperty('compType');
    expect(inputComponent.compType).toBe(COMPONENT_TYPES.INPUT);
    expect(inputComponent.formData.label).toBe('测试输入框');
    expect(inputComponent.formData.required).toBe(true);
  });

  test('应该能够创建所有组件类型', () => {
    const componentTypes = Object.values(COMPONENT_TYPES);
    
    componentTypes.forEach(type => {
      const component = ComponentDataFactory.createComponentByType(type);
      expect(component.compType).toBe(type);
      expect(component.compId).toMatch(new RegExp(`^${type}_`));
    });
  });
});
