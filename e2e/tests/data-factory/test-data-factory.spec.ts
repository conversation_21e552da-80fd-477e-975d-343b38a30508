import { test, expect } from '@playwright/test';
import {
  ComponentDataFactory,
  FormTemplateFactory,
  UserScenarioFactory,
  TestUserFactory,
  TestDataFactory,
  TestDataValidator,
  ValidationHelpers,
  RandomDataHelpers,
  COMPONENT_TYPES,
} from '../../fixtures';

test.describe('测试数据工厂验证', () => {
  test.describe('ComponentDataFactory 组件数据工厂', () => {
    test('应该能够创建所有类型的组件数据', () => {
      const componentTypes = Object.values(COMPONENT_TYPES);
      
      componentTypes.forEach(type => {
        const component = ComponentDataFactory.createComponentByType(type);
        
        // 验证基础结构
        expect(component).toHaveProperty('compId');
        expect(component).toHaveProperty('compName');
        expect(component).toHaveProperty('compType');
        expect(component).toHaveProperty('group');
        expect(component).toHaveProperty('formData');
        
        // 验证类型正确
        expect(component.compType).toBe(type);
        expect(component.compId).toMatch(new RegExp(`^${type}_`));
        
        // 验证数据完整性
        expect(ValidationHelpers.validateComponentData(component)).toBe(true);
      });
    });

    test('应该能够创建带自定义属性的组件', () => {
      const customInput = ComponentDataFactory.createInputComponent({
        label: '自定义标签',
        placeholder: '自定义占位符',
        required: false,
        maxLength: 100,
      });

      expect(customInput.formData.label).toBe('自定义标签');
      expect(customInput.formData.placeholder).toBe('自定义占位符');
      expect(customInput.formData.required).toBe(false);
      expect(customInput.formData.maxLength).toBe(100);
    });

    test('应该能够批量创建多个组件', () => {
      const componentConfigs = [
        { type: COMPONENT_TYPES.INPUT, properties: { label: '姓名' } },
        { type: COMPONENT_TYPES.SELECT, properties: { label: '部门' } },
        { type: COMPONENT_TYPES.TEXTAREA, properties: { label: '备注' } },
      ];

      const components = ComponentDataFactory.createMultipleComponents(componentConfigs);
      
      expect(components).toHaveLength(3);
      expect(components[0].formData.label).toBe('姓名');
      expect(components[1].formData.label).toBe('部门');
      expect(components[2].formData.label).toBe('备注');
    });

    test('应该为不支持的组件类型抛出错误', () => {
      expect(() => {
        ComponentDataFactory.createComponentByType('unsupported_type');
      }).toThrow('Unsupported component type: unsupported_type');
    });
  });

  test.describe('FormTemplateFactory 表单模板工厂', () => {
    test('应该能够创建所有预定义的表单模板', () => {
      const templates = FormTemplateFactory.getAllTemplates();
      
      expect(templates.length).toBeGreaterThan(0);
      
      templates.forEach(template => {
        // 验证基础结构
        expect(template).toHaveProperty('id');
        expect(template).toHaveProperty('title');
        expect(template).toHaveProperty('components');
        expect(template).toHaveProperty('layout');
        expect(template).toHaveProperty('validation');
        expect(template).toHaveProperty('metadata');
        
        // 验证组件数组
        expect(Array.isArray(template.components)).toBe(true);
        expect(template.components.length).toBeGreaterThan(0);
        
        // 验证数据完整性
        expect(ValidationHelpers.validateFormTemplate(template)).toBe(true);
      });
    });

    test('应该能够按难度级别筛选模板', () => {
      const simpleTemplates = FormTemplateFactory.getTemplatesByDifficulty('simple');
      const complexTemplates = FormTemplateFactory.getTemplatesByDifficulty('complex');
      
      expect(simpleTemplates.length).toBeGreaterThan(0);
      expect(complexTemplates.length).toBeGreaterThan(0);
      
      simpleTemplates.forEach(template => {
        expect(template.metadata.difficulty).toBe('simple');
      });
      
      complexTemplates.forEach(template => {
        expect(template.metadata.difficulty).toBe('complex');
      });
    });

    test('应该能够按分类筛选模板', () => {
      const allTemplates = FormTemplateFactory.getAllTemplates();
      const categories = [...new Set(allTemplates.map(t => t.metadata.category))];
      
      categories.forEach(category => {
        const categoryTemplates = FormTemplateFactory.getTemplatesByCategory(category);
        expect(categoryTemplates.length).toBeGreaterThan(0);
        
        categoryTemplates.forEach(template => {
          expect(template.metadata.category).toBe(category);
        });
      });
    });

    test('应该能够根据ID获取特定模板', () => {
      const simpleForm = FormTemplateFactory.getTemplateById('simple_contact_form');
      expect(simpleForm).toBeDefined();
      expect(simpleForm?.id).toBe('simple_contact_form');
      
      const nonExistent = FormTemplateFactory.getTemplateById('non_existent');
      expect(nonExistent).toBeUndefined();
    });
  });

  test.describe('UserScenarioFactory 用户场景工厂', () => {
    test('应该能够创建所有预定义的用户场景', () => {
      const scenarios = UserScenarioFactory.getAllScenarios();
      
      expect(scenarios.length).toBeGreaterThan(0);
      
      scenarios.forEach(scenario => {
        // 验证基础结构
        expect(scenario).toHaveProperty('id');
        expect(scenario).toHaveProperty('name');
        expect(scenario).toHaveProperty('actions');
        expect(scenario).toHaveProperty('expectedOutcome');
        
        // 验证操作数组
        expect(Array.isArray(scenario.actions)).toBe(true);
        expect(scenario.actions.length).toBeGreaterThan(0);
        
        // 验证数据完整性
        expect(ValidationHelpers.validateUserScenario(scenario)).toBe(true);
      });
    });

    test('应该能够按分类筛选场景', () => {
      const basicScenarios = UserScenarioFactory.getScenariosByCategory('basic');
      const advancedScenarios = UserScenarioFactory.getScenariosByCategory('advanced');
      
      expect(basicScenarios.length).toBeGreaterThan(0);
      expect(advancedScenarios.length).toBeGreaterThan(0);
      
      basicScenarios.forEach(scenario => {
        expect(scenario.category).toBe('basic');
      });
      
      advancedScenarios.forEach(scenario => {
        expect(scenario.category).toBe('advanced');
      });
    });

    test('应该能够按优先级筛选场景', () => {
      const highPriorityScenarios = UserScenarioFactory.getScenariosByPriority('high');
      
      expect(highPriorityScenarios.length).toBeGreaterThan(0);
      
      highPriorityScenarios.forEach(scenario => {
        expect(scenario.priority).toBe('high');
      });
    });

    test('应该能够获取核心场景', () => {
      const essentialScenarios = UserScenarioFactory.getEssentialScenarios();
      
      expect(essentialScenarios.length).toBeGreaterThan(0);
      
      essentialScenarios.forEach(scenario => {
        expect(scenario.priority).toBe('high');
        expect(scenario.category).toBe('basic');
      });
    });
  });

  test.describe('TestUserFactory 测试用户工厂', () => {
    test('应该能够创建不同角色的测试用户', () => {
      const admin = TestUserFactory.createAdminUser();
      const editor = TestUserFactory.createEditorUser();
      const viewer = TestUserFactory.createViewerUser();
      
      expect(admin.role).toBe('admin');
      expect(editor.role).toBe('editor');
      expect(viewer.role).toBe('viewer');
      
      // 验证权限差异
      expect(admin.permissions.length).toBeGreaterThan(editor.permissions.length);
      expect(editor.permissions.length).toBeGreaterThan(viewer.permissions.length);
      
      // 验证管理员有所有权限
      expect(admin.permissions).toContain('delete');
      expect(admin.permissions).toContain('manage');
      
      // 验证查看者只有读权限
      expect(viewer.permissions).toEqual(['read']);
    });

    test('应该能够获取所有测试用户', () => {
      const users = TestUserFactory.getAllUsers();
      
      expect(users.length).toBe(3);
      
      const roles = users.map(u => u.role);
      expect(roles).toContain('admin');
      expect(roles).toContain('editor');
      expect(roles).toContain('viewer');
    });
  });

  test.describe('RandomDataHelpers 随机数据工具', () => {
    test('应该能够生成随机字符串', () => {
      const str1 = RandomDataHelpers.randomString(10);
      const str2 = RandomDataHelpers.randomString(10);
      
      expect(str1).toHaveLength(10);
      expect(str2).toHaveLength(10);
      expect(str1).not.toBe(str2); // 应该不相同
    });

    test('应该能够生成有效的随机邮箱', () => {
      const email = RandomDataHelpers.randomEmail();
      
      expect(ValidationHelpers.isValidEmail(email)).toBe(true);
      expect(email).toMatch(/@/);
    });

    test('应该能够生成有效的随机手机号', () => {
      const phone = RandomDataHelpers.randomPhone();
      
      expect(ValidationHelpers.isValidPhone(phone)).toBe(true);
      expect(phone).toHaveLength(11);
      expect(phone).toMatch(/^1[3-9]/);
    });

    test('应该能够生成随机中文姓名', () => {
      const name = RandomDataHelpers.randomChineseName();
      
      expect(name).toHaveLength(2);
      expect(/[\u4e00-\u9fa5]/.test(name)).toBe(true); // 包含中文字符
    });

    test('应该能够从数组中随机选择', () => {
      const array = ['a', 'b', 'c', 'd', 'e'];
      const choice = RandomDataHelpers.randomChoice(array);
      
      expect(array).toContain(choice);
    });

    test('应该能够从数组中随机选择多个元素', () => {
      const array = ['a', 'b', 'c', 'd', 'e'];
      const choices = RandomDataHelpers.randomChoices(array, 3);
      
      expect(choices).toHaveLength(3);
      choices.forEach(choice => {
        expect(array).toContain(choice);
      });
      
      // 验证没有重复
      const uniqueChoices = [...new Set(choices)];
      expect(uniqueChoices).toHaveLength(choices.length);
    });
  });

  test.describe('TestDataFactory 主工厂', () => {
    test('应该能够获取快速测试数据', () => {
      const quickData = TestDataFactory.getQuickTestData();
      
      expect(quickData).toHaveProperty('simpleInput');
      expect(quickData).toHaveProperty('simpleForm');
      expect(quickData).toHaveProperty('basicScenario');
      expect(quickData).toHaveProperty('testUser');
      expect(quickData).toHaveProperty('randomData');
      
      // 验证数据类型
      expect(ValidationHelpers.validateComponentData(quickData.simpleInput)).toBe(true);
      expect(ValidationHelpers.validateFormTemplate(quickData.simpleForm)).toBe(true);
      expect(ValidationHelpers.validateUserScenario(quickData.basicScenario)).toBe(true);
    });

    test('应该能够获取完整测试数据', () => {
      const completeData = TestDataFactory.getCompleteTestData();
      
      expect(completeData).toHaveProperty('components');
      expect(completeData).toHaveProperty('templates');
      expect(completeData).toHaveProperty('scenarios');
      expect(completeData).toHaveProperty('users');
      expect(completeData).toHaveProperty('categorized');
      
      // 验证组件数据
      const componentTypes = Object.keys(completeData.components);
      expect(componentTypes.length).toBe(Object.values(COMPONENT_TYPES).length);
    });

    test('应该能够创建自定义测试数据', () => {
      const customData = TestDataFactory.createCustomTestData({
        componentCount: 3,
        componentTypes: [COMPONENT_TYPES.INPUT, COMPONENT_TYPES.SELECT],
        formComplexity: 'simple',
        includeValidation: true,
        includeRandomData: true,
      });
      
      expect(customData.components).toHaveLength(3);
      expect(customData.template.layout.columns).toBe(1); // simple = 1 column
      expect(customData.randomData).toBeDefined();
      
      // 验证组件类型限制
      customData.components.forEach(component => {
        expect([COMPONENT_TYPES.INPUT, COMPONENT_TYPES.SELECT]).toContain(component.compType);
      });
    });
  });

  test.describe('TestDataValidator 数据验证器', () => {
    test('应该能够验证所有测试数据的完整性', () => {
      const validation = TestDataValidator.validateAllTestData();
      
      expect(validation).toHaveProperty('isValid');
      expect(validation).toHaveProperty('errors');
      
      if (!validation.isValid) {
        console.log('验证错误:', validation.errors);
      }
      
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    test('应该能够生成测试数据报告', () => {
      const report = TestDataValidator.generateTestDataReport();
      
      expect(report).toContain('测试数据工厂报告');
      expect(report).toContain('数据统计');
      expect(report).toContain('组件类型');
      expect(report).toContain('表单模板');
      expect(report).toContain('用户场景');
      expect(report).toContain('验证结果');
    });
  });
});
