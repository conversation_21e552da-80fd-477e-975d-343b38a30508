import { test, expect } from '@playwright/test';

/**
 * 简化的环境验证测试
 * 只测试核心功能确保E2E环境正常工作
 */
test.describe('Simple Environment Verification', () => {
  test('应用页面能够正常加载', async ({ page }) => {
    // 导航到应用首页
    await page.goto('/');
    
    // 验证页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容存在
    const body = page.locator('body');
    await expect(body).toBeVisible();
    
    // 验证页面标题存在
    await expect(page).toHaveTitle(/.*/, { timeout: 10000 });
  });

  test('基本JavaScript功能正常', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // 测试JavaScript执行
    const result = await page.evaluate(() => {
      return typeof window !== 'undefined' && typeof document !== 'undefined';
    });
    expect(result).toBe(true);
    
    // 测试本地存储
    await page.evaluate(() => {
      localStorage.setItem('e2e-test-key', 'e2e-test-value');
    });
    
    const storageValue = await page.evaluate(() => {
      return localStorage.getItem('e2e-test-key');
    });
    expect(storageValue).toBe('e2e-test-value');
    
    // 清理测试数据
    await page.evaluate(() => {
      localStorage.removeItem('e2e-test-key');
    });
  });

  test('页面响应正常', async ({ page }) => {
    const response = await page.goto('/');
    
    // 验证响应状态
    expect(response?.status()).toBe(200);
    
    // 验证页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 验证页面内容
    const body = page.locator('body');
    await expect(body).toBeVisible();
  });
});
