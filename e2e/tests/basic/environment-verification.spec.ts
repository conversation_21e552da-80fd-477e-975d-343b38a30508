import { expect, test } from "@playwright/test";

/**
 * 环境验证测试
 * 确保E2E测试环境正常工作
 */
test.describe("Environment Verification", () => {
  test.beforeEach(async ({ page }) => {
    // 导航到应用首页
    await page.goto("/");
  });

  test("应用页面能够正常加载", async ({ page }) => {
    // 验证页面标题
    await expect(page).toHaveTitle(/.*/, { timeout: 10000 });

    // 验证页面加载完成
    await page.waitForLoadState("networkidle");

    // 验证页面内容存在
    const body = page.locator("body");
    await expect(body).toBeVisible();
  });

  test("浏览器基本功能正常", async ({ page }) => {
    // 设置更长的超时时间
    test.setTimeout(60000);

    // 测试页面导航
    await page.goto("/", { waitUntil: "domcontentloaded" });
    expect(page.url()).toContain("localhost:5173");

    // 等待页面基本加载完成
    await page.waitForTimeout(3000);

    // 测试JavaScript执行 - 分步骤进行，避免长时间阻塞
    const jsTest = await page.evaluate(() => {
      return typeof window !== "undefined" && typeof document !== "undefined";
    });
    expect(jsTest).toBe(true);

    // 测试本地存储 - 分步骤进行
    await page.evaluate(() => {
      localStorage.setItem("test-key", "test-value");
    });

    // 等待一下确保存储操作完成
    await page.waitForTimeout(500);

    const storageValue = await page.evaluate(() => {
      return localStorage.getItem("test-key");
    });
    expect(storageValue).toBe("test-value");

    // 清理测试数据
    await page.evaluate(() => {
      localStorage.removeItem("test-key");
    });
  });

  test("网络请求功能正常", async ({ page }) => {
    // 监听网络请求
    const responses: string[] = [];

    page.on("response", (response) => {
      responses.push(response.url());
    });

    // 导航到页面，触发网络请求
    await page.goto("/");
    await page.waitForLoadState("networkidle");

    // 验证有网络请求发生
    expect(responses.length).toBeGreaterThan(0);

    // 验证主要资源加载成功
    const mainPageResponse = responses.find((url) =>
      url.includes("localhost:5173")
    );
    expect(mainPageResponse).toBeDefined();
  });

  test("控制台无严重错误", async ({ page }) => {
    const consoleErrors: string[] = [];

    // 监听控制台错误
    page.on("console", (msg) => {
      if (msg.type() === "error") {
        consoleErrors.push(msg.text());
      }
    });

    // 监听页面错误
    page.on("pageerror", (error) => {
      consoleErrors.push(error.message);
    });

    // 导航到页面
    await page.goto("/", { waitUntil: "domcontentloaded" });

    // 等待一段时间确保所有异步操作完成
    await page.waitForTimeout(5000);

    // 过滤掉预期的错误（这些在开发环境中是正常的）
    const seriousErrors = consoleErrors.filter(
      (error) =>
        !error.includes("Warning") &&
        !error.includes("DevTools") &&
        !error.includes("Extension") &&
        !error.includes("401 (Unauthorized)") && // API未授权是预期的
        !error.includes("404 (Not Found)") && // 某些资源404是预期的
        !error.includes("503") && // 服务不可用是预期的
        !error.includes("queryKey needs to be an Array") && // React Query警告
        !error.includes("Failed to load resource") // 资源加载失败是预期的
    );

    if (seriousErrors.length > 0) {
      console.log("Serious console errors found:", seriousErrors);
    }

    // CI环境中应该没有严重错误
    if (process.env.CI) {
      expect(seriousErrors.length).toBe(0);
    } else {
      // 本地开发环境允许少量错误
      expect(seriousErrors.length).toBeLessThanOrEqual(5);
    }
  });

  test("响应式设计基本功能", async ({ page }) => {
    // 测试桌面视口
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.goto("/");
    await page.waitForLoadState("networkidle");

    const desktopBody = page.locator("body");
    await expect(desktopBody).toBeVisible();

    // 测试平板视口
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500); // 等待响应式调整

    await expect(desktopBody).toBeVisible();

    // 测试移动视口
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500); // 等待响应式调整

    await expect(desktopBody).toBeVisible();
  });

  test("基本交互功能正常", async ({ page }) => {
    await page.goto("/");
    await page.waitForLoadState("networkidle");

    // 测试点击事件
    const clickableElements = await page
      .locator('button, a, [role="button"]')
      .all();

    if (clickableElements.length > 0) {
      // 测试第一个可点击元素
      const firstElement = clickableElements[0];
      await expect(firstElement).toBeVisible();

      // 验证元素可以接收焦点
      await firstElement.focus();
      await expect(firstElement).toBeFocused();
    }

    // 测试键盘导航
    await page.keyboard.press("Tab");

    // 验证页面可以滚动（如果页面有足够内容）
    const bodyHeight = await page.evaluate(() => document.body.scrollHeight);
    const windowHeight = await page.evaluate(() => window.innerHeight);

    if (bodyHeight > windowHeight) {
      await page.evaluate(() => {
        window.scrollTo(0, 100);
      });

      const scrollY = await page.evaluate(() => window.scrollY);
      expect(scrollY).toBeGreaterThan(0);
    } else {
      // 如果页面内容不足以滚动，跳过滚动测试
      console.log("页面内容不足以滚动，跳过滚动测试");
    }
  });
});
