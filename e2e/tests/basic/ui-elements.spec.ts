/**
 * UI元素可见性测试
 * 测试关键UI元素的可见性和基本交互
 */

import { expect, test } from "@playwright/test";
import { ErrorHelpers, WaitHelpers } from "../../fixtures";

test.describe("UI元素可见性测试", () => {
  test.beforeEach(async ({ page }) => {
    // 设置错误捕获
    ErrorHelpers.setupErrorCapture(page);

    // 导航到登录页面（公开页面，无需认证）
    await page.goto("/login", { timeout: 30000 });

    // 使用宽松的页面加载等待策略，避免网络超时问题
    await WaitHelpers.waitForPageLoadLoose(page);
  });

  test("页面头部元素正常显示", async ({ page }) => {
    // 查找页面头部区域
    const header = page.locator("header, .header, .top-bar, .navbar").first();

    const headerCount = await header.count();
    if (headerCount > 0) {
      await expect(header).toBeVisible();

      // 查找Logo
      const logo = header.locator('img, .logo, [data-testid="logo"]').first();
      const logoCount = await logo.count();
      if (logoCount > 0) {
        await expect(logo).toBeVisible();
      }

      // 查找标题
      const title = header.locator("h1, .title, .app-title, a").first();
      const titleCount = await title.count();
      if (titleCount > 0) {
        await expect(title).toBeVisible();
      }
    }
  });

  test("主要布局容器正常显示", async ({ page }) => {
    // 验证主要布局容器
    const mainContainer = page
      .locator("main, .main, .container, .app, #root")
      .first();
    await expect(mainContainer).toBeVisible();

    // 验证内容区域
    const content = page
      .locator(".content, .main-content, .page-content")
      .first();
    const contentCount = await content.count();
    if (contentCount > 0) {
      await expect(content).toBeVisible();
    }
  });

  test("导航菜单元素正常显示", async ({ page }) => {
    // 等待页面完全稳定
    await page.waitForTimeout(2000);

    // 查找导航菜单（扩展选择器以覆盖更多可能的导航元素）
    const nav = page
      .locator(
        "nav, .nav, .navigation, .menu, .sidebar, .header-nav, .main-nav, .top-nav"
      )
      .first();

    const navCount = await nav.count();
    if (navCount > 0) {
      // 等待导航元素稳定
      await expect(nav).toBeVisible({ timeout: 10000 });

      // 验证菜单项
      const menuItems = nav.locator("a, button, .menu-item, .nav-item, li");
      const itemCount = await menuItems.count();

      if (itemCount > 0) {
        // 验证至少有一个菜单项可见
        await expect(menuItems.first()).toBeVisible({ timeout: 5000 });

        // 验证菜单项包含文本或图标
        const firstItemText = await menuItems.first().textContent();
        const hasIcon =
          (await menuItems.first().locator("svg, i, .icon").count()) > 0;

        expect(firstItemText?.trim() || hasIcon).toBeTruthy();
      }
    } else {
      // 如果没有找到导航菜单，检查是否有其他导航形式
      const alternativeNav = page
        .locator(".header, .toolbar, .app-bar")
        .first();
      const altNavCount = await alternativeNav.count();

      if (altNavCount > 0) {
        await expect(alternativeNav).toBeVisible({ timeout: 5000 });
        console.log("找到替代导航元素");
      } else {
        console.log("未找到导航菜单，可能是单页面应用");
      }
    }
  });

  test("表单元素正常显示和交互", async ({ page }) => {
    // 查找页面中的表单
    const forms = page.locator("form, .form, .search-form");
    const formCount = await forms.count();

    if (formCount > 0) {
      const form = forms.first();
      await expect(form).toBeVisible();

      // 查找输入框
      const inputs = form.locator("input, textarea, select");
      const inputCount = await inputs.count();

      if (inputCount > 0) {
        const firstInput = inputs.first();
        await expect(firstInput).toBeVisible();

        // 测试输入框交互
        const inputType = await firstInput.getAttribute("type");
        if (inputType !== "hidden" && inputType !== "submit") {
          await firstInput.click();
          await expect(firstInput).toBeFocused();

          // 如果是文本输入框，测试输入
          if (!inputType || inputType === "text" || inputType === "search") {
            await firstInput.fill("测试输入");
            await expect(firstInput).toHaveValue("测试输入");
            await firstInput.clear();
          }
        }
      }

      // 查找按钮
      const buttons = form.locator(
        'button, input[type="submit"], input[type="button"]'
      );
      const buttonCount = await buttons.count();

      if (buttonCount > 0) {
        const firstButton = buttons.first();
        await expect(firstButton).toBeVisible();
        await expect(firstButton).toBeEnabled();
      }
    }
  });

  test("按钮元素正常显示和交互", async ({ page }) => {
    // 查找页面中的按钮
    const buttons = page.locator(
      'button, .btn, input[type="button"], input[type="submit"]'
    );
    const buttonCount = await buttons.count();

    if (buttonCount > 0) {
      // 测试前几个按钮
      const testCount = Math.min(3, buttonCount);

      for (let i = 0; i < testCount; i++) {
        const button = buttons.nth(i);

        // 验证按钮可见
        await expect(button).toBeVisible();

        // 验证按钮可点击（如果启用）
        const isEnabled = await button.isEnabled();
        if (isEnabled) {
          // 悬停测试
          await button.hover();

          // 验证按钮基本属性（某些按钮可能是纯样式按钮）
          const buttonText = await button.textContent();
          const hasIcon = (await button.locator("svg, i, .icon").count()) > 0;
          const hasTextContent = buttonText && buttonText.trim().length > 0;
          const hasBackgroundImage = await button.evaluate((el) => {
            const style = window.getComputedStyle(el);
            return style.backgroundImage !== "none";
          });
          const hasSize = await button.evaluate((el) => {
            const rect = el.getBoundingClientRect();
            return rect.width > 0 && rect.height > 0;
          });

          // 按钮应该有文本内容、图标、背景图片或至少有尺寸（可见）
          expect(
            hasTextContent || hasIcon || hasBackgroundImage || hasSize
          ).toBeTruthy();
        }
      }
    }
  });

  test("链接元素正常显示和交互", async ({ page }) => {
    // 查找页面中的链接
    const links = page.locator("a[href]");
    const linkCount = await links.count();

    if (linkCount > 0) {
      // 测试前几个链接
      const testCount = Math.min(3, linkCount);

      for (let i = 0; i < testCount; i++) {
        const link = links.nth(i);

        // 验证链接可见
        await expect(link).toBeVisible();

        // 验证链接有href属性
        const href = await link.getAttribute("href");
        expect(href).toBeTruthy();

        // 验证链接有文本内容
        const linkText = await link.textContent();
        expect(linkText?.trim()).toBeTruthy();

        // 悬停测试
        await link.hover();
      }
    }
  });

  test("图片元素正常加载", async ({ page }) => {
    // 查找页面中的图片
    const images = page.locator("img");
    const imageCount = await images.count();

    if (imageCount > 0) {
      // 测试前几个图片
      const testCount = Math.min(3, imageCount);

      for (let i = 0; i < testCount; i++) {
        const img = images.nth(i);

        // 验证图片可见
        await expect(img).toBeVisible();

        // 验证图片有src属性
        const src = await img.getAttribute("src");
        expect(src).toBeTruthy();

        // 验证图片加载成功
        const naturalWidth = await img.evaluate(
          (el: HTMLImageElement) => el.naturalWidth
        );
        expect(naturalWidth).toBeGreaterThan(0);
      }
    }
  });

  test("表格元素正常显示", async ({ page }) => {
    // 查找页面中的表格
    const tables = page.locator("table, .table, .data-table");
    const tableCount = await tables.count();

    if (tableCount > 0) {
      const table = tables.first();
      await expect(table).toBeVisible();

      // 验证表头
      const headers = table.locator("th, .table-header, .header-cell");
      const headerCount = await headers.count();

      if (headerCount > 0) {
        await expect(headers.first()).toBeVisible();
      }

      // 验证表格行
      const rows = table.locator("tr, .table-row");
      const rowCount = await rows.count();

      if (rowCount > 0) {
        await expect(rows.first()).toBeVisible();
      }
    }
  });

  test("模态框和弹窗元素正常工作", async ({ page }) => {
    // 查找可能触发模态框的按钮
    const modalTriggers = page.locator(
      'button:has-text("添加"), button:has-text("新增"), button:has-text("创建"), .add-btn, .create-btn'
    );
    const triggerCount = await modalTriggers.count();

    if (triggerCount > 0) {
      const trigger = modalTriggers.first();

      // 点击触发按钮
      await trigger.click();

      // 等待模态框出现
      await page.waitForTimeout(1000);

      // 查找模态框
      const modal = page
        .locator('.modal, .dialog, .popup, [role="dialog"]')
        .first();
      const modalCount = await modal.count();

      if (modalCount > 0) {
        await expect(modal).toBeVisible();

        // 查找关闭按钮
        const closeBtn = modal
          .locator(
            'button:has-text("取消"), button:has-text("关闭"), .close-btn, [aria-label="Close"]'
          )
          .first();
        const closeBtnCount = await closeBtn.count();

        if (closeBtnCount > 0) {
          await closeBtn.click();
          await WaitHelpers.waitForDisappear(modal);
        } else {
          // 尝试按ESC键关闭
          await page.keyboard.press("Escape");
        }
      }
    }
  });

  test("加载状态和空状态正常显示", async ({ page }) => {
    // 刷新页面观察加载状态
    await page.reload();

    // 查找加载指示器
    const loadingIndicators = page.locator(
      '.loading, .spinner, .skeleton, [data-testid="loading"]'
    );
    const loadingCount = await loadingIndicators.count();

    // 等待页面加载完成
    await WaitHelpers.waitForPageLoad(page);

    // 查找空状态
    const emptyStates = page.locator(
      '.empty, .no-data, .empty-state, :has-text("暂无数据")'
    );
    const emptyCount = await emptyStates.count();

    // 至少应该有加载状态或内容
    const hasContent = (await page.locator("body *").count()) > 10;
    expect(hasContent).toBe(true);

    // 验证加载和空状态的存在性（用于调试）
    console.log(`加载指示器数量: ${loadingCount}, 空状态数量: ${emptyCount}`);
  });

  test("页面滚动和固定元素正常工作", async ({ page }) => {
    // 获取页面高度
    const pageHeight = await page.evaluate(() => document.body.scrollHeight);
    const viewportHeight = await page.evaluate(() => window.innerHeight);

    if (pageHeight > viewportHeight) {
      // 滚动到页面底部
      await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
      await page.waitForTimeout(500);

      // 验证滚动位置
      const scrollTop = await page.evaluate(() => window.pageYOffset);
      expect(scrollTop).toBeGreaterThan(0);

      // 滚动回顶部
      await page.evaluate(() => window.scrollTo(0, 0));
      await page.waitForTimeout(500);

      // 验证回到顶部
      const newScrollTop = await page.evaluate(() => window.pageYOffset);
      expect(newScrollTop).toBe(0);
    }

    // 查找固定定位元素
    const fixedElements = page.locator('[style*="position: fixed"], .fixed');
    const fixedCount = await fixedElements.count();

    if (fixedCount > 0) {
      // 验证固定元素在滚动时保持可见
      await expect(fixedElements.first()).toBeVisible();
    }
  });
});
