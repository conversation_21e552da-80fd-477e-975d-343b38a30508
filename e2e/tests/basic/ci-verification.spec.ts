import { expect, test } from "@playwright/test";

/**
 * CI环境专用验证测试
 * 专门为CI环境设计，确保稳定性和可靠性
 */
test.describe("CI Environment Verification", () => {
  test.beforeEach(async ({ page }) => {
    // 设置更长的超时时间
    test.setTimeout(90000);

    // 导航到应用首页
    await page.goto("/", {
      waitUntil: "domcontentloaded",
      timeout: 30000,
    });
  });

  test("应用能够在CI环境中正常启动", async ({ page }) => {
    // 验证页面基本加载
    await expect(page.locator("body")).toBeVisible({ timeout: 30000 });

    // 验证URL正确
    expect(page.url()).toContain("localhost:5173");

    // 验证页面标题存在
    await expect(page).toHaveTitle(/.*/, { timeout: 15000 });
  });

  test("JavaScript基础功能在CI环境中正常", async ({ page }) => {
    // 等待页面稳定
    await page.waitForTimeout(5000);

    // 使用更安全的方式测试JavaScript功能
    try {
      const jsWorking = await page.evaluate(() => {
        return (
          typeof window !== "undefined" &&
          typeof document !== "undefined" &&
          typeof localStorage !== "undefined"
        );
      });
      expect(jsWorking).toBe(true);
    } catch (error) {
      // 如果执行上下文被销毁，重新导航并重试
      console.log("执行上下文被销毁，重新导航...");
      await page.goto("/", { waitUntil: "domcontentloaded", timeout: 30000 });
      await page.waitForTimeout(3000);

      const jsWorking = await page.evaluate(() => {
        return (
          typeof window !== "undefined" &&
          typeof document !== "undefined" &&
          typeof localStorage !== "undefined"
        );
      });
      expect(jsWorking).toBe(true);
    }
  });

  test("网络请求在CI环境中能够发起", async ({ page }) => {
    const responses: string[] = [];

    // 监听网络请求
    page.on("response", (response) => {
      responses.push(response.url());
    });

    // 等待网络请求完成
    await page.waitForLoadState("networkidle", { timeout: 45000 });

    // 验证有网络请求发生
    expect(responses.length).toBeGreaterThan(0);
  });

  test("页面在不同视口下能够正常显示", async ({ page }) => {
    // 测试桌面视口
    await page.setViewportSize({ width: 1920, height: 1080 });
    await page.waitForTimeout(1000);
    await expect(page.locator("body")).toBeVisible();

    // 测试移动视口
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(1000);
    await expect(page.locator("body")).toBeVisible();
  });

  test("CI环境中无阻塞性错误", async ({ page }) => {
    const criticalErrors: string[] = [];

    // 只监听真正严重的错误
    page.on("pageerror", (error) => {
      // 过滤掉网络相关的错误（在CI中是预期的）
      if (
        !error.message.includes("fetch") &&
        !error.message.includes("network") &&
        !error.message.includes("401") &&
        !error.message.includes("404")
      ) {
        criticalErrors.push(error.message);
      }
    });

    // 等待页面完全加载
    await page.waitForTimeout(10000);

    // CI环境中不应该有阻塞性错误
    if (criticalErrors.length > 0) {
      console.log("Critical errors in CI:", criticalErrors);
    }

    expect(criticalErrors.length).toBe(0);
  });
});
