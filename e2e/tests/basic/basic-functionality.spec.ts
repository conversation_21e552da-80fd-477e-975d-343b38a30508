/**
 * 基础功能测试
 * 测试应用的核心功能和页面加载
 */

import { expect, test } from "@playwright/test";
import {
  AuthHelpers,
  ComponentDataFactory,
  ErrorHelpers,
  FormTemplateFactory,
  TestDataFactory,
  WaitHelpers,
} from "../../fixtures";

test.describe("基础功能测试", () => {
  test.beforeEach(async ({ page }) => {
    // 设置错误捕获
    ErrorHelpers.setupErrorCapture(page);
  });

  test("应用首页能够正常加载（需要认证）", async ({ page }) => {
    // 先登录获取认证状态
    await AuthHelpers.login(page);

    // 导航到应用首页，使用更宽松的等待策略
    await page.goto("/", {
      timeout: 60000,
      waitUntil: "domcontentloaded",
    });

    // 等待页面加载完成
    await WaitHelpers.waitForPageLoadLoose(page);

    // 验证没有被重定向到登录页面
    expect(page.url()).not.toContain("/login");

    // 验证页面标题
    await expect(page).toHaveTitle(/.*/, { timeout: 15000 });

    // 验证页面基本结构存在
    const body = page.locator("body");
    await expect(body).toBeVisible();

    // 验证页面内容已加载（应该是认证后的首页内容）
    const hasContent = await page.locator("body *").count();
    expect(hasContent).toBeGreaterThan(10);

    // 验证确实处于登录状态
    const isLoggedIn = await AuthHelpers.isLoggedIn(page);
    expect(isLoggedIn).toBe(true);
  });

  test("登录页面能够正常显示", async ({ page }) => {
    // 导航到登录页面
    await page.goto("/login", { timeout: 30000 });
    await WaitHelpers.waitForPageLoad(page);

    // 验证登录页面的关键元素
    const loginForm = page
      .locator('form, .login-form, [data-testid="login-form"]')
      .first();
    await expect(loginForm).toBeVisible({ timeout: 15000 });

    // 验证用户名输入框
    const usernameInput = page
      .locator(
        'input[type="text"], input[name="username"], input[placeholder*="用户"], input[placeholder*="账号"]'
      )
      .first();
    await expect(usernameInput).toBeVisible();

    // 验证密码输入框
    const passwordInput = page
      .locator(
        'input[type="password"], input[name="password"], input[placeholder*="密码"]'
      )
      .first();
    await expect(passwordInput).toBeVisible();

    // 验证登录按钮
    const loginButton = page
      .locator('button[type="submit"], button:has-text("登录"), .login-btn')
      .first();
    await expect(loginButton).toBeVisible();
  });

  test("主要导航菜单能够正常显示（认证状态）", async ({ page }) => {
    // 先登录获取认证状态
    await AuthHelpers.login(page);

    // 导航到应用首页，使用更宽松的等待策略
    await page.goto("/", {
      timeout: 60000,
      waitUntil: "domcontentloaded",
    });
    await WaitHelpers.waitForPageLoadLoose(page);

    // 验证没有被重定向到登录页面
    expect(page.url()).not.toContain("/login");

    // 查找导航菜单（认证后的导航菜单）
    const navigation = page
      .locator(
        "nav, .nav, .navigation, .menu, .sidebar, .header-nav, .main-nav"
      )
      .first();

    // 如果找到导航菜单，验证其可见性
    const navCount = await navigation.count();
    if (navCount > 0) {
      await expect(navigation).toBeVisible();

      // 验证导航菜单包含链接
      const navLinks = navigation.locator(
        "a, button, .menu-item, .nav-item, li"
      );
      const linkCount = await navLinks.count();
      expect(linkCount).toBeGreaterThan(0);

      // 验证菜单项包含文本或图标
      if (linkCount > 0) {
        const firstItemText = await navLinks.first().textContent();
        const hasIcon =
          (await navLinks.first().locator("svg, i, .icon").count()) > 0;
        expect(firstItemText?.trim() || hasIcon).toBeTruthy();
      }
    } else {
      // 如果没有导航菜单，检查是否有其他导航形式
      const alternativeNav = page
        .locator(".header, .toolbar, .app-bar, .user-menu")
        .first();
      const altNavCount = await alternativeNav.count();

      if (altNavCount > 0) {
        await expect(alternativeNav).toBeVisible();
        console.log("找到替代导航元素");
      } else {
        // 至少验证页面有基本内容（认证后的内容）
        const hasContent = await page.locator("body *").count();
        expect(hasContent).toBeGreaterThan(10);
        console.log("未找到导航菜单，但页面内容正常");
      }
    }
  });

  test("页面响应式布局正常工作", async ({ page }) => {
    // 先导航到登录页面
    await page.goto("/login", { timeout: 30000 });
    await WaitHelpers.waitForPageLoad(page);

    // 测试不同视口大小下的页面显示
    const viewports = [
      { width: 1920, height: 1080 }, // 桌面
      { width: 1366, height: 768 }, // 笔记本
      { width: 768, height: 1024 }, // 平板
    ];

    for (const viewport of viewports) {
      await page.setViewportSize(viewport);
      await page.waitForTimeout(1000); // 等待布局调整

      // 验证页面内容仍然可见
      const body = page.locator("body");
      await expect(body).toBeVisible();

      // 验证页面有基本内容
      const hasContent = await page.locator("body *").count();
      expect(hasContent).toBeGreaterThan(5);
    }
  });

  test("页面加载性能符合预期", async ({ page }) => {
    const startTime = Date.now();

    // 导航到首页
    await page.goto("/");
    await WaitHelpers.waitForPageLoad(page);

    const loadTime = Date.now() - startTime;

    // 验证页面加载时间不超过12秒（考虑到API调用延迟）
    expect(loadTime).toBeLessThan(12000);

    // 获取性能指标
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType(
        "navigation"
      )[0] as PerformanceNavigationTiming;
      return {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded:
          navigation.domContentLoadedEventEnd -
          navigation.domContentLoadedEventStart,
      };
    });

    // 验证DOM内容加载时间合理
    expect(performanceMetrics.domContentLoaded).toBeLessThan(5000);
  });

  test("基础JavaScript功能正常工作", async ({ page }) => {
    // 先导航到登录页面
    await page.goto("/login", { timeout: 30000 });
    await WaitHelpers.waitForPageLoad(page);

    // 测试基础JavaScript功能
    const result = await page.evaluate(() => {
      // 测试基本的JavaScript功能
      const tests = {
        arrayMethods: Array.isArray([1, 2, 3]),
        objectMethods: typeof Object.keys === "function",
        promiseSupport: typeof Promise === "function",
        fetchSupport: typeof fetch === "function",
        localStorageSupport: false,
        sessionStorageSupport: false,
      };

      // 安全地测试 localStorage 和 sessionStorage
      try {
        tests.localStorageSupport =
          typeof localStorage === "object" && localStorage !== null;
        tests.sessionStorageSupport =
          typeof sessionStorage === "object" && sessionStorage !== null;
      } catch (e) {
        // 在某些环境下可能无法访问 localStorage
        tests.localStorageSupport = false;
        tests.sessionStorageSupport = false;
      }

      return tests;
    });

    // 验证所有基础功能都可用
    expect(result.arrayMethods).toBe(true);
    expect(result.objectMethods).toBe(true);
    expect(result.promiseSupport).toBe(true);
    expect(result.fetchSupport).toBe(true);
    // localStorage 和 sessionStorage 可能在某些环境下不可用，所以不强制要求
  });

  test("测试数据工厂集成正常", async ({ page }) => {
    // 验证测试数据工厂能够正常工作
    const quickData = TestDataFactory.getQuickTestData();
    expect(quickData).toBeDefined();
    expect(quickData.simpleInput).toBeDefined();
    expect(quickData.simpleForm).toBeDefined();
    expect(quickData.testUser).toBeDefined();

    // 验证组件数据工厂
    const inputComponent = ComponentDataFactory.createComponentByType("input");
    expect(inputComponent).toBeDefined();
    expect(inputComponent.compType).toBe("input");

    // 验证表单模板工厂
    const templates = FormTemplateFactory.getAllTemplates();
    expect(templates.length).toBeGreaterThan(0);
  });

  test("错误处理机制正常工作", async ({ page }) => {
    // 测试404页面
    await page.goto("/non-existent-page");

    // 等待页面加载
    await page.waitForTimeout(2000);

    // 验证显示了错误页面或重定向到有效页面
    const currentUrl = page.url();
    const pageContent = await page.textContent("body");

    // 应该显示404错误信息或重定向到首页
    const isErrorPage =
      pageContent?.includes("404") ||
      pageContent?.includes("页面不存在") ||
      pageContent?.includes("Not Found") ||
      currentUrl.includes("/login") ||
      currentUrl.endsWith("/");

    expect(isErrorPage).toBe(true);
  });
});
