import { expect, test } from "@playwright/test";
import { AuthHelpers } from "../utils/test-helpers";

test.describe("认证验证测试", () => {
  test("真实API登录后应该能够访问需要认证的页面", async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);

    // 验证登录状态
    const isLoggedIn = await AuthHelpers.isLoggedIn(page);
    expect(isLoggedIn).toBe(true);

    // 尝试访问需要认证的页面（关于页面，简单页面）
    await page.goto("/system/about", { timeout: 30000 });

    // 等待页面加载
    await page.waitForLoadState("networkidle", { timeout: 10000 });

    // 检查是否成功访问（不应该被重定向到登录页面）
    const currentUrl = page.url();
    expect(currentUrl).not.toContain("/login");
    expect(currentUrl).toContain("/system/about");

    // 检查页面是否有基本的导航元素（说明认证成功）
    const hasNavigation =
      (await page
        .locator('nav, .nav, [role="navigation"], .sidebar, .menu')
        .count()) > 0;
    expect(hasNavigation).toBe(true);

    console.log("✅ 认证验证成功：可以访问需要认证的页面");
  });

  test("真实API登录后localStorage应该包含正确的认证信息", async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);

    // 检查localStorage中的认证信息
    const authData = await page.evaluate(() => {
      const userInfo = localStorage.getItem("vr-userinfo");
      return userInfo ? JSON.parse(userInfo) : null;
    });

    // 验证认证数据结构
    expect(authData).toBeTruthy();
    expect(authData.token).toBeTruthy();
    expect(authData.userInfo).toBeTruthy();
    expect(authData.userInfo.name).toBe("admin");
    expect(authData.userInfo.employeeId).toBe("admin");

    console.log("✅ localStorage认证信息验证成功");
    console.log("Token:", authData.token.substring(0, 20) + "...");
    console.log("User:", authData.userInfo.name);
  });

  test("真实API登录后应该能够调用需要认证的API", async ({ page }) => {
    // 使用真实API登录
    await AuthHelpers.realApiLogin(page);

    // 从环境变量构建API URL
    const protocol = process.env.VITE_PROTOCOL || "https";
    const host = process.env.VITE_HOST || "api-dev3.vren-tech.com";
    const port = process.env.VITE_PORT || "443";
    const version = process.env.VITE_VERSION || "v1";
    const apiUrl = `${protocol}://${host}:${port}/${version}`;

    // 尝试调用一个需要认证的API
    const apiResponse = await page.evaluate(async (apiUrl) => {
      try {
        // 获取认证信息
        const userInfo = localStorage.getItem("vr-userinfo");
        const authData = userInfo ? JSON.parse(userInfo) : null;

        if (!authData?.token) {
          return { success: false, error: "No auth token found" };
        }

        // 调用一个简单的API（获取用户信息）
        const response = await fetch(`${apiUrl}/system/user/info`, {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${authData.token}`,
          },
        });

        const data = await response.json();
        return {
          success: response.ok,
          status: response.status,
          data: data,
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
        };
      }
    }, apiUrl);

    // 验证API调用结果
    console.log("API调用结果:", apiResponse);

    // 如果API返回401，说明token无效；如果返回其他状态，说明认证有效
    if (apiResponse.status === 401) {
      throw new Error("API返回401，认证token无效");
    }

    // 只要不是401，就说明认证是有效的（即使API不存在返回404也没关系）
    expect(apiResponse.status).not.toBe(401);

    console.log("✅ API认证验证成功：token有效");
  });
});
