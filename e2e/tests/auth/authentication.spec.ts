import { expect, test } from "@playwright/test";
import {
  <PERSON>th<PERSON><PERSON><PERSON>,
  <PERSON>rror<PERSON>el<PERSON>,
  WaitHel<PERSON>,
} from "../../utils/test-helpers";

test.describe("用户认证测试", () => {
  test.beforeEach(async ({ page }) => {
    // 设置错误捕获
    ErrorHelpers.setupErrorCapture(page);
  });

  test("用户可以成功登录", async ({ page }) => {
    // 执行登录
    await AuthHelpers.login(page);

    // 验证登录成功
    const isLoggedIn = await AuthHelpers.isLoggedIn(page);
    expect(isLoggedIn).toBe(true);

    // 验证不在登录页面
    expect(page.url()).not.toContain("/login");

    // 验证页面内容已加载
    const bodyContent = await page.locator("body").count();
    expect(bodyContent).toBeGreaterThan(0);
  });

  test("登录后可以访问受保护的页面", async ({ page }) => {
    // 先登录
    await AuthHelpers.login(page);

    // 尝试访问首页
    await page.goto("/", { timeout: 30000 });
    await WaitHelpers.waitForPageLoadLoose(page);

    // 验证没有被重定向到登录页面
    expect(page.url()).not.toContain("/login");

    // 验证页面内容存在
    const hasContent = await page.locator("body *").count();
    expect(hasContent).toBeGreaterThan(10);
  });

  test("可以检测登录状态", async ({ page }) => {
    // 未登录状态
    await page.goto("/login");
    const notLoggedIn = await AuthHelpers.isLoggedIn(page);
    expect(notLoggedIn).toBe(false);

    // 登录后状态
    await AuthHelpers.login(page);
    const loggedIn = await AuthHelpers.isLoggedIn(page);
    expect(loggedIn).toBe(true);
  });

  test("可以成功登出", async ({ page }) => {
    // 先登录
    await AuthHelpers.login(page);

    // 验证已登录
    const isLoggedIn = await AuthHelpers.isLoggedIn(page);
    expect(isLoggedIn).toBe(true);

    // 执行登出
    await AuthHelpers.logout(page);

    // 验证已登出（应该在登录页面或本地存储已清除）
    const currentUrl = page.url();
    const hasLoginUrl = currentUrl.includes("/login");

    // 检查本地存储是否已清除
    const hasAuthData = await page.evaluate(() => {
      return (
        localStorage.getItem("_auth") !== null ||
        localStorage.getItem("userInfo") !== null ||
        sessionStorage.length > 0
      );
    });

    // 至少满足一个条件：在登录页面或认证数据已清除
    expect(hasLoginUrl || !hasAuthData).toBe(true);
  });

  test("ensureLoggedIn 辅助函数正常工作", async ({ page }) => {
    // 在未登录状态下调用 ensureLoggedIn
    await page.goto("/login");
    await AuthHelpers.ensureLoggedIn(page);

    // 验证现在已登录
    const isLoggedIn = await AuthHelpers.isLoggedIn(page);
    expect(isLoggedIn).toBe(true);

    // 再次调用 ensureLoggedIn（应该不会重复登录）
    const currentUrl = page.url();
    await AuthHelpers.ensureLoggedIn(page);

    // URL 应该没有变化（没有重复登录）
    expect(page.url()).toBe(currentUrl);
  });

  test("登录表单元素正确显示", async ({ page }) => {
    await page.goto("/login", { timeout: 30000 });
    await WaitHelpers.waitForPageLoadLoose(page);

    // 验证登录表单存在
    await expect(page.locator("form")).toBeVisible();

    // 验证用户名输入框
    const usernameInput = page
      .locator(
        'input[field="username"], input[name="username"], input[placeholder*="用户名"]'
      )
      .first();
    await expect(usernameInput).toBeVisible();

    // 验证密码输入框
    const passwordInput = page
      .locator(
        'input[field="password"], input[name="password"], input[type="password"]'
      )
      .first();
    await expect(passwordInput).toBeVisible();

    // 验证登录按钮
    const submitButton = page
      .locator(
        'button[htmlType="submit"], button[type="submit"], button:has-text("登录")'
      )
      .first();
    await expect(submitButton).toBeVisible();

    // 验证验证码输入框（可能存在）
    const captchaInput = page
      .locator(
        'input[field="captchaAnswer"], input[name="captcha"], input[placeholder*="验证码"]'
      )
      .first();
    const captchaCount = await captchaInput.count();
    if (captchaCount > 0) {
      await expect(captchaInput).toBeVisible();
    }
  });

  test("登录失败时显示错误信息", async ({ page }) => {
    await page.goto("/login", { timeout: 30000 });
    await WaitHelpers.waitForPageLoadLoose(page);

    // 先清除任何现有的认证状态
    await AuthHelpers.logout(page);

    // 等待登录表单加载
    await page.waitForSelector("form", { timeout: 10000 });

    // 填写错误的用户名和密码
    const usernameInput = page
      .locator(
        'input[field="username"], input[name="username"], input[placeholder*="用户名"]'
      )
      .first();
    await usernameInput.fill("wronguser");

    const passwordInput = page
      .locator(
        'input[field="password"], input[name="password"], input[type="password"]'
      )
      .first();
    await passwordInput.fill("wrongpass");

    // 处理验证码（如果存在）
    const captchaInput = page
      .locator(
        'input[field="captchaAnswer"], input[name="captcha"], input[placeholder*="验证码"]'
      )
      .first();
    const captchaCount = await captchaInput.count();

    if (captchaCount > 0) {
      await captchaInput.fill("1234");
    }

    // 提交登录表单
    const submitButton = page
      .locator(
        'button[htmlType="submit"], button[type="submit"], button:has-text("登录")'
      )
      .first();
    await submitButton.click();

    // 等待错误信息出现（检查页面上的错误文本，不是toast）
    await page.waitForTimeout(2000); // 等待API响应

    // 检查页面上的错误信息显示
    const errorText = await page
      .locator(".text-semi-color-danger, .error-message, .login-error")
      .first()
      .textContent()
      .catch(() => null);

    // 验证错误信息存在（可能是网络错误或认证错误）
    if (errorText) {
      console.log(`✅ 检测到错误信息: ${errorText}`);
      expect(errorText.length).toBeGreaterThan(0);
    } else {
      // 如果没有错误文本，可能是因为网络问题，验证仍在登录页面
      expect(page.url()).toContain("/login");
      console.log("⚠️ 未检测到具体错误信息，但仍在登录页面（符合预期）");
    }

    // 验证仍在登录页面
    expect(page.url()).toContain("/login");

    // 验证未登录状态
    const isLoggedIn = await AuthHelpers.isLoggedIn(page);
    expect(isLoggedIn).toBe(false);
  });
});
