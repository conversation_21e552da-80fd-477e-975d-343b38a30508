# E2E测试环境配置指南

## 🚀 快速开始

### 一键管理（推荐）

```bash
# 初始化E2E测试环境
./scripts/e2e.sh setup

# 环境管理
./scripts/e2e.sh env test       # 切换到测试环境
./scripts/e2e.sh env dev3       # 切换到dev3环境
./scripts/e2e.sh current        # 查看当前环境
./scripts/e2e.sh list           # 列出所有环境

# 运行测试
./scripts/e2e.sh test           # 运行所有测试
./scripts/e2e.sh test --ui      # UI模式运行
./scripts/e2e.sh test --project=chromium  # 指定浏览器
```

### 手动设置

如果不使用统一脚本，也可以手动设置：

1. **安装依赖**

   ```bash
   yarn install
   npx playwright install
   ```

2. **配置环境变量**

   ```bash
   # 创建符号链接到测试环境配置
   ln -sf .env.test .env.local

   # 或连接到其他环境
   ln -sf .env.dev3 .env.local      # 连接dev3环境
   ln -sf .env.customer-xxx .env.local  # 连接客户环境
   ```

3. **运行测试**

   ```bash
   yarn test:e2e                    # 运行所有测试
   yarn test:e2e:ui                 # UI模式
   yarn test:e2e:debug              # 调试模式
   yarn test:e2e --project=chromium # 指定浏览器
   ```

## 🔧 环境配置

### 简化的配置架构

E2E测试使用简化的配置管理，**所有配置都来自 `.env.*` 文件**，无需额外的配置层：

### 环境变量优先级

1. **CI环境变量** (最高优先级)
2. **`.env.local`** (本地开发，不提交到git)
3. **`.env.test`** (E2E测试的标准环境，推荐默认配置)
4. **`.env`** (项目默认配置)

> 💡 **设计理念**: 配置即代码，直接使用 `.env` 文件作为唯一配置源，避免多层配置重复。

### 环境切换

项目提供多个预配置的环境文件，使用符号链接方式切换环境：

**符号链接的优势**:

- ✅ **实时同步**: 源文件更新时，链接文件自动同步
- ✅ **节省空间**: 不创建重复文件
- ✅ **清晰可见**: `ls -la .env.local` 可以看到当前使用的环境
- ✅ **原子切换**: 环境切换是原子性操作

```bash
# 使用统一脚本 (推荐)
./scripts/e2e.sh env test       # 测试环境
./scripts/e2e.sh env dev1       # dev1环境
./scripts/e2e.sh env dev3       # dev3环境
./scripts/e2e.sh current        # 查看当前环境
./scripts/e2e.sh list           # 列出所有环境

# 或直接使用ln命令
ln -sf .env.test .env.local     # 测试环境
ln -sf .env.dev3 .env.local     # dev3环境
ls -la .env.local               # 查看当前环境
```

### 必需的环境变量

#### 应用核心配置 (VITE\_\* 变量)

这些变量是应用运行的核心配置，E2E测试必须提供：

| 变量名                   | 描述           | 默认值                          | 示例                            |
| ------------------------ | -------------- | ------------------------------- | ------------------------------- |
| `VITE_PROTOCOL`          | API协议        | `https`                         | `https`                         |
| `VITE_HOST`              | API主机地址    | `api-test.vren-tech.com`        | `api-dev3.vren-tech.com`        |
| `VITE_PORT`              | API端口        | `443`                           | `443`                           |
| `VITE_VERSION`           | API版本        | `v1`                            | `v1`                            |
| `VITE_LHOTSE_HOST`       | 承包商系统主机 | `test-contractor.vren-tech.com` | `dev3-contractor.vren-tech.com` |
| `VITE_LHOTSE_PORT`       | 承包商系统端口 | `443`                           | `443`                           |
| `VITE_DHAULAGIRI_HOST`   | 移动端主机     | `test-mobile.vren-tech.com`     | `dev3-mobile.vren-tech.com`     |
| `VITE_DHAULAGIRI_PORT`   | 移动端端口     | `443`                           | `443`                           |
| `VITE_OEM_ID`            | 组织标识       | `test`                          | `dev3`                          |
| `VITE_TIANDITU_TOKEN`    | 天地图Token    | -                               | `token1,token2`                 |
| `VITE_APP_DOWNLOAD_PATH` | 应用下载路径   | `static/apk/current.apk`        | -                               |
| `VITE_SENTRY_DSN`        | 错误监控DSN    | -                               | `https://...`                   |

#### E2E测试配置

| 变量名          | 描述        | 默认值                  | 示例                    |
| --------------- | ----------- | ----------------------- | ----------------------- |
| `BASE_URL`      | 应用基础URL | `http://localhost:5173` | `http://localhost:3000` |
| `TEST_USERNAME` | 测试用户名  | `admin`                 | `your-username`         |
| `TEST_PASSWORD` | 测试密码    | `safe12345`             | `your-password`         |

### 可选的环境变量

| 变量名         | 描述           | 默认值  | 可选值       |
| -------------- | -------------- | ------- | ------------ |
| `HEADLESS`     | 无头模式       | `false` | `true/false` |
| `DEBUG`        | 调试模式       | `false` | `true/false` |
| `TRACING`      | 启用追踪       | `false` | `true/false` |
| `VIDEO`        | 录制视频       | `false` | `true/false` |
| `SLOW_MO`      | 慢动作延迟(ms) | `0`     | `100, 500`   |
| `TEST_TIMEOUT` | 测试超时(ms)   | `30000` | `60000`      |

## 🏗️ CI/CD配置

### GitHub Actions

项目已配置GitHub Actions工作流 (`.github/workflows/e2e-tests.yml`)，支持：

- ✅ 多浏览器并行测试 (Chromium, Firefox, Safari)
- ✅ 自动环境变量配置
- ✅ 测试报告上传
- ✅ 失败时的截图和视频
- ✅ **智能测试结果判断**

### CI测试结果判断机制

CI环境通过以下方式判断E2E测试是否全部通过：

1. **测试执行**: 运行CI专用测试套件和简单验证测试
2. **结果解析**: 解析 `e2e/reports/test-results.json` 文件
3. **状态验证**: 检查测试统计信息
   - ✅ `expected > 0` - 必须有通过的测试
   - ❌ `unexpected = 0` - 不允许失败的测试
   - ⚠️ `flaky = 0` - CI环境不允许不稳定测试
4. **退出码**: 根据验证结果返回相应的退出码

**本地验证命令**:

```bash
# 运行测试并验证结果
./scripts/e2e.sh test
./scripts/e2e.sh verify

# 或者直接验证最近的测试结果
./scripts/verify-e2e-results.sh
```

### 配置GitHub Secrets

在GitHub仓库设置中添加以下Secrets：

**必需的Secrets:**

- `TEST_USERNAME` - 测试用户名 (默认: `admin`)
- `TEST_PASSWORD` - 测试密码 (默认: `safe12345`)

**可选的Secrets:**

- `VITE_PROTOCOL` - API协议
- `VITE_HOST` - API主机
- `VITE_PORT` - API端口
- 其他VITE\_\*环境变量

### 本地CI模拟

```bash
# 模拟CI环境运行测试
CI=true HEADLESS=true yarn test:e2e

# 使用构建版本测试
yarn build
yarn preview --port 5173 &
CI=true BASE_URL=http://localhost:5173 yarn test:e2e
```

## 🐛 故障排除

### 常见问题

1. **端口冲突**

   ```bash
   # 检查端口占用
   lsof -i :5173

   # 使用不同端口
   BASE_URL=http://localhost:3000 yarn test:e2e
   ```

2. **浏览器安装问题**

   ```bash
   # 重新安装浏览器
   npx playwright install --force

   # 安装系统依赖
   npx playwright install-deps
   ```

3. **环境变量未生效**

   ```bash
   # 检查环境变量加载
   node -e "require('./e2e/config/test-env').validateTestEnvironment()"
   ```

4. **测试超时**

   ```bash
   # 增加超时时间
   TEST_TIMEOUT=60000 yarn test:e2e

   # 启用慢动作模式
   SLOW_MO=500 yarn test:e2e
   ```

## 📊 测试报告

测试完成后，报告将生成在：

- **HTML报告**: `e2e/reports/html-report/index.html`
- **JSON报告**: `e2e/reports/test-results.json`
- **JUnit报告**: `e2e/reports/test-results.xml`

查看HTML报告：

```bash
yarn test:e2e:report
```

## 🔍 调试技巧

1. **可视化调试**

   ```bash
   yarn test:e2e:debug
   ```

2. **启用追踪**

   ```bash
   TRACING=true yarn test:e2e
   ```

3. **录制视频**

   ```bash
   VIDEO=true yarn test:e2e
   ```

4. **慢动作模式**
   ```bash
   SLOW_MO=1000 yarn test:e2e
   ```
