/**
 * 表单模板工厂
 * 提供各种业务场景的完整表单模板
 */

import { ComponentData, ComponentDataFactory } from './componentData';

// 表单模板接口
export interface FormTemplate {
  id: string;
  title: string;
  description: string;
  components: ComponentData[];
  layout: {
    columns: number;
    responsive: boolean;
  };
  validation: {
    required: string[];
    rules: Record<string, any>;
  };
  metadata: {
    category: string;
    tags: string[];
    difficulty: 'simple' | 'medium' | 'complex';
    estimatedTime: number; // 预计填写时间（分钟）
  };
}

/**
 * 表单模板工厂类
 * 提供各种业务场景的表单模板
 */
export class FormTemplateFactory {
  /**
   * 创建用户信息表单模板
   */
  static createUserInfoForm(): FormTemplate {
    const components = [
      ComponentDataFactory.createInputComponent({
        label: '姓名',
        placeholder: '请输入真实姓名',
        required: true,
      }),
      ComponentDataFactory.createInputComponent({
        label: '邮箱',
        placeholder: '请输入邮箱地址',
        required: true,
      }),
      ComponentDataFactory.createInputComponent({
        label: '手机号',
        placeholder: '请输入手机号码',
        required: true,
      }),
      ComponentDataFactory.createSelectComponent({
        label: '部门',
        placeholder: '请选择所属部门',
        required: true,
        options: [
          { label: '技术部', value: 'tech' },
          { label: '产品部', value: 'product' },
          { label: '运营部', value: 'operation' },
          { label: '人事部', value: 'hr' },
        ],
      }),
      ComponentDataFactory.createRadioComponent({
        label: '性别',
        required: true,
        options: [
          { label: '男', value: 'male' },
          { label: '女', value: 'female' },
        ],
      }),
      ComponentDataFactory.createDatePickerComponent({
        label: '出生日期',
        placeholder: '请选择出生日期',
        required: false,
      }),
    ];

    return {
      id: 'user_info_form',
      title: '用户信息表',
      description: '用于收集用户基本信息的表单',
      components,
      layout: {
        columns: 2,
        responsive: true,
      },
      validation: {
        required: ['姓名', '邮箱', '手机号', '部门', '性别'],
        rules: {
          邮箱: { type: 'email' },
          手机号: { pattern: /^1[3-9]\d{9}$/ },
        },
      },
      metadata: {
        category: '用户管理',
        tags: ['用户', '基础信息', '注册'],
        difficulty: 'simple',
        estimatedTime: 3,
      },
    };
  }

  /**
   * 创建反馈表单模板
   */
  static createFeedbackForm(): FormTemplate {
    const components = [
      ComponentDataFactory.createSelectComponent({
        label: '反馈类型',
        placeholder: '请选择反馈类型',
        required: true,
        options: [
          { label: '功能建议', value: 'feature' },
          { label: '问题反馈', value: 'bug' },
          { label: '使用咨询', value: 'question' },
          { label: '其他', value: 'other' },
        ],
      }),
      ComponentDataFactory.createRadioComponent({
        label: '满意度评价',
        required: true,
        options: [
          { label: '非常满意', value: 5 },
          { label: '满意', value: 4 },
          { label: '一般', value: 3 },
          { label: '不满意', value: 2 },
          { label: '非常不满意', value: 1 },
        ],
      }),
      ComponentDataFactory.createTextareaComponent({
        label: '详细描述',
        placeholder: '请详细描述您的反馈内容',
        required: true,
        rows: 6,
        maxLength: 1000,
      }),
      ComponentDataFactory.createInputComponent({
        label: '联系方式',
        placeholder: '请留下您的联系方式（可选）',
        required: false,
      }),
      ComponentDataFactory.createImagePickerComponent({
        label: '相关截图',
        required: false,
        maxCount: 5,
      }),
    ];

    return {
      id: 'feedback_form',
      title: '用户反馈表',
      description: '收集用户反馈和建议的表单',
      components,
      layout: {
        columns: 1,
        responsive: true,
      },
      validation: {
        required: ['反馈类型', '满意度评价', '详细描述'],
        rules: {},
      },
      metadata: {
        category: '客户服务',
        tags: ['反馈', '满意度', '客服'],
        difficulty: 'medium',
        estimatedTime: 5,
      },
    };
  }

  /**
   * 创建项目申请表单模板
   */
  static createProjectApplicationForm(): FormTemplate {
    const components = [
      ComponentDataFactory.createInputComponent({
        label: '项目名称',
        placeholder: '请输入项目名称',
        required: true,
      }),
      ComponentDataFactory.createSelectComponent({
        label: '项目类型',
        placeholder: '请选择项目类型',
        required: true,
        options: [
          { label: '产品开发', value: 'product' },
          { label: '技术研发', value: 'research' },
          { label: '市场推广', value: 'marketing' },
          { label: '内部优化', value: 'internal' },
        ],
      }),
      ComponentDataFactory.createEmployeePickerComponent({
        label: '项目负责人',
        placeholder: '请选择项目负责人',
        required: true,
        multiple: false,
      }),
      ComponentDataFactory.createEmployeePickerComponent({
        label: '项目成员',
        placeholder: '请选择项目成员',
        required: false,
        multiple: true,
      }),
      ComponentDataFactory.createDatePickerComponent({
        label: '预计开始时间',
        placeholder: '请选择预计开始时间',
        required: true,
      }),
      ComponentDataFactory.createDatePickerComponent({
        label: '预计结束时间',
        placeholder: '请选择预计结束时间',
        required: true,
      }),
      ComponentDataFactory.createTextareaComponent({
        label: '项目描述',
        placeholder: '请详细描述项目内容、目标和预期成果',
        required: true,
        rows: 8,
        maxLength: 2000,
      }),
      ComponentDataFactory.createCheckboxComponent({
        label: '所需资源',
        required: false,
        options: [
          { label: '人力资源', value: 'human' },
          { label: '技术支持', value: 'tech' },
          { label: '资金预算', value: 'budget' },
          { label: '设备采购', value: 'equipment' },
          { label: '外部合作', value: 'partner' },
        ],
      }),
      ComponentDataFactory.createFilePickerComponent({
        label: '相关文档',
        required: false,
        maxCount: 10,
      }),
    ];

    return {
      id: 'project_application_form',
      title: '项目申请表',
      description: '用于申请新项目的详细表单',
      components,
      layout: {
        columns: 2,
        responsive: true,
      },
      validation: {
        required: ['项目名称', '项目类型', '项目负责人', '预计开始时间', '预计结束时间', '项目描述'],
        rules: {
          预计结束时间: { afterField: '预计开始时间' },
        },
      },
      metadata: {
        category: '项目管理',
        tags: ['项目', '申请', '审批'],
        difficulty: 'complex',
        estimatedTime: 15,
      },
    };
  }

  /**
   * 创建简单联系表单模板
   */
  static createSimpleContactForm(): FormTemplate {
    const components = [
      ComponentDataFactory.createInputComponent({
        label: '姓名',
        placeholder: '请输入您的姓名',
        required: true,
      }),
      ComponentDataFactory.createInputComponent({
        label: '邮箱',
        placeholder: '请输入邮箱地址',
        required: true,
      }),
      ComponentDataFactory.createTextareaComponent({
        label: '留言',
        placeholder: '请输入您的留言',
        required: true,
        rows: 4,
      }),
    ];

    return {
      id: 'simple_contact_form',
      title: '简单联系表单',
      description: '最基础的联系表单，用于快速测试',
      components,
      layout: {
        columns: 1,
        responsive: true,
      },
      validation: {
        required: ['姓名', '邮箱', '留言'],
        rules: {
          邮箱: { type: 'email' },
        },
      },
      metadata: {
        category: '联系',
        tags: ['联系', '简单', '测试'],
        difficulty: 'simple',
        estimatedTime: 2,
      },
    };
  }

  /**
   * 创建复杂调查表单模板
   */
  static createComplexSurveyForm(): FormTemplate {
    const components = [
      ComponentDataFactory.createInputComponent({
        label: '参与者姓名',
        required: true,
      }),
      ComponentDataFactory.createSelectComponent({
        label: '年龄段',
        required: true,
        options: [
          { label: '18-25岁', value: '18-25' },
          { label: '26-35岁', value: '26-35' },
          { label: '36-45岁', value: '36-45' },
          { label: '46-55岁', value: '46-55' },
          { label: '55岁以上', value: '55+' },
        ],
      }),
      ComponentDataFactory.createRadioComponent({
        label: '使用频率',
        required: true,
        options: [
          { label: '每天', value: 'daily' },
          { label: '每周', value: 'weekly' },
          { label: '每月', value: 'monthly' },
          { label: '很少', value: 'rarely' },
        ],
      }),
      ComponentDataFactory.createCheckboxComponent({
        label: '感兴趣的功能',
        required: false,
        options: [
          { label: '数据分析', value: 'analytics' },
          { label: '报表生成', value: 'reports' },
          { label: '团队协作', value: 'collaboration' },
          { label: '移动端支持', value: 'mobile' },
          { label: 'API集成', value: 'api' },
        ],
      }),
      ComponentDataFactory.createTableComponent({
        label: '功能评分表',
        required: false,
      }),
      ComponentDataFactory.createTextareaComponent({
        label: '改进建议',
        placeholder: '请提供您的改进建议',
        required: false,
        rows: 6,
      }),
    ];

    return {
      id: 'complex_survey_form',
      title: '产品使用调查表',
      description: '包含多种组件类型的复杂调查表单',
      components,
      layout: {
        columns: 2,
        responsive: true,
      },
      validation: {
        required: ['参与者姓名', '年龄段', '使用频率'],
        rules: {},
      },
      metadata: {
        category: '调研',
        tags: ['调查', '复杂', '多组件'],
        difficulty: 'complex',
        estimatedTime: 10,
      },
    };
  }

  /**
   * 获取所有预定义的表单模板
   */
  static getAllTemplates(): FormTemplate[] {
    return [
      this.createSimpleContactForm(),
      this.createUserInfoForm(),
      this.createFeedbackForm(),
      this.createProjectApplicationForm(),
      this.createComplexSurveyForm(),
    ];
  }

  /**
   * 根据难度级别获取表单模板
   */
  static getTemplatesByDifficulty(difficulty: 'simple' | 'medium' | 'complex'): FormTemplate[] {
    return this.getAllTemplates().filter(template => template.metadata.difficulty === difficulty);
  }

  /**
   * 根据分类获取表单模板
   */
  static getTemplatesByCategory(category: string): FormTemplate[] {
    return this.getAllTemplates().filter(template => template.metadata.category === category);
  }

  /**
   * 根据ID获取表单模板
   */
  static getTemplateById(id: string): FormTemplate | undefined {
    return this.getAllTemplates().find(template => template.id === id);
  }
}
