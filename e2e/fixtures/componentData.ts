/**
 * 组件测试数据工厂
 * 基于实际FormEditor支持的组件类型和配置结构
 */

import { COMPONENT_TYPES } from '../utils/selectors';

// 基础组件数据接口
export interface ComponentData {
  compId: string;
  compName: string;
  compType: string;
  business: string;
  group: 'base' | 'business' | 'layout';
  formData: Record<string, any>;
}

// 组件属性配置接口
export interface ComponentProperties {
  label?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  defaultValue?: any;
  options?: Array<{ label: string; value: any }>;
  rows?: number;
  maxLength?: number;
  format?: string;
  multiple?: boolean;
  maxCount?: number;
}

/**
 * 组件数据工厂类
 * 提供各种组件类型的测试数据生成
 */
export class ComponentDataFactory {
  /**
   * 生成基础输入框组件数据
   */
  static createInputComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    return {
      compId: `input_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '输入框',
      compType: COMPONENT_TYPES.INPUT,
      business: '',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '姓名',
        placeholder: properties.placeholder || '请输入姓名',
        required: properties.required ?? true,
        disabled: properties.disabled ?? false,
        defaultValue: properties.defaultValue || '',
        maxLength: properties.maxLength || 50,
      },
    };
  }

  /**
   * 生成多行输入框组件数据
   */
  static createTextareaComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    return {
      compId: `textarea_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '多行输入框',
      compType: COMPONENT_TYPES.TEXTAREA,
      business: '',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '备注',
        placeholder: properties.placeholder || '请输入备注信息',
        required: properties.required ?? false,
        disabled: properties.disabled ?? false,
        defaultValue: properties.defaultValue || '',
        rows: properties.rows || 4,
        maxLength: properties.maxLength || 500,
      },
    };
  }

  /**
   * 生成下拉选择器组件数据
   */
  static createSelectComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    const defaultOptions = [
      { label: '选项1', value: 'option1' },
      { label: '选项2', value: 'option2' },
      { label: '选项3', value: 'option3' },
    ];

    return {
      compId: `selector_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '复选框',
      compType: COMPONENT_TYPES.SELECT,
      business: '',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '部门',
        placeholder: properties.placeholder || '请选择部门',
        required: properties.required ?? true,
        disabled: properties.disabled ?? false,
        defaultValue: properties.defaultValue || '',
        options: properties.options || defaultOptions,
        multiple: properties.multiple ?? false,
      },
    };
  }

  /**
   * 生成单选框组件数据
   */
  static createRadioComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    const defaultOptions = [
      { label: '是', value: true },
      { label: '否', value: false },
    ];

    return {
      compId: `radio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '单选框',
      compType: COMPONENT_TYPES.RADIO,
      business: '',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '是否同意',
        required: properties.required ?? true,
        disabled: properties.disabled ?? false,
        defaultValue: properties.defaultValue || '',
        options: properties.options || defaultOptions,
      },
    };
  }

  /**
   * 生成复选框组件数据
   */
  static createCheckboxComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    const defaultOptions = [
      { label: '选项A', value: 'a' },
      { label: '选项B', value: 'b' },
      { label: '选项C', value: 'c' },
    ];

    return {
      compId: `checkbox_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '复选框',
      compType: COMPONENT_TYPES.CHECKBOX,
      business: '',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '兴趣爱好',
        required: properties.required ?? false,
        disabled: properties.disabled ?? false,
        defaultValue: properties.defaultValue || [],
        options: properties.options || defaultOptions,
      },
    };
  }

  /**
   * 生成日期选择器组件数据
   */
  static createDatePickerComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    return {
      compId: `datePicker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '日期选择器',
      compType: COMPONENT_TYPES.DATE,
      business: '',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '出生日期',
        placeholder: properties.placeholder || '请选择日期',
        required: properties.required ?? true,
        disabled: properties.disabled ?? false,
        defaultValue: properties.defaultValue || '',
        format: properties.format || 'YYYY-MM-DD',
      },
    };
  }

  /**
   * 生成人员选择器组件数据
   */
  static createEmployeePickerComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    return {
      compId: `employeePicker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '人员选择',
      compType: COMPONENT_TYPES.EMPLOYEE,
      business: '',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '负责人',
        placeholder: properties.placeholder || '请选择负责人',
        required: properties.required ?? true,
        disabled: properties.disabled ?? false,
        defaultValue: properties.defaultValue || '',
        multiple: properties.multiple ?? false,
      },
    };
  }

  /**
   * 生成文件附件组件数据
   */
  static createFilePickerComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    return {
      compId: `annexFilePicker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '附件(文件)',
      compType: COMPONENT_TYPES.FILE,
      business: 'file',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '相关文件',
        required: properties.required ?? false,
        disabled: properties.disabled ?? false,
        defaultValue: properties.defaultValue || [],
        maxCount: properties.maxCount || 5,
      },
    };
  }

  /**
   * 生成图片附件组件数据
   */
  static createImagePickerComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    return {
      compId: `annexImgPicker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '附件(图片)',
      compType: COMPONENT_TYPES.IMAGE,
      business: 'img',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '相关图片',
        required: properties.required ?? false,
        disabled: properties.disabled ?? false,
        defaultValue: properties.defaultValue || [],
        maxCount: properties.maxCount || 3,
      },
    };
  }

  /**
   * 生成表格组件数据
   */
  static createTableComponent(properties: Partial<ComponentProperties> = {}): ComponentData {
    return {
      compId: `table_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      compName: '表格',
      compType: COMPONENT_TYPES.TABLE,
      business: '',
      group: 'base',
      formData: {
        isReq: 'required',
        label: properties.label || '数据表格',
        required: properties.required ?? false,
        disabled: properties.disabled ?? false,
        rowNum: 3,
        colNum: 3,
        isHead: true,
        paddingLeft: 0,
        paddingRight: 0,
        paddingTop: 0,
        paddingBottom: 0,
      },
    };
  }

  /**
   * 获取所有支持的组件类型
   */
  static getSupportedComponentTypes(): string[] {
    return Object.values(COMPONENT_TYPES);
  }

  /**
   * 根据组件类型创建组件数据
   */
  static createComponentByType(
    componentType: string,
    properties: Partial<ComponentProperties> = {}
  ): ComponentData {
    switch (componentType) {
      case COMPONENT_TYPES.INPUT:
        return this.createInputComponent(properties);
      case COMPONENT_TYPES.TEXTAREA:
        return this.createTextareaComponent(properties);
      case COMPONENT_TYPES.SELECT:
        return this.createSelectComponent(properties);
      case COMPONENT_TYPES.RADIO:
        return this.createRadioComponent(properties);
      case COMPONENT_TYPES.CHECKBOX:
        return this.createCheckboxComponent(properties);
      case COMPONENT_TYPES.DATE:
        return this.createDatePickerComponent(properties);
      case COMPONENT_TYPES.EMPLOYEE:
        return this.createEmployeePickerComponent(properties);
      case COMPONENT_TYPES.FILE:
        return this.createFilePickerComponent(properties);
      case COMPONENT_TYPES.IMAGE:
        return this.createImagePickerComponent(properties);
      case COMPONENT_TYPES.TABLE:
        return this.createTableComponent(properties);
      default:
        throw new Error(`Unsupported component type: ${componentType}`);
    }
  }

  /**
   * 批量创建多个组件
   */
  static createMultipleComponents(
    componentConfigs: Array<{ type: string; properties?: Partial<ComponentProperties> }>
  ): ComponentData[] {
    return componentConfigs.map(config =>
      this.createComponentByType(config.type, config.properties || {})
    );
  }
}
