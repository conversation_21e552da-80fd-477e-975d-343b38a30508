/**
 * 用户场景数据工厂
 * 提供各种用户操作场景的测试数据
 */

import { FormTemplate } from './formTemplates';
import { ComponentData } from './componentData';

// 用户操作步骤接口
export interface UserAction {
  type: 'drag' | 'click' | 'input' | 'select' | 'wait' | 'verify';
  target: string;
  value?: any;
  description: string;
  expectedResult?: string;
}

// 用户场景接口
export interface UserScenario {
  id: string;
  name: string;
  description: string;
  category: 'basic' | 'intermediate' | 'advanced' | 'edge-case';
  priority: 'high' | 'medium' | 'low';
  estimatedDuration: number; // 预计执行时间（秒）
  preconditions: string[];
  actions: UserAction[];
  expectedOutcome: string;
  tags: string[];
}

// 测试用户角色
export interface TestUser {
  id: string;
  name: string;
  role: 'admin' | 'editor' | 'viewer' | 'guest';
  permissions: string[];
  experience: 'beginner' | 'intermediate' | 'expert';
  preferences: {
    language: string;
    theme: 'light' | 'dark';
    shortcuts: boolean;
  };
}

/**
 * 用户场景数据工厂类
 */
export class UserScenarioFactory {
  /**
   * 创建基础表单创建场景
   */
  static createBasicFormCreationScenario(): UserScenario {
    return {
      id: 'basic_form_creation',
      name: '基础表单创建',
      description: '用户创建一个包含基础组件的简单表单',
      category: 'basic',
      priority: 'high',
      estimatedDuration: 120,
      preconditions: [
        '用户已登录系统',
        '用户有表单编辑权限',
        '表单编辑器页面已加载',
      ],
      actions: [
        {
          type: 'drag',
          target: 'input-component',
          description: '拖拽输入框组件到画布',
          expectedResult: '画布中出现输入框组件',
        },
        {
          type: 'click',
          target: 'input-component-canvas',
          description: '点击画布中的输入框组件',
          expectedResult: '组件被选中，属性面板显示组件属性',
        },
        {
          type: 'input',
          target: 'property-label',
          value: '姓名',
          description: '设置组件标签为"姓名"',
          expectedResult: '组件标签更新为"姓名"',
        },
        {
          type: 'input',
          target: 'property-placeholder',
          value: '请输入您的姓名',
          description: '设置组件占位符',
          expectedResult: '组件占位符更新',
        },
        {
          type: 'click',
          target: 'property-required',
          description: '设置组件为必填',
          expectedResult: '组件标记为必填',
        },
        {
          type: 'click',
          target: 'save-button',
          description: '保存表单配置',
          expectedResult: '表单保存成功，显示成功提示',
        },
      ],
      expectedOutcome: '成功创建包含一个必填输入框的表单',
      tags: ['基础操作', '拖拽', '属性设置', '保存'],
    };
  }

  /**
   * 创建多组件表单场景
   */
  static createMultiComponentFormScenario(): UserScenario {
    return {
      id: 'multi_component_form',
      name: '多组件表单创建',
      description: '创建包含多种组件类型的复杂表单',
      category: 'intermediate',
      priority: 'high',
      estimatedDuration: 300,
      preconditions: [
        '用户已登录系统',
        '表单编辑器页面已加载',
        '组件库显示正常',
      ],
      actions: [
        {
          type: 'drag',
          target: 'input-component',
          description: '添加输入框组件',
        },
        {
          type: 'drag',
          target: 'select-component',
          description: '添加下拉选择组件',
        },
        {
          type: 'drag',
          target: 'textarea-component',
          description: '添加多行输入组件',
        },
        {
          type: 'drag',
          target: 'radio-component',
          description: '添加单选框组件',
        },
        {
          type: 'drag',
          target: 'checkbox-component',
          description: '添加复选框组件',
        },
        {
          type: 'click',
          target: 'save-button',
          description: '保存多组件表单',
        },
      ],
      expectedOutcome: '成功创建包含5种不同组件类型的表单',
      tags: ['多组件', '复杂表单', '组件类型'],
    };
  }

  /**
   * 创建表单预览场景
   */
  static createFormPreviewScenario(): UserScenario {
    return {
      id: 'form_preview',
      name: '表单预览功能',
      description: '用户创建表单后进行预览测试',
      category: 'basic',
      priority: 'medium',
      estimatedDuration: 90,
      preconditions: [
        '表单已创建完成',
        '表单包含至少一个组件',
      ],
      actions: [
        {
          type: 'click',
          target: 'preview-button',
          description: '点击预览按钮',
          expectedResult: '打开表单预览模态框',
        },
        {
          type: 'verify',
          target: 'preview-modal',
          description: '验证预览模态框显示',
          expectedResult: '预览模态框正确显示表单内容',
        },
        {
          type: 'input',
          target: 'preview-form-input',
          value: '测试数据',
          description: '在预览表单中输入测试数据',
          expectedResult: '输入框正确接收数据',
        },
        {
          type: 'click',
          target: 'preview-close',
          description: '关闭预览模态框',
          expectedResult: '返回表单编辑界面',
        },
      ],
      expectedOutcome: '成功预览表单并验证表单功能正常',
      tags: ['预览', '模态框', '表单验证'],
    };
  }

  /**
   * 创建表单导入导出场景
   */
  static createFormImportExportScenario(): UserScenario {
    return {
      id: 'form_import_export',
      name: '表单导入导出',
      description: '测试表单配置的导入和导出功能',
      category: 'advanced',
      priority: 'medium',
      estimatedDuration: 180,
      preconditions: [
        '用户有导入导出权限',
        '存在可导出的表单配置',
      ],
      actions: [
        {
          type: 'click',
          target: 'export-button',
          description: '点击导出按钮',
          expectedResult: '开始导出表单配置',
        },
        {
          type: 'verify',
          target: 'export-file',
          description: '验证导出文件生成',
          expectedResult: '成功生成配置文件',
        },
        {
          type: 'click',
          target: 'reset-button',
          description: '重置当前表单',
          expectedResult: '表单被清空',
        },
        {
          type: 'click',
          target: 'import-button',
          description: '点击导入按钮',
          expectedResult: '打开文件选择对话框',
        },
        {
          type: 'select',
          target: 'import-file',
          value: 'exported-config.json',
          description: '选择之前导出的配置文件',
          expectedResult: '文件被选中',
        },
        {
          type: 'verify',
          target: 'imported-form',
          description: '验证表单配置被正确导入',
          expectedResult: '表单恢复到导出前的状态',
        },
      ],
      expectedOutcome: '成功完成表单配置的导出和导入流程',
      tags: ['导入', '导出', '配置管理'],
    };
  }

  /**
   * 创建错误处理场景
   */
  static createErrorHandlingScenario(): UserScenario {
    return {
      id: 'error_handling',
      name: '错误处理测试',
      description: '测试各种错误情况下的系统行为',
      category: 'edge-case',
      priority: 'medium',
      estimatedDuration: 240,
      preconditions: [
        '表单编辑器已加载',
      ],
      actions: [
        {
          type: 'click',
          target: 'save-button',
          description: '尝试保存空表单',
          expectedResult: '显示错误提示：表单不能为空',
        },
        {
          type: 'drag',
          target: 'input-component',
          description: '添加输入框组件',
        },
        {
          type: 'input',
          target: 'property-label',
          value: '',
          description: '设置空标签',
          expectedResult: '显示验证错误',
        },
        {
          type: 'input',
          target: 'property-label',
          value: 'A'.repeat(100),
          description: '设置过长标签',
          expectedResult: '显示长度限制错误',
        },
        {
          type: 'verify',
          target: 'error-messages',
          description: '验证错误消息显示',
          expectedResult: '错误消息清晰可见',
        },
      ],
      expectedOutcome: '系统正确处理各种错误情况并提供友好的错误提示',
      tags: ['错误处理', '验证', '边界情况'],
    };
  }

  /**
   * 创建性能测试场景
   */
  static createPerformanceTestScenario(): UserScenario {
    return {
      id: 'performance_test',
      name: '性能压力测试',
      description: '测试大量组件情况下的系统性能',
      category: 'advanced',
      priority: 'low',
      estimatedDuration: 600,
      preconditions: [
        '表单编辑器已加载',
        '系统资源充足',
      ],
      actions: [
        {
          type: 'drag',
          target: 'multiple-components',
          description: '快速添加50个组件',
          expectedResult: '系统响应正常，无明显卡顿',
        },
        {
          type: 'verify',
          target: 'performance-metrics',
          description: '检查性能指标',
          expectedResult: '内存使用合理，渲染流畅',
        },
        {
          type: 'click',
          target: 'save-button',
          description: '保存大型表单',
          expectedResult: '保存操作在合理时间内完成',
        },
      ],
      expectedOutcome: '系统在大量组件情况下仍能保持良好性能',
      tags: ['性能', '压力测试', '大数据量'],
    };
  }

  /**
   * 获取所有预定义场景
   */
  static getAllScenarios(): UserScenario[] {
    return [
      this.createBasicFormCreationScenario(),
      this.createMultiComponentFormScenario(),
      this.createFormPreviewScenario(),
      this.createFormImportExportScenario(),
      this.createErrorHandlingScenario(),
      this.createPerformanceTestScenario(),
    ];
  }

  /**
   * 根据分类获取场景
   */
  static getScenariosByCategory(category: UserScenario['category']): UserScenario[] {
    return this.getAllScenarios().filter(scenario => scenario.category === category);
  }

  /**
   * 根据优先级获取场景
   */
  static getScenariosByPriority(priority: UserScenario['priority']): UserScenario[] {
    return this.getAllScenarios().filter(scenario => scenario.priority === priority);
  }

  /**
   * 获取高优先级的基础场景
   */
  static getEssentialScenarios(): UserScenario[] {
    return this.getAllScenarios().filter(
      scenario => scenario.priority === 'high' && scenario.category === 'basic'
    );
  }
}

/**
 * 测试用户工厂类
 */
export class TestUserFactory {
  /**
   * 创建管理员用户
   */
  static createAdminUser(): TestUser {
    return {
      id: 'admin_user',
      name: '管理员',
      role: 'admin',
      permissions: ['create', 'read', 'update', 'delete', 'import', 'export', 'manage'],
      experience: 'expert',
      preferences: {
        language: 'zh-CN',
        theme: 'light',
        shortcuts: true,
      },
    };
  }

  /**
   * 创建编辑者用户
   */
  static createEditorUser(): TestUser {
    return {
      id: 'editor_user',
      name: '编辑者',
      role: 'editor',
      permissions: ['create', 'read', 'update', 'import', 'export'],
      experience: 'intermediate',
      preferences: {
        language: 'zh-CN',
        theme: 'light',
        shortcuts: false,
      },
    };
  }

  /**
   * 创建查看者用户
   */
  static createViewerUser(): TestUser {
    return {
      id: 'viewer_user',
      name: '查看者',
      role: 'viewer',
      permissions: ['read'],
      experience: 'beginner',
      preferences: {
        language: 'zh-CN',
        theme: 'light',
        shortcuts: false,
      },
    };
  }

  /**
   * 获取所有测试用户
   */
  static getAllUsers(): TestUser[] {
    return [
      this.createAdminUser(),
      this.createEditorUser(),
      this.createViewerUser(),
    ];
  }
}
