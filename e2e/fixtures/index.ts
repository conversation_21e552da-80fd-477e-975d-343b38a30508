/**
 * 测试数据工厂统一导出文件
 * 提供所有测试数据和工具函数的统一入口
 */

// 组件数据相关
export {
  ComponentDataFactory,
  type ComponentData,
  type ComponentProperties,
} from "./componentData";

// 表单模板相关
export { FormTemplateFactory, type FormTemplate } from "./formTemplates";

// 用户场景相关
export {
  TestUserFactory,
  UserScenarioFactory,
  type TestUser,
  type UserAction,
  type UserScenario,
} from "./userScenarios";

// 测试辅助工具
export {
  AuthHelpers,
  ErrorHelpers,
  FormHelpers,
  PerformanceHelpers,
  RandomDataHelpers,
  ScenarioHelpers,
  ScreenshotHelpers,
  ValidationHelpers,
  WaitHelpers,
} from "../utils/test-helpers";

// 导入内部使用的类（避免循环导入）
import { COMPONENT_TYPES as InternalCOMPONENT_TYPES } from "../utils/selectors";
import {
  RandomDataHelpers as InternalRandomDataHelpers,
  ValidationHelpers as InternalValidationHelpers,
} from "../utils/test-helpers";
import { ComponentDataFactory as InternalComponentDataFactory } from "./componentData";
import { FormTemplateFactory as InternalFormTemplateFactory } from "./formTemplates";
import {
  TestUserFactory as InternalTestUserFactory,
  UserScenarioFactory as InternalUserScenarioFactory,
} from "./userScenarios";

// 选择器和常量
export {
  COMPONENT_GROUPS,
  COMPONENT_TYPES,
  SELECTORS,
} from "../utils/selectors";

/**
 * 测试数据工厂主类
 * 提供一站式的测试数据生成服务
 */
export class TestDataFactory {
  /**
   * 获取快速测试数据集
   * 用于快速创建基础测试场景
   */
  static getQuickTestData() {
    return {
      // 简单组件
      simpleInput: InternalComponentDataFactory.createInputComponent({
        label: "测试输入框",
        placeholder: "请输入测试数据",
        required: true,
      }),

      // 简单表单
      simpleForm: InternalFormTemplateFactory.createSimpleContactForm(),

      // 基础场景
      basicScenario:
        InternalUserScenarioFactory.createBasicFormCreationScenario(),

      // 测试用户
      testUser: InternalTestUserFactory.createEditorUser(),

      // 随机数据
      randomData: {
        name: InternalRandomDataHelpers.randomChineseName(),
        email: InternalRandomDataHelpers.randomEmail(),
        phone: InternalRandomDataHelpers.randomPhone(),
        text: InternalRandomDataHelpers.randomString(20),
      },
    };
  }

  /**
   * 获取完整测试数据集
   * 用于全面的E2E测试
   */
  static getCompleteTestData() {
    return {
      // 所有组件类型
      components: {
        input: InternalComponentDataFactory.createInputComponent(),
        textarea: InternalComponentDataFactory.createTextareaComponent(),
        select: InternalComponentDataFactory.createSelectComponent(),
        radio: InternalComponentDataFactory.createRadioComponent(),
        checkbox: InternalComponentDataFactory.createCheckboxComponent(),
        date: InternalComponentDataFactory.createDatePickerComponent(),
        employee: InternalComponentDataFactory.createEmployeePickerComponent(),
        file: InternalComponentDataFactory.createFilePickerComponent(),
        image: InternalComponentDataFactory.createImagePickerComponent(),
        table: InternalComponentDataFactory.createTableComponent(),
      },

      // 所有表单模板
      templates: InternalFormTemplateFactory.getAllTemplates(),

      // 所有用户场景
      scenarios: InternalUserScenarioFactory.getAllScenarios(),

      // 所有测试用户
      users: InternalTestUserFactory.getAllUsers(),

      // 分类数据
      categorized: {
        simpleTemplates:
          InternalFormTemplateFactory.getTemplatesByDifficulty("simple"),
        complexTemplates:
          InternalFormTemplateFactory.getTemplatesByDifficulty("complex"),
        basicScenarios:
          InternalUserScenarioFactory.getScenariosByCategory("basic"),
        advancedScenarios:
          InternalUserScenarioFactory.getScenariosByCategory("advanced"),
        essentialScenarios: InternalUserScenarioFactory.getEssentialScenarios(),
      },
    };
  }

  /**
   * 获取性能测试数据
   * 用于性能和压力测试
   */
  static getPerformanceTestData() {
    // 生成大量组件数据
    const manyComponents = Array.from({ length: 50 }, (_, index) => {
      const types = Object.values(InternalCOMPONENT_TYPES);
      const randomType = InternalRandomDataHelpers.randomChoice(types);
      return InternalComponentDataFactory.createComponentByType(randomType, {
        label: `组件${index + 1}`,
        required: Math.random() > 0.5,
      });
    });

    return {
      manyComponents,
      largeForm: {
        id: "large_performance_form",
        title: "性能测试表单",
        description: "包含大量组件的性能测试表单",
        components: manyComponents,
        layout: { columns: 3, responsive: true },
        validation: { required: [], rules: {} },
        metadata: {
          category: "性能测试",
          tags: ["性能", "大数据量"],
          difficulty: "complex" as const,
          estimatedTime: 30,
        },
      },
      performanceScenario:
        InternalUserScenarioFactory.createPerformanceTestScenario(),
    };
  }

  /**
   * 获取错误测试数据
   * 用于边界情况和错误处理测试
   */
  static getErrorTestData() {
    return {
      // 无效数据
      invalidComponent: {
        compId: "",
        compName: "",
        compType: "invalid",
        business: "",
        group: "invalid" as any,
        formData: null,
      },

      // 边界值数据
      boundaryData: {
        emptyString: "",
        longString: "A".repeat(1000),
        specialChars: "!@#$%^&*()_+-=[]{}|;:,.<>?",
        unicodeChars: "测试数据🎉",
        nullValue: null,
        undefinedValue: undefined,
      },

      // 错误场景
      errorScenario: InternalUserScenarioFactory.createErrorHandlingScenario(),

      // 无效表单模板
      invalidTemplate: {
        id: "",
        title: "",
        description: "",
        components: [],
        layout: null,
        validation: null,
        metadata: null,
      },
    };
  }

  /**
   * 创建自定义测试数据
   */
  static createCustomTestData(config: {
    componentCount?: number;
    componentTypes?: string[];
    formComplexity?: "simple" | "medium" | "complex";
    includeValidation?: boolean;
    includeRandomData?: boolean;
  }) {
    const {
      componentCount = 5,
      componentTypes = Object.values(InternalCOMPONENT_TYPES),
      formComplexity = "medium",
      includeValidation = true,
      includeRandomData = true,
    } = config;

    // 生成指定数量的组件
    const components = Array.from({ length: componentCount }, (_, index) => {
      const randomType = InternalRandomDataHelpers.randomChoice(componentTypes);
      return InternalComponentDataFactory.createComponentByType(randomType, {
        label: includeRandomData
          ? InternalRandomDataHelpers.randomString(8)
          : `字段${index + 1}`,
        required: includeValidation && Math.random() > 0.5,
      });
    });

    // 创建自定义表单模板
    const customTemplate: FormTemplate = {
      id: `custom_form_${Date.now()}`,
      title: "自定义测试表单",
      description: "根据配置生成的自定义表单",
      components,
      layout: {
        columns:
          formComplexity === "simple" ? 1 : formComplexity === "medium" ? 2 : 3,
        responsive: true,
      },
      validation: {
        required: includeValidation
          ? components
              .filter((c) => c.formData.required)
              .map((c) => c.formData.label)
          : [],
        rules: {},
      },
      metadata: {
        category: "自定义",
        tags: ["自定义", "测试"],
        difficulty: formComplexity,
        estimatedTime: componentCount * 0.5,
      },
    };

    return {
      components,
      template: customTemplate,
      randomData: includeRandomData
        ? {
            names: Array.from({ length: 10 }, () =>
              InternalRandomDataHelpers.randomChineseName()
            ),
            emails: Array.from({ length: 10 }, () =>
              InternalRandomDataHelpers.randomEmail()
            ),
            phones: Array.from({ length: 10 }, () =>
              InternalRandomDataHelpers.randomPhone()
            ),
          }
        : null,
    };
  }
}

/**
 * 测试数据验证器
 * 确保测试数据的完整性和正确性
 */
export class TestDataValidator {
  /**
   * 验证所有测试数据的完整性
   */
  static validateAllTestData(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    try {
      // 验证组件数据
      const componentTypes = Object.values(InternalCOMPONENT_TYPES);
      componentTypes.forEach((type) => {
        try {
          const component =
            InternalComponentDataFactory.createComponentByType(type);
          if (!InternalValidationHelpers.validateComponentData(component)) {
            errors.push(`Invalid component data for type: ${type}`);
          }
        } catch (error) {
          errors.push(
            `Error creating component of type ${type}: ${error.message}`
          );
        }
      });

      // 验证表单模板
      const templates = InternalFormTemplateFactory.getAllTemplates();
      templates.forEach((template) => {
        if (!InternalValidationHelpers.validateFormTemplate(template)) {
          errors.push(`Invalid form template: ${template.id}`);
        }
      });

      // 验证用户场景
      const scenarios = InternalUserScenarioFactory.getAllScenarios();
      scenarios.forEach((scenario) => {
        if (!InternalValidationHelpers.validateUserScenario(scenario)) {
          errors.push(`Invalid user scenario: ${scenario.id}`);
        }
      });
    } catch (error) {
      errors.push(`Validation error: ${error.message}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 生成测试数据报告
   */
  static generateTestDataReport(): string {
    const validation = this.validateAllTestData();
    const templates = InternalFormTemplateFactory.getAllTemplates();
    const scenarios = InternalUserScenarioFactory.getAllScenarios();
    const componentTypes = Object.values(InternalCOMPONENT_TYPES);

    return `
# 测试数据工厂报告

## 数据统计
- 支持组件类型: ${componentTypes.length}
- 预定义表单模板: ${templates.length}
- 用户场景: ${scenarios.length}

## 组件类型
${componentTypes.map((type) => `- ${type}`).join("\n")}

## 表单模板
${templates.map((t) => `- ${t.title} (${t.metadata.difficulty})`).join("\n")}

## 用户场景
${scenarios.map((s) => `- ${s.name} (${s.category})`).join("\n")}

## 验证结果
- 数据完整性: ${validation.isValid ? "✅ 通过" : "❌ 失败"}
${validation.errors.length > 0 ? `\n### 错误列表\n${validation.errors.map((e) => `- ${e}`).join("\n")}` : ""}
    `.trim();
  }
}
