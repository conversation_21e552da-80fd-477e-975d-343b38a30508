import { Locator, Page } from "@playwright/test";

/**
 * 拖拽辅助工具类
 * 提供各种拖拽操作的封装方法
 */
export class DragDropHelpers {
  constructor(private page: Page) {}

  /**
   * 基础拖拽操作
   * @param source 源元素选择器或Locator
   * @param target 目标元素选择器或Locator
   * @param options 拖拽选项
   */
  async dragAndDrop(
    source: string | Locator,
    target: string | Locator,
    options: {
      sourcePosition?: { x: number; y: number };
      targetPosition?: { x: number; y: number };
      force?: boolean;
      timeout?: number;
    } = {}
  ): Promise<void> {
    const sourceLocator =
      typeof source === "string" ? this.page.locator(source) : source;
    const targetLocator =
      typeof target === "string" ? this.page.locator(target) : target;

    await sourceLocator.dragTo(targetLocator, {
      sourcePosition: options.sourcePosition,
      targetPosition: options.targetPosition,
      force: options.force,
      timeout: options.timeout || 30000,
    });
  }

  /**
   * 精确拖拽到指定坐标
   * @param source 源元素
   * @param targetX 目标X坐标
   * @param targetY 目标Y坐标
   */
  async dragToCoordinates(
    source: string | Locator,
    targetX: number,
    targetY: number
  ): Promise<void> {
    const sourceLocator =
      typeof source === "string" ? this.page.locator(source) : source;

    // 获取源元素的边界框
    const sourceBox = await sourceLocator.boundingBox();
    if (!sourceBox) {
      throw new Error("Source element not found or not visible");
    }

    // 计算源元素中心点
    const sourceCenterX = sourceBox.x + sourceBox.width / 2;
    const sourceCenterY = sourceBox.y + sourceBox.height / 2;

    // 执行拖拽
    await this.page.mouse.move(sourceCenterX, sourceCenterY);
    await this.page.mouse.down();
    await this.page.mouse.move(targetX, targetY, { steps: 10 });
    await this.page.mouse.up();
  }

  /**
   * 组件库到画布的拖拽
   * @param componentSelector 组件库中的组件选择器
   * @param canvasSelector 画布选择器
   * @param dropPosition 在画布中的投放位置（可选）
   */
  async dragComponentToCanvas(
    componentSelector: string,
    canvasSelector: string,
    dropPosition?: { x: number; y: number }
  ): Promise<void> {
    const component = this.page.locator(componentSelector);
    const canvas = this.page.locator(canvasSelector);

    // 确保组件和画布都可见
    await component.waitFor({ state: "visible" });
    await canvas.waitFor({ state: "visible" });

    if (dropPosition) {
      // 拖拽到指定位置
      const canvasBox = await canvas.boundingBox();
      if (!canvasBox) {
        throw new Error("Canvas not found or not visible");
      }

      const targetX = canvasBox.x + dropPosition.x;
      const targetY = canvasBox.y + dropPosition.y;

      await this.dragToCoordinates(component, targetX, targetY);
    } else {
      // 拖拽到画布中心，使用force避免被阻挡元素干扰
      await this.dragAndDrop(component, canvas, {
        targetPosition: { x: 0.5, y: 0.5 }, // 相对位置，画布中心
        force: true, // 强制拖拽，忽略阻挡元素
      });
    }
  }

  /**
   * 画布内组件排序拖拽
   * @param sourceIndex 源组件索引
   * @param targetIndex 目标组件索引
   * @param containerSelector 容器选择器
   */
  async dragToReorder(
    sourceIndex: number,
    targetIndex: number,
    containerSelector: string
  ): Promise<void> {
    const container = this.page.locator(containerSelector);
    const sourceComponent = container.locator("> *").nth(sourceIndex);
    const targetComponent = container.locator("> *").nth(targetIndex);

    await this.dragAndDrop(sourceComponent, targetComponent, {
      targetPosition:
        targetIndex > sourceIndex ? { x: 0.5, y: 0.9 } : { x: 0.5, y: 0.1 },
    });
  }

  /**
   * 等待拖拽操作完成
   * @param timeout 超时时间
   */
  async waitForDragComplete(timeout: number = 5000): Promise<void> {
    // 等待一小段时间让拖拽动画完成
    await this.page.waitForTimeout(500);

    // 等待页面稳定
    await this.page.waitForLoadState("networkidle", { timeout });
  }

  /**
   * 验证拖拽操作是否成功
   * @param containerSelector 容器选择器
   * @param expectedCount 期望的组件数量
   */
  async verifyDragSuccess(
    containerSelector: string,
    expectedCount: number
  ): Promise<boolean> {
    const container = this.page.locator(containerSelector);
    const actualCount = await container.locator("> *").count();
    return actualCount === expectedCount;
  }

  /**
   * 获取拖拽反馈信息
   * @param feedbackSelector 反馈元素选择器
   */
  async getDragFeedback(feedbackSelector: string): Promise<string | null> {
    try {
      const feedback = this.page.locator(feedbackSelector);
      await feedback.waitFor({ state: "visible", timeout: 2000 });
      return await feedback.textContent();
    } catch {
      return null;
    }
  }

  /**
   * 模拟拖拽悬停效果
   * @param source 源元素
   * @param target 目标元素
   * @param hoverDuration 悬停时间（毫秒）
   */
  async dragWithHover(
    source: string | Locator,
    target: string | Locator,
    hoverDuration: number = 1000
  ): Promise<void> {
    const sourceLocator =
      typeof source === "string" ? this.page.locator(source) : source;
    const targetLocator =
      typeof target === "string" ? this.page.locator(target) : target;

    // 开始拖拽
    await sourceLocator.hover();
    await this.page.mouse.down();

    // 移动到目标并悬停
    await targetLocator.hover();
    await this.page.waitForTimeout(hoverDuration);

    // 完成拖拽
    await this.page.mouse.up();
  }

  /**
   * 批量拖拽操作
   * @param operations 拖拽操作数组
   */
  async batchDragOperations(
    operations: Array<{
      source: string | Locator;
      target: string | Locator;
      options?: any;
    }>
  ): Promise<void> {
    for (const operation of operations) {
      await this.dragAndDrop(
        operation.source,
        operation.target,
        operation.options
      );
      await this.waitForDragComplete(2000);
    }
  }

  /**
   * 取消拖拽操作
   * @param source 源元素
   */
  async cancelDrag(source: string | Locator): Promise<void> {
    const sourceLocator =
      typeof source === "string" ? this.page.locator(source) : source;

    await sourceLocator.hover();
    await this.page.mouse.down();

    // 移动一小段距离然后按ESC取消
    await this.page.mouse.move(0, 0, { steps: 5 });
    await this.page.keyboard.press("Escape");
    await this.page.mouse.up();
  }
}
