/**
 * 测试辅助函数集合
 * 提供E2E测试中常用的工具函数
 */

import { Locator, Page, expect } from "@playwright/test";
import { ComponentData } from "../fixtures/componentData";
import { FormTemplate } from "../fixtures/formTemplates";
import { UserAction, UserScenario } from "../fixtures/userScenarios";

/**
 * 等待工具类
 */
export class WaitHelpers {
  /**
   * 等待元素可见并可交互
   */
  static async waitForInteractable(
    locator: Locator,
    timeout = 10000
  ): Promise<void> {
    await expect(locator).toBeVisible({ timeout });
    await expect(locator).toBeEnabled({ timeout });
  }

  /**
   * 等待元素消失
   */
  static async waitForDisappear(
    locator: Locator,
    timeout = 10000
  ): Promise<void> {
    await expect(locator).toBeHidden({ timeout });
  }

  /**
   * 等待页面加载完成
   */
  static async waitForPageLoad(page: Page): Promise<void> {
    await page.waitForLoadState("networkidle");
    await page.waitForLoadState("domcontentloaded");
  }

  /**
   * 等待页面加载完成（宽松模式，适用于UI测试）
   */
  static async waitForPageLoadLoose(page: Page): Promise<void> {
    try {
      // 首先等待DOM加载完成
      await page.waitForLoadState("domcontentloaded", { timeout: 10000 });

      // 尝试等待网络空闲，但允许超时
      try {
        await page.waitForLoadState("networkidle", { timeout: 5000 });
      } catch (error) {
        // 网络空闲超时不影响测试继续
        console.log("网络空闲等待超时，继续执行测试");
      }

      // 等待一小段时间确保页面稳定
      await page.waitForTimeout(1000);
    } catch (error) {
      console.error("页面加载等待失败:", error);
      // 即使失败也继续执行，让具体测试来判断页面状态
    }
  }

  /**
   * 等待指定时间
   */
  static async wait(milliseconds: number): Promise<void> {
    await new Promise((resolve) => setTimeout(resolve, milliseconds));
  }

  /**
   * 等待条件满足
   */
  static async waitForCondition(
    condition: () => Promise<boolean>,
    timeout = 10000,
    interval = 100
  ): Promise<void> {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return;
      }
      await this.wait(interval);
    }
    throw new Error(`Condition not met within ${timeout}ms`);
  }
}

/**
 * 数据验证工具类
 */
export class ValidationHelpers {
  /**
   * 验证组件数据结构
   */
  static validateComponentData(component: ComponentData): boolean {
    const requiredFields = [
      "compId",
      "compName",
      "compType",
      "group",
      "formData",
    ];
    return requiredFields.every((field) => component[field] !== undefined);
  }

  /**
   * 验证表单模板结构
   */
  static validateFormTemplate(template: FormTemplate): boolean {
    const requiredFields = [
      "id",
      "title",
      "components",
      "layout",
      "validation",
      "metadata",
    ];
    return (
      requiredFields.every((field) => template[field] !== undefined) &&
      Array.isArray(template.components) &&
      template.components.length > 0
    );
  }

  /**
   * 验证用户场景结构
   */
  static validateUserScenario(scenario: UserScenario): boolean {
    const requiredFields = ["id", "name", "actions", "expectedOutcome"];
    return (
      requiredFields.every((field) => scenario[field] !== undefined) &&
      Array.isArray(scenario.actions) &&
      scenario.actions.length > 0
    );
  }

  /**
   * 验证邮箱格式
   */
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * 验证手机号格式
   */
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  }
}

/**
 * 随机数据生成工具类
 */
export class RandomDataHelpers {
  /**
   * 生成随机字符串
   */
  static randomString(length = 8): string {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成随机邮箱
   */
  static randomEmail(): string {
    const domains = ["example.com", "test.com", "demo.org"];
    const username = this.randomString(8).toLowerCase();
    const domain = domains[Math.floor(Math.random() * domains.length)];
    return `${username}@${domain}`;
  }

  /**
   * 生成随机手机号
   */
  static randomPhone(): string {
    const prefixes = [
      "130",
      "131",
      "132",
      "133",
      "134",
      "135",
      "136",
      "137",
      "138",
      "139",
    ];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    const suffix = Math.floor(Math.random() * 100000000)
      .toString()
      .padStart(8, "0");
    return prefix + suffix;
  }

  /**
   * 生成随机中文姓名
   */
  static randomChineseName(): string {
    const surnames = [
      "张",
      "王",
      "李",
      "赵",
      "刘",
      "陈",
      "杨",
      "黄",
      "周",
      "吴",
    ];
    const names = ["伟", "芳", "娜", "敏", "静", "丽", "强", "磊", "军", "洋"];
    const surname = surnames[Math.floor(Math.random() * surnames.length)];
    const name = names[Math.floor(Math.random() * names.length)];
    return surname + name;
  }

  /**
   * 生成随机日期
   */
  static randomDate(start?: Date, end?: Date): Date {
    const startDate = start || new Date(1990, 0, 1);
    const endDate = end || new Date();
    return new Date(
      startDate.getTime() +
        Math.random() * (endDate.getTime() - startDate.getTime())
    );
  }

  /**
   * 从数组中随机选择元素
   */
  static randomChoice<T>(array: T[]): T {
    return array[Math.floor(Math.random() * array.length)];
  }

  /**
   * 从数组中随机选择多个元素
   */
  static randomChoices<T>(array: T[], count: number): T[] {
    const shuffled = [...array].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, array.length));
  }
}

/**
 * 截图工具类
 */
export class ScreenshotHelpers {
  /**
   * 截取页面截图
   */
  static async takePageScreenshot(page: Page, name: string): Promise<void> {
    await page.screenshot({
      path: `e2e/reports/screenshots/${name}-${Date.now()}.png`,
      fullPage: true,
    });
  }

  /**
   * 截取元素截图
   */
  static async takeElementScreenshot(
    locator: Locator,
    name: string
  ): Promise<void> {
    await locator.screenshot({
      path: `e2e/reports/screenshots/${name}-element-${Date.now()}.png`,
    });
  }

  /**
   * 在测试失败时自动截图
   */
  static async screenshotOnFailure(
    page: Page,
    testName: string
  ): Promise<void> {
    await this.takePageScreenshot(page, `failure-${testName}`);
  }
}

/**
 * 表单操作工具类
 */
export class FormHelpers {
  /**
   * 填写表单字段
   */
  static async fillFormField(
    page: Page,
    fieldName: string,
    value: string
  ): Promise<void> {
    const field = page.locator(
      `[data-testid="form-field-${fieldName}"], input[name="${fieldName}"]`
    );
    await field.fill(value);
  }

  /**
   * 选择下拉选项
   */
  static async selectOption(
    page: Page,
    fieldName: string,
    value: string
  ): Promise<void> {
    const select = page.locator(
      `[data-testid="form-field-${fieldName}"], select[name="${fieldName}"]`
    );
    await select.selectOption(value);
  }

  /**
   * 点击单选框
   */
  static async clickRadio(
    page: Page,
    fieldName: string,
    value: string
  ): Promise<void> {
    const radio = page.locator(`input[name="${fieldName}"][value="${value}"]`);
    await radio.click();
  }

  /**
   * 点击复选框
   */
  static async clickCheckbox(
    page: Page,
    fieldName: string,
    checked = true
  ): Promise<void> {
    const checkbox = page.locator(`input[name="${fieldName}"]`);
    if (checked !== (await checkbox.isChecked())) {
      await checkbox.click();
    }
  }

  /**
   * 验证表单字段值
   */
  static async verifyFieldValue(
    page: Page,
    fieldName: string,
    expectedValue: string
  ): Promise<void> {
    const field = page.locator(
      `[data-testid="form-field-${fieldName}"], input[name="${fieldName}"]`
    );
    await expect(field).toHaveValue(expectedValue);
  }
}

/**
 * 场景执行工具类
 */
export class ScenarioHelpers {
  /**
   * 执行用户操作
   */
  static async executeUserAction(
    page: Page,
    action: UserAction
  ): Promise<void> {
    switch (action.type) {
      case "click":
        await page.locator(action.target).click();
        break;
      case "input":
        await page.locator(action.target).fill(action.value);
        break;
      case "select":
        await page.locator(action.target).selectOption(action.value);
        break;
      case "wait":
        await WaitHelpers.wait(action.value || 1000);
        break;
      case "verify":
        await expect(page.locator(action.target)).toBeVisible();
        break;
      case "drag":
        // 拖拽操作需要特殊处理
        const source = page.locator(action.target);
        const target = page.locator(action.value);
        await source.dragTo(target);
        break;
      default:
        throw new Error(`Unsupported action type: ${action.type}`);
    }
  }

  /**
   * 执行完整用户场景
   */
  static async executeScenario(
    page: Page,
    scenario: UserScenario
  ): Promise<void> {
    console.log(`开始执行场景: ${scenario.name}`);

    for (const action of scenario.actions) {
      console.log(`执行操作: ${action.description}`);
      await this.executeUserAction(page, action);
      await WaitHelpers.wait(500); // 操作间隔
    }

    console.log(`场景执行完成: ${scenario.name}`);
  }
}

/**
 * 性能监控工具类
 */
export class PerformanceHelpers {
  /**
   * 测量操作执行时间
   */
  static async measureTime<T>(
    operation: () => Promise<T>
  ): Promise<{ result: T; duration: number }> {
    const startTime = Date.now();
    const result = await operation();
    const duration = Date.now() - startTime;
    return { result, duration };
  }

  /**
   * 监控页面性能指标
   */
  static async getPerformanceMetrics(page: Page): Promise<any> {
    return await page.evaluate(() => {
      const navigation = performance.getEntriesByType(
        "navigation"
      )[0] as PerformanceNavigationTiming;
      return {
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        domContentLoaded:
          navigation.domContentLoadedEventEnd -
          navigation.domContentLoadedEventStart,
        firstPaint:
          performance.getEntriesByName("first-paint")[0]?.startTime || 0,
        firstContentfulPaint:
          performance.getEntriesByName("first-contentful-paint")[0]
            ?.startTime || 0,
      };
    });
  }

  /**
   * 检查内存使用情况
   */
  static async getMemoryUsage(page: Page): Promise<any> {
    return await page.evaluate(() => {
      return (performance as any).memory
        ? {
            usedJSHeapSize: (performance as any).memory.usedJSHeapSize,
            totalJSHeapSize: (performance as any).memory.totalJSHeapSize,
            jsHeapSizeLimit: (performance as any).memory.jsHeapSizeLimit,
          }
        : null;
    });
  }
}

/**
 * 错误处理工具类
 */
export class ErrorHelpers {
  /**
   * 捕获并记录页面错误
   */
  static setupErrorCapture(page: Page): void {
    page.on("pageerror", (error) => {
      console.error("页面错误:", error.message);
    });

    page.on("console", (msg) => {
      if (msg.type() === "error") {
        console.error("控制台错误:", msg.text());
      }
    });
  }

  /**
   * 验证错误消息显示
   */
  static async verifyErrorMessage(
    page: Page,
    expectedMessage: string
  ): Promise<void> {
    const errorElement = page.locator(
      ".error-message, .semi-notification-error"
    );
    await expect(errorElement).toBeVisible();
    await expect(errorElement).toContainText(expectedMessage);
  }

  /**
   * 清除错误状态
   */
  static async clearErrors(page: Page): Promise<void> {
    const closeButtons = page.locator(".error-close, .semi-notification-close");
    const count = await closeButtons.count();
    for (let i = 0; i < count; i++) {
      await closeButtons.nth(i).click();
    }
  }
}

/**
 * 认证辅助工具类
 */
export class AuthHelpers {
  /**
   * 执行登录操作（优先使用localStorage模拟，备用真实登录）
   */
  static async login(
    page: Page,
    username: string = process.env.TEST_USERNAME || "admin",
    password: string = process.env.TEST_PASSWORD || "safe12345"
  ): Promise<void> {
    // 策略1: 直接通过localStorage设置认证状态（推荐用于测试）
    try {
      await this.mockLogin(page, username);
      console.log("✅ 使用localStorage模拟登录成功");
      return;
    } catch (error) {
      console.log("⚠️ localStorage模拟登录失败，尝试真实登录流程");
    }

    // 策略2: 真实登录流程（备用方案）
    await this.realLogin(page, username, password);
  }

  /**
   * 通过真实API进行登录（E2E测试推荐）
   */
  static async realApiLogin(
    page: Page,
    username: string = process.env.TEST_USERNAME || "admin",
    password: string = process.env.TEST_PASSWORD || "safe12345"
  ): Promise<void> {
    try {
      // 导航到登录页面
      await page.goto("/login", { timeout: 30000 });
      await WaitHelpers.waitForPageLoadLoose(page);

      // 从环境变量构建正确的API URL
      const protocol = process.env.VITE_PROTOCOL || "https";
      const host = process.env.VITE_HOST || "api-test.vren-tech.com";
      const port = process.env.VITE_PORT || "443";
      const version = process.env.VITE_VERSION || "v1";

      // 构建完整的API URL：${protocol}://${host}:${port}/${version}/system/login
      const base_url = `${protocol}://${host}:${port}`;
      const api_url = `${base_url}/${version}`;
      const loginUrl = `${api_url}/system/login`;

      console.log(
        "环境变量 - Protocol:",
        protocol,
        "Host:",
        host,
        "Port:",
        port,
        "Version:",
        version
      );
      console.log("API URL:", api_url);
      console.log("登录URL:", loginUrl);

      // 调用真实的登录API，使用vdebug header绕过验证码
      const response = await page.evaluate(
        async ({ username, password, loginUrl }) => {
          try {
            console.log("当前页面URL:", window.location.href);
            console.log("登录URL:", loginUrl);

            // 使用后端为测试环境配置的固定验证码，通过Authorization header绕过CORS限制
            const response = await fetch(loginUrl, {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
                Authorization: "vdebug 6c3e8e4aa985fd0fa6d7a9bf1c3ded58", // 使用Authorization header绕过CORS
              },
              body: JSON.stringify({
                username: username,
                password: password,
                captchaKey: "test",
                captchaAnswer: "6c3e8e4aa985fd0fa6d7a9bf1c3ded58", // 固定的测试验证码
              }),
            });

            // 检查响应是否成功
            if (!response.ok) {
              return {
                status: response.status,
                error: `HTTP ${response.status}: ${response.statusText}`,
                data: null,
              };
            }

            // 尝试解析JSON
            const text = await response.text();
            let data: any;
            try {
              data = JSON.parse(text);
            } catch (e) {
              // 打印JSON解析错误的详细信息
              console.error("🔴 JSON解析错误详情:", {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
                responseText: text.substring(0, 500), // 显示更多内容用于调试
                parseError: e.message,
                loginUrl,
                username,
              });

              return {
                status: response.status,
                error: `Invalid JSON response (${response.status} ${response.statusText}): ${text.substring(0, 100)}...`,
                data: null,
              };
            }

            return { status: response.status, data, error: null };
          } catch (error) {
            // 打印网络错误的详细信息
            console.error("🔴 网络错误详情:", {
              errorMessage: error.message,
              errorName: error.name,
              errorStack: error.stack,
              loginUrl,
              username,
            });

            return {
              status: 0,
              error: `Network error (${error.name}): ${error.message}`,
              data: null,
            };
          }
        },
        { username, password, loginUrl }
      );

      // 检查是否有错误
      if (response.error) {
        // 打印详细的错误信息
        console.error("🔴 API调用错误详情:", {
          error: response.error,
          status: response.status,
          data: response.data,
          loginUrl,
          username,
        });
        throw new Error(`API调用失败: ${response.error}`);
      }

      if (
        response.status === 200 &&
        response.data &&
        response.data.code === 0
      ) {
        // 登录成功，设置认证数据到localStorage
        await page.evaluate((loginData) => {
          const userData = {
            username: loginData.employee.name,
            token: loginData.authToken,
            userInfo: loginData.employee,
            refreshToken: loginData.refreshToken,
            expireTime: loginData.expireTime,
          };

          // 设置认证数据
          localStorage.setItem("vr-userinfo", JSON.stringify(userData));

          // ✅ 关键修复：同时设置独立的token，供API请求使用
          localStorage.setItem("token", loginData.authToken);

          // 设置其他认证标志（如果API返回了）
          if (loginData.pfHeaderKey && loginData.pfHeaderValue) {
            localStorage.setItem("customHeadKey", loginData.pfHeaderKey);
            localStorage.setItem("customHeadValue", loginData.pfHeaderValue);
          }
        }, response.data.data);

        console.log("✅ 真实API登录成功");
      } else {
        // 打印详细的登录失败信息
        const errorDetails = {
          status: response.status,
          code: response.data?.code,
          message: response.data?.message,
          data: response.data,
          loginUrl,
          username,
        };

        console.error("🔴 登录失败详情:", errorDetails);

        // 根据API文档，构建更详细的错误信息
        let errorMessage = "登录失败";
        if (response.data?.message) {
          errorMessage += `: ${response.data.message}`;
        }
        if (response.data?.code !== undefined) {
          errorMessage += ` (错误码: ${response.data.code})`;
        }
        if (response.status !== 200) {
          errorMessage += ` (HTTP状态: ${response.status})`;
        }

        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error("❌ 真实API登录失败:", error);
      throw error;
    }
  }

  /**
   * 通过localStorage直接设置认证状态（备用方案）
   */
  static async mockLogin(
    page: Page,
    username: string = "admin"
  ): Promise<void> {
    // 导航到登录页面
    await page.goto("/login", { timeout: 30000 });
    await WaitHelpers.waitForPageLoadLoose(page);

    // 直接设置localStorage认证数据，使用正确的格式和key
    await page.evaluate((username) => {
      const mockUserData = {
        username: username,
        token: "mock-test-token-" + Date.now(), // 这是authToken
        userInfo: {
          id: 1,
          employeeId: "1", // 应该是字符串
          name: username,
        },
        refreshToken: "mock-refresh-token-" + Date.now(),
        expireTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // ISO格式的过期时间
      };

      // 设置认证数据到localStorage，使用正确的key
      localStorage.setItem("vr-userinfo", JSON.stringify(mockUserData)); // 正确的key

      // 设置其他必需的认证标志（根据实际应用需求）
      localStorage.setItem("customHeadKey", "test-key");
      localStorage.setItem("customHeadValue", "test-value");
    }, username);

    // 刷新页面以应用认证状态
    await page.reload({ waitUntil: "networkidle" });
    await WaitHelpers.waitForPageLoadLoose(page);

    // 验证是否成功跳转到认证后的页面
    const currentUrl = page.url();
    if (currentUrl.includes("/login")) {
      // 如果还在登录页面，尝试导航到首页
      await page.goto("/", { timeout: 30000 });
      await WaitHelpers.waitForPageLoadLoose(page);
    }
  }

  /**
   * 真实登录流程（备用方案）
   */
  static async realLogin(
    page: Page,
    username: string,
    password: string
  ): Promise<void> {
    // 导航到登录页面
    await page.goto("/login", { timeout: 30000 });
    await WaitHelpers.waitForPageLoadLoose(page);

    // 等待登录表单加载
    await page.waitForSelector("form", { timeout: 10000 });

    // 填写用户名
    const usernameInput = page
      .locator(
        'input[field="username"], input[name="username"], input[placeholder*="用户名"]'
      )
      .first();
    await usernameInput.fill(username);

    // 填写密码
    const passwordInput = page
      .locator(
        'input[field="password"], input[name="password"], input[type="password"]'
      )
      .first();
    await passwordInput.fill(password);

    // 处理验证码（在测试环境中尝试绕过或使用固定值）
    const captchaInput = page
      .locator(
        'input[field="captchaAnswer"], input[name="captcha"], input[placeholder*="验证码"]'
      )
      .first();
    const captchaCount = await captchaInput.count();

    if (captchaCount > 0) {
      // 尝试使用常见的测试验证码
      await captchaInput.fill("1234");
    }

    // 提交登录表单
    const submitButton = page
      .locator(
        'button[htmlType="submit"], button[type="submit"], button:has-text("登录")'
      )
      .first();
    await submitButton.click();

    // 等待登录完成（检查是否跳转或出现成功提示）
    try {
      // 等待页面跳转或登录成功的标志
      await Promise.race([
        page.waitForURL((url) => !url.pathname.includes("/login"), {
          timeout: 10000,
        }),
        page.waitForSelector(".semi-toast-success", { timeout: 5000 }),
        page.waitForSelector('[data-testid="user-menu"], .user-info, .logout', {
          timeout: 5000,
        }),
      ]);

      console.log("✅ 真实登录成功");
    } catch (error) {
      // 检查是否有错误提示
      const errorMessage = await page
        .locator(".semi-toast-error, .error-message, .login-error")
        .first()
        .textContent()
        .catch(() => null);
      if (errorMessage) {
        throw new Error(`登录失败: ${errorMessage}`);
      }

      // 如果没有明确的错误，可能是验证码问题，尝试继续
      console.log("⚠️ 登录状态不明确，继续执行测试");
    }
  }

  /**
   * 检查是否已登录
   */
  static async isLoggedIn(page: Page): Promise<boolean> {
    try {
      // 首先检查localStorage中的认证状态
      const hasAuthData = await page.evaluate(() => {
        const userInfo = localStorage.getItem("vr-userinfo"); // 使用正确的key
        const customKey = localStorage.getItem("customHeadKey");

        // 检查是否有有效的认证数据
        if (userInfo) {
          try {
            const data = JSON.parse(userInfo);
            return !!(data.token && data.userInfo); // 必须同时有token和userInfo
          } catch {
            return false;
          }
        }

        // 检查是否有自定义认证头
        return !!(customKey && localStorage.getItem("customHeadValue"));
      });

      if (hasAuthData) {
        // 如果有认证数据，就认为已登录（不依赖当前URL）
        return true;
      }

      // 如果没有localStorage认证数据，检查页面元素
      const loggedInIndicators = [
        '[data-testid="user-menu"]',
        ".user-info",
        ".logout",
        ".user-avatar",
        'button:has-text("退出")',
        'button:has-text("登出")',
      ];

      for (const selector of loggedInIndicators) {
        const element = await page.locator(selector).first().count();
        if (element > 0) {
          return true;
        }
      }

      // 最后检查URL
      const currentUrl = page.url();
      return !currentUrl.includes("/login");
    } catch (error) {
      return false;
    }
  }

  /**
   * 登出操作
   */
  static async logout(page: Page): Promise<void> {
    try {
      // 直接清除认证状态（推荐用于测试）
      await page.evaluate(() => {
        // 清除所有认证相关的localStorage数据
        localStorage.removeItem("userInfo");
        localStorage.removeItem("_auth");
        localStorage.removeItem("customHeadKey");
        localStorage.removeItem("customHeadValue");

        // 清除其他可能的认证数据
        sessionStorage.clear();
      });

      // 导航到登录页面
      await page.goto("/login", { timeout: 30000 });
      await WaitHelpers.waitForPageLoadLoose(page);

      console.log("✅ 清除认证状态成功");
    } catch (error) {
      console.log("⚠️ 登出操作失败，尝试完全清除存储");
      try {
        await page.evaluate(() => {
          localStorage.clear();
          sessionStorage.clear();
        });

        await page.goto("/login", { timeout: 30000 });
        console.log("✅ 完全清除存储成功");
      } catch (finalError) {
        console.error("❌ 登出操作完全失败:", finalError);
      }
    }
  }

  /**
   * 确保已登录（如果未登录则执行登录）
   */
  static async ensureLoggedIn(page: Page): Promise<void> {
    const isLoggedIn = await this.isLoggedIn(page);
    if (!isLoggedIn) {
      await this.login(page);
    }
  }
}
