/**
 * 选择器常量
 * 基于实际FormEditor组件的DOM结构和CSS类名
 */

export const SELECTORS = {
  // 主要区域 - 基于实际的布局结构
  FORM_EDITOR: ".form-sandbox__content", // 使用实际存在的画布区域作为主容器
  COMPONENT_LIBRARY: ".bg-white.rounded-md.h-full.pb-4", // 左侧组件库实际的类名
  CANVAS_AREA: ".form-sandbox__content", // 中间画布区域
  PROPERTY_PANEL: '[data-testid="col"]:nth-child(3)', // 右侧属性面板
  TOOLBAR: ".form-sandbox__operation", // 顶部工具栏

  // 组件库区域
  COMPONENT_LIBRARY_TITLE: ".text-sm.my-2.text-slate-400", // 组件分组标题
  COMPONENT_ITEM: ".btn.btn-sm.btn-primary.rounded.no-animation", // 组件按钮

  // 画布区域
  CANVAS_CONTAINER: ".form-sandbox__payground",
  SORTABLE_CONTAINER: '[data-testid="sortable-container"]',
  FORM_PREVIEW: '[data-testid="form"]',

  // 工具栏按钮
  SAVE_BTN: 'button:has-text("保存")',
  RESET_BTN: 'button:has-text("重置")',
  IMPORT_BTN: 'button:has-text("导入")',
  EXPORT_BTN: 'button:has-text("导出")',

  // 组件库中的具体组件按钮 - 使用精确的属性选择器
  COMPONENT_INPUT: 'button[comp-type="input"]',
  COMPONENT_TEXTAREA: 'button[comp-type="textarea"]',
  COMPONENT_SELECT: 'button[comp-type="select"]', // 推测
  COMPONENT_RADIO: 'button[comp-type="radio"]', // 推测
  COMPONENT_CHECKBOX: 'button[comp-type="checkbox"]', // 推测
  COMPONENT_DATE: 'button[comp-type="date"]', // 推测
  COMPONENT_EMPLOYEE: 'button[comp-type="employee"]', // 推测
  COMPONENT_FILE: 'button[comp-type="file"]', // 推测
  COMPONENT_IMAGE: 'button[comp-type="image"]', // 推测
  COMPONENT_TABLE: 'button[comp-type="table"]', // 推测

  // 布局控制
  ADD_COLUMN: 'button:has-text("加一列")',
  REMOVE_COLUMN: 'button:has-text("减一列")',

  // 画布中的组件（基于调试结果）
  CANVAS_COMPONENT: ".form-sandbox__payground > div[comp-type]",
  SELECTED_COMPONENT: ".form-sandbox__payground > div[comp-type].selected",

  // 属性面板
  PROPERTY_TITLE: 'text="额外属性"',
  PROPERTY_FORM: '[data-testid="property-form"]',
  PROPERTY_INPUT: '[data-testid^="property-input-"]',

  // 表单项
  FORM_ITEM: '[data-testid^="form-input-"]',
  FORM_INPUT: '[data-testid^="input-"]',
  FORM_LABEL: "label",

  // 模态框
  MODAL: ".form-editor-modal",
  MODAL_TITLE: ".semi-modal-title",
  MODAL_CONTENT: ".semi-modal-content",

  // 通用元素
  ROW: '[data-testid="row"]',
  COL: '[data-testid="col"]',
  BUTTON: "button",
  INPUT: "input",
  TEXTAREA: "textarea",
  SELECT: "select",

  // 拖拽相关
  SORTABLE_ITEM: ".sortable-item",
  DRAG_HANDLE: ".drag-handle",
  DROP_ZONE: ".drop-zone",

  // 状态指示器
  LOADING: ".loading",
  ERROR: ".error",
  SUCCESS: ".success",
  DRAFT_BADGE: '.semi-badge:has-text("草稿")',
} as const;

// 组件类型映射
export const COMPONENT_TYPES = {
  INPUT: "input",
  TEXTAREA: "textarea",
  SELECT: "selector",
  RADIO: "radio",
  CHECKBOX: "checkbox",
  DATE: "datePicker",
  EMPLOYEE: "employeePicker",
  FILE: "annexFilePicker",
  IMAGE: "annexImgPicker",
  TABLE: "table",
} as const;

// 组件分组
export const COMPONENT_GROUPS = {
  BASE: "base",
  BUSINESS: "business",
  LAYOUT: "layout",
} as const;

/**
 * 动态选择器生成器
 */
export const selectorGenerators = {
  /**
   * 生成组件选择器
   */
  component: (type: string) => `[data-testid="component-${type}"]`,

  /**
   * 生成画布组件选择器
   */
  canvasComponent: (id: string) => `[data-testid="canvas-component-${id}"]`,

  /**
   * 生成属性选择器
   */
  property: (name: string) => `[data-testid="prop-${name}"]`,

  /**
   * 生成加载项选择器
   */
  loadItem: (id: string) => `[data-testid="load-item-${id}"]`,

  /**
   * 生成表单字段选择器
   */
  formField: (name: string) => `[data-testid="form-field-${name}"]`,
};

/**
 * 常用的CSS选择器
 */
export const CSS_SELECTORS = {
  // 通用状态
  LOADING: ".loading",
  DISABLED: ".disabled",
  SELECTED: ".selected",
  ACTIVE: ".active",
  HIDDEN: ".hidden",

  // 拖拽状态
  DRAGGING: ".dragging",
  DRAG_OVER: ".drag-over",
  DROP_TARGET: ".drop-target",

  // 表单状态
  INVALID: ".invalid",
  VALID: ".valid",
  REQUIRED: ".required",

  // 布局
  SIDEBAR: ".sidebar",
  MAIN_CONTENT: ".main-content",
  HEADER: ".header",
  FOOTER: ".footer",
} as const;
