# Page Object 架构文档

## 📋 概述

本目录包含了FormEditor E2E测试的Page Object模式实现。Page Object模式将页面元素和操作封装在类中，提供了更好的代码复用性和维护性。

## 🏗️ 架构设计

### 类层次结构

```
BasePage (抽象基类)
├── FormEditorPage (主页面对象)
├── ComponentLibraryPage (组件库页面对象)
└── PropertyPanelPage (属性面板页面对象)
```

### 核心组件

#### 1. BasePage
基础页面对象类，提供所有页面对象的通用功能：
- 元素等待和交互
- 拖拽操作
- 截图功能
- 通用工具方法

#### 2. FormEditorPage
表单编辑器主页面对象，负责：
- 页面导航和加载
- 整体页面状态管理
- 工具栏操作（保存、重置、导入、导出）
- 画布操作（组件拖拽、选择、删除）
- 协调子页面对象

#### 3. ComponentLibraryPage
组件库页面对象，负责：
- 组件按钮交互
- 组件拖拽操作
- 布局控制（添加/删除列）
- 组件状态查询

#### 4. PropertyPanelPage
属性面板页面对象，负责：
- 属性设置和获取
- 不同类型属性的处理（文本、数字、布尔、选择）
- 属性验证
- 批量属性操作

## 🚀 使用方法

### 基础使用

```typescript
import { test } from '@playwright/test';
import { FormEditorPage } from '../pages/FormEditorPage';

test('基础表单编辑测试', async ({ page }) => {
  const formEditor = new FormEditorPage(page);
  
  // 导航到页面
  await formEditor.goto();
  await formEditor.waitForEditorLoad();
  
  // 添加组件
  await formEditor.dragComponentToCanvas('INPUT');
  
  // 保存表单
  await formEditor.saveForm();
});
```

### 使用测试套件

```typescript
import { FormEditorTestSuite } from '../pages';

test('完整表单创建流程', async ({ page }) => {
  const testSuite = new FormEditorTestSuite(page);
  await testSuite.initialize();
  
  // 创建表单配置
  const formConfig = {
    components: [
      { type: 'INPUT', name: '姓名', group: 'base' },
      { type: 'EMAIL', name: '邮箱', group: 'base' }
    ],
    layout: { columns: 2, responsive: true },
    validation: { required: ['姓名'], rules: {} }
  };
  
  // 执行完整流程
  await testSuite.createCompleteForm(formConfig);
  
  // 验证结果
  const isValid = await testSuite.validateFormConfiguration(formConfig);
  expect(isValid).toBe(true);
});
```

### 子页面对象使用

```typescript
test('组件库操作', async ({ page }) => {
  const formEditor = new FormEditorPage(page);
  await formEditor.goto();
  
  // 直接使用组件库页面对象
  const componentLibrary = formEditor.componentLibrary;
  
  // 检查组件是否存在
  const hasInput = await componentLibrary.hasComponent('INPUT');
  
  // 获取所有可见组件
  const visibleComponents = await componentLibrary.getVisibleComponents();
  
  // 添加列
  await componentLibrary.addColumn();
});
```

## 🔧 选择器策略

### 选择器优先级

1. **data-testid** (最高优先级) - 专门为测试添加的属性
2. **CSS类名** - 稳定的样式类名
3. **文本内容** - 按钮文本等用户可见内容
4. **元素属性** - id、name等属性
5. **CSS选择器** (最低优先级) - 复杂的CSS选择器

### 选择器示例

```typescript
// 优先使用data-testid
FORM_PREVIEW: '[data-testid="form"]',

// 使用稳定的CSS类名
COMPONENT_LIBRARY: '.form-sandbox__components',

// 使用文本内容
SAVE_BTN: 'button:has-text("保存")',

// 组合选择器
PROPERTY_INPUT: '[data-testid^="property-input-"]',
```

## 📊 最佳实践

### 1. 页面对象设计原则

- **单一职责**: 每个页面对象只负责一个页面区域
- **封装性**: 隐藏实现细节，提供简洁的API
- **可复用性**: 方法可以在不同测试中复用
- **可维护性**: 选择器变化时只需修改一处

### 2. 方法命名规范

```typescript
// 动作方法使用动词开头
async clickSaveButton()
async dragComponentToCanvas()
async setProperty()

// 查询方法使用get/is/has开头
async getComponentCount()
async isVisible()
async hasUnsavedChanges()

// 等待方法使用wait开头
async waitForLoad()
async waitForVisible()
```

### 3. 错误处理

```typescript
// 提供有意义的错误信息
async getComponentButton(componentType: string): Promise<Locator> {
  const button = this.componentButtons[componentType];
  if (!button) {
    throw new Error(`Component type ${componentType} not found`);
  }
  return button;
}

// 使用try-catch处理可选操作
async isElementVisible(locator: Locator): Promise<boolean> {
  try {
    await expect(locator).toBeVisible({ timeout: 5000 });
    return true;
  } catch {
    return false;
  }
}
```

### 4. 等待策略

```typescript
// 等待元素可见
await this.waitForVisible(this.saveBtn);

// 等待网络空闲
await this.waitForNetworkIdle();

// 等待特定状态
await this.waitForSaveComplete();

// 组合等待
async waitForEditorLoad(): Promise<void> {
  await this.waitForVisible(this.formEditor);
  await this.waitForVisible(this.canvasArea);
  await this.componentLibrary.waitForLoad();
}
```

## 🧪 测试组织

### 测试文件结构

```
e2e/tests/
├── page-objects/           # Page Object验证测试
├── basic/                  # 基础功能测试
├── components/             # 组件操作测试
├── properties/             # 属性配置测试
└── integration/            # 集成测试
```

### 测试分层

1. **Page Object验证测试** - 验证页面对象本身的功能
2. **单元功能测试** - 测试单个功能点
3. **集成测试** - 测试完整的用户流程
4. **端到端测试** - 测试完整的业务场景

## 🔍 调试和维护

### 调试技巧

```typescript
// 添加截图
await formEditor.screenshot('debug-state');

// 获取页面状态
const state = await testSuite.getAllStates();
console.log('Current state:', state);

// 等待调试
await formEditor.wait(5000);
```

### 维护指南

1. **定期更新选择器** - 当页面结构变化时及时更新
2. **添加新方法** - 为新功能添加对应的页面对象方法
3. **重构公共方法** - 将重复的操作提取到BasePage
4. **更新文档** - 保持文档与代码同步

## 📈 扩展指南

### 添加新页面对象

1. 继承BasePage类
2. 定义页面特有的定位器
3. 实现页面特有的操作方法
4. 添加到index.ts导出
5. 编写验证测试

### 添加新组件类型

1. 更新COMPONENT_TYPES常量
2. 添加对应的选择器
3. 更新ComponentLibraryPage方法
4. 添加属性配置支持
5. 编写测试用例
