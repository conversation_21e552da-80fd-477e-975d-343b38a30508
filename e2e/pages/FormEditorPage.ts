import { Locator, Page } from "@playwright/test";
import { SpecialWorkRoutes } from "../../src/utils/routerConstants";
import { COMPONENT_TYPES, SELECTORS } from "../utils/selectors";
import { BasePage } from "./BasePage";
import { ComponentLibraryPage } from "./ComponentLibraryPage";
import { PropertyPanelPage } from "./PropertyPanelPage";

/**
 * 表单编辑器主页面对象
 * 负责整体页面的导航和协调各个子页面对象
 */
export class FormEditorPage extends BasePage {
  // 子页面对象
  public readonly componentLibrary: ComponentLibraryPage;
  public readonly propertyPanel: PropertyPanelPage;

  // 主要区域定位器
  private readonly formEditor: Locator;
  private readonly _canvasArea: Locator;
  private readonly _toolbar: Locator;

  // 工具栏按钮
  private readonly saveBtn: Locator;
  private readonly resetBtn: Locator;
  private readonly importBtn: Locator;
  private readonly exportBtn: Locator;

  // 画布相关
  private readonly canvasContainer: Locator;
  private readonly sortableContainer: Locator;
  private readonly formPreview: Locator;

  constructor(page: Page) {
    super(page);

    // 初始化子页面对象
    this.componentLibrary = new ComponentLibraryPage(page);
    this.propertyPanel = new PropertyPanelPage(page);

    // 初始化定位器
    this.formEditor = page.locator(SELECTORS.FORM_EDITOR);
    this._canvasArea = page.locator(SELECTORS.CANVAS_CONTAINER); // 使用实际的拖拽目标区域
    this._toolbar = page.locator(SELECTORS.TOOLBAR);

    // 工具栏按钮
    this.saveBtn = page.locator(SELECTORS.SAVE_BTN);
    this.resetBtn = page.locator(SELECTORS.RESET_BTN);
    this.importBtn = page.locator(SELECTORS.IMPORT_BTN);
    this.exportBtn = page.locator(SELECTORS.EXPORT_BTN);

    // 画布相关
    this.canvasContainer = page.locator(SELECTORS.CANVAS_CONTAINER);
    this.sortableContainer = page.locator(SELECTORS.SORTABLE_CONTAINER);
    this.formPreview = page.locator(SELECTORS.FORM_PREVIEW);
  }

  /**
   * 获取画布区域定位器
   */
  get canvasArea(): Locator {
    return this._canvasArea;
  }

  /**
   * 获取工具栏定位器
   */
  get toolbar(): Locator {
    return this._toolbar;
  }

  /**
   * 导航到表单编辑器页面
   */
  async goto(url?: string): Promise<void> {
    const targetUrl = url || `${SpecialWorkRoutes.FORM_CONFIG}/1`; // 使用路由常量
    await this.page.goto(targetUrl);
    await this.waitForLoad();
  }

  /**
   * 等待表单编辑器加载完成
   */
  async waitForEditorLoad(): Promise<void> {
    try {
      // 增加超时时间，分步等待关键元素
      console.log("🔍 等待画布区域加载...");
      await this.waitForVisible(this._canvasArea, 15000); // form-sandbox__content

      console.log("🔍 等待工具栏加载...");
      await this.waitForVisible(
        this.page.locator(".form-sandbox__operation"),
        15000
      ); // 工具栏

      console.log("🔍 等待组件库加载...");
      await this.componentLibrary.waitForLoad();

      // 等待网络空闲，确保页面完全加载
      console.log("🔍 等待网络空闲...");
      await this.page.waitForLoadState("networkidle", { timeout: 10000 });

      console.log("✅ 表单编辑器加载完成");
    } catch (error) {
      console.error("❌ 表单编辑器加载失败:", error.message);

      // 提供调试信息
      const currentUrl = this.page.url();
      console.log("🔍 当前URL:", currentUrl);

      // 检查页面是否被重定向
      if (currentUrl.includes("/login")) {
        throw new Error("页面被重定向到登录页面，可能是认证失败");
      }

      throw error;
    }
  }

  /**
   * 检查页面是否已加载
   */
  async isLoaded(): Promise<boolean> {
    // 检查实际存在的关键元素
    return await this.isElementVisible(this._canvasArea);
  }

  /**
   * 保存表单配置
   */
  async saveForm(): Promise<void> {
    await this.waitAndClick(this.saveBtn);
    await this.wait(2000); // 等待保存完成
  }

  /**
   * 重置表单配置
   */
  async resetForm(): Promise<void> {
    await this.waitAndClick(this.resetBtn);
    // 等待确认对话框并点击确认
    const confirmBtn = this.page.locator('button:has-text("确认")');
    if (await this.isElementVisible(confirmBtn)) {
      await this.clickAndWait(confirmBtn);
    }
  }

  /**
   * 导入表单配置
   */
  async importForm(): Promise<void> {
    await this.waitAndClick(this.importBtn);
  }

  /**
   * 导出表单配置
   */
  async exportForm(): Promise<void> {
    await this.waitAndClick(this.exportBtn);
  }

  /**
   * 拖拽组件到画布
   */
  async dragComponentToCanvas(
    componentType: keyof typeof COMPONENT_TYPES
  ): Promise<void> {
    const sourceComponent =
      await this.componentLibrary.getComponentButton(componentType);
    const targetCanvas = this._canvasArea;

    await this.dragAndDrop(sourceComponent, targetCanvas);
    await this.wait(1000); // 等待组件添加完成
  }

  /**
   * 获取画布中的所有组件
   */
  async getCanvasComponents(): Promise<Locator[]> {
    const components = this.page.locator(SELECTORS.CANVAS_COMPONENT);
    const count = await components.count();
    const result: Locator[] = [];

    for (let i = 0; i < count; i++) {
      result.push(components.nth(i));
    }

    return result;
  }

  /**
   * 选择画布中的组件
   */
  async selectCanvasComponent(index: number): Promise<void> {
    const components = await this.getCanvasComponents();
    if (components[index]) {
      // 使用force避免被阻挡元素干扰
      await components[index].click({ force: true });
      await this.wait(500);
    }
  }

  /**
   * 删除画布中的组件
   */
  async deleteCanvasComponent(index: number): Promise<void> {
    await this.selectCanvasComponent(index);
    await this.pressKey("Delete");
    await this.wait(500);
  }

  /**
   * 获取画布中组件的数量
   */
  async getCanvasComponentCount(): Promise<number> {
    const components = this.page.locator(SELECTORS.CANVAS_COMPONENT);
    return await components.count();
  }

  /**
   * 检查是否有未保存的更改
   */
  async hasUnsavedChanges(): Promise<boolean> {
    const draftBadge = this.page.locator(SELECTORS.DRAFT_BADGE);
    return await this.isElementVisible(draftBadge);
  }

  /**
   * 等待保存完成
   */
  async waitForSaveComplete(): Promise<void> {
    // 等待草稿标识消失
    const draftBadge = this.page.locator(SELECTORS.DRAFT_BADGE);
    await this.waitForHidden(draftBadge, 10000);
  }

  /**
   * 获取表单预览内容
   */
  async getFormPreviewContent(): Promise<string> {
    await this.waitForVisible(this.formPreview);
    return await this.getText(this.formPreview);
  }

  /**
   * 检查工具栏是否可见
   */
  async isToolbarVisible(): Promise<boolean> {
    return await this.isElementVisible(this.toolbar);
  }

  /**
   * 获取当前表单配置的JSON
   */
  async getFormConfiguration(): Promise<object> {
    // 这里可以通过页面的JavaScript获取当前配置
    return await this.page.evaluate(() => {
      // 假设页面有全局的配置对象
      return (window as any).formConfiguration || {};
    });
  }

  /**
   * 设置表单配置
   */
  async setFormConfiguration(config: object): Promise<void> {
    await this.page.evaluate((config) => {
      (window as any).formConfiguration = config;
    }, config);
  }
}
