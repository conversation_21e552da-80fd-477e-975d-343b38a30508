/**
 * Page Object 统一导出文件
 * 提供所有页面对象的统一入口
 */

// 基础页面对象
export { BasePage } from './BasePage';

// 主要页面对象
export { FormEditorPage } from './FormEditorPage';
export { ComponentLibraryPage } from './ComponentLibraryPage';
export { PropertyPanelPage } from './PropertyPanelPage';

// 选择器和常量
export { SELECTORS, COMPONENT_TYPES, COMPONENT_GROUPS } from '../utils/selectors';

// 类型定义
export interface PageObjectOptions {
  timeout?: number;
  waitForLoad?: boolean;
  screenshot?: boolean;
}

export interface ComponentInfo {
  type: string;
  name: string;
  group: string;
  selector: string;
}

export interface FormConfiguration {
  title?: string;
  components: ComponentInfo[];
  layout: {
    columns: number;
    responsive: boolean;
  };
  validation: {
    required: string[];
    rules: Record<string, any>;
  };
}

// 工具函数
export class PageObjectFactory {
  /**
   * 创建FormEditorPage实例
   */
  static createFormEditorPage(page: any, options?: PageObjectOptions): FormEditorPage {
    return new FormEditorPage(page);
  }

  /**
   * 创建ComponentLibraryPage实例
   */
  static createComponentLibraryPage(page: any, options?: PageObjectOptions): ComponentLibraryPage {
    return new ComponentLibraryPage(page);
  }

  /**
   * 创建PropertyPanelPage实例
   */
  static createPropertyPanelPage(page: any, options?: PageObjectOptions): PropertyPanelPage {
    return new PropertyPanelPage(page);
  }
}

// 常用的页面对象组合
export class FormEditorTestSuite {
  public readonly formEditor: FormEditorPage;
  public readonly componentLibrary: ComponentLibraryPage;
  public readonly propertyPanel: PropertyPanelPage;

  constructor(page: any) {
    this.formEditor = new FormEditorPage(page);
    this.componentLibrary = this.formEditor.componentLibrary;
    this.propertyPanel = this.formEditor.propertyPanel;
  }

  /**
   * 初始化整个测试套件
   */
  async initialize(url?: string): Promise<void> {
    await this.formEditor.goto(url);
    await this.formEditor.waitForEditorLoad();
  }

  /**
   * 获取所有页面对象的状态
   */
  async getAllStates(): Promise<{
    formEditor: { isLoaded: boolean; hasUnsavedChanges: boolean };
    componentLibrary: any;
    propertyPanel: any;
  }> {
    return {
      formEditor: {
        isLoaded: await this.formEditor.isLoaded(),
        hasUnsavedChanges: await this.formEditor.hasUnsavedChanges(),
      },
      componentLibrary: await this.componentLibrary.getLibraryState(),
      propertyPanel: await this.propertyPanel.getPanelState(),
    };
  }

  /**
   * 执行完整的表单创建流程
   */
  async createCompleteForm(config: FormConfiguration): Promise<void> {
    // 重置表单
    await this.formEditor.resetForm();

    // 设置列数
    const currentColumns = 1; // 假设默认1列
    const targetColumns = config.layout.columns;
    
    if (targetColumns > currentColumns) {
      for (let i = currentColumns; i < targetColumns; i++) {
        await this.componentLibrary.addColumn();
      }
    }

    // 添加组件
    for (const component of config.components) {
      await this.formEditor.dragComponentToCanvas(component.type as any);
      
      // 如果有属性配置，设置属性
      if (component.name) {
        await this.propertyPanel.setProperty('name', component.name);
      }
    }

    // 保存表单
    await this.formEditor.saveForm();
    await this.formEditor.waitForSaveComplete();
  }

  /**
   * 验证表单配置
   */
  async validateFormConfiguration(expectedConfig: Partial<FormConfiguration>): Promise<boolean> {
    // 验证组件数量
    if (expectedConfig.components) {
      const actualCount = await this.formEditor.getCanvasComponentCount();
      if (actualCount !== expectedConfig.components.length) {
        return false;
      }
    }

    // 验证保存状态
    const hasUnsavedChanges = await this.formEditor.hasUnsavedChanges();
    if (hasUnsavedChanges) {
      return false;
    }

    return true;
  }

  /**
   * 清理测试环境
   */
  async cleanup(): Promise<void> {
    await this.formEditor.resetForm();
  }
}
