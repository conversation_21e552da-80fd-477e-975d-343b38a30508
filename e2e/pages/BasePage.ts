import { Page, Locator, expect } from '@playwright/test';

/**
 * 基础页面对象类
 * 提供所有页面对象的通用功能
 */
export abstract class BasePage {
  protected readonly page: Page;

  constructor(page: Page) {
    this.page = page;
  }

  /**
   * 等待页面加载完成
   */
  async waitForLoad(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * 等待元素可见
   */
  async waitForVisible(locator: Locator, timeout = 10000): Promise<void> {
    await expect(locator).toBeVisible({ timeout });
  }

  /**
   * 等待元素隐藏
   */
  async waitForHidden(locator: Locator, timeout = 10000): Promise<void> {
    await expect(locator).toBeHidden({ timeout });
  }

  /**
   * 点击元素并等待
   */
  async clickAndWait(locator: Locator, waitTime = 1000): Promise<void> {
    await locator.click();
    await this.page.waitForTimeout(waitTime);
  }

  /**
   * 输入文本并等待
   */
  async fillAndWait(locator: Locator, text: string, waitTime = 500): Promise<void> {
    await locator.fill(text);
    await this.page.waitForTimeout(waitTime);
  }

  /**
   * 拖拽元素
   */
  async dragAndDrop(source: Locator, target: Locator): Promise<void> {
    await source.dragTo(target);
    await this.page.waitForTimeout(1000); // 等待拖拽动画完成
  }

  /**
   * 获取元素文本
   */
  async getText(locator: Locator): Promise<string> {
    return await locator.textContent() || '';
  }

  /**
   * 检查元素是否存在
   */
  async isElementVisible(locator: Locator): Promise<boolean> {
    try {
      await expect(locator).toBeVisible({ timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 等待并点击
   */
  async waitAndClick(locator: Locator, timeout = 10000): Promise<void> {
    await this.waitForVisible(locator, timeout);
    await locator.click();
  }

  /**
   * 滚动到元素
   */
  async scrollToElement(locator: Locator): Promise<void> {
    await locator.scrollIntoViewIfNeeded();
  }

  /**
   * 获取元素属性
   */
  async getAttribute(locator: Locator, attribute: string): Promise<string | null> {
    return await locator.getAttribute(attribute);
  }

  /**
   * 等待元素包含文本
   */
  async waitForText(locator: Locator, text: string, timeout = 10000): Promise<void> {
    await expect(locator).toContainText(text, { timeout });
  }

  /**
   * 截图
   */
  async screenshot(name: string): Promise<void> {
    await this.page.screenshot({ 
      path: `e2e/reports/screenshots/${name}-${Date.now()}.png`,
      fullPage: true 
    });
  }

  /**
   * 等待网络空闲
   */
  async waitForNetworkIdle(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
  }

  /**
   * 模拟键盘按键
   */
  async pressKey(key: string): Promise<void> {
    await this.page.keyboard.press(key);
  }

  /**
   * 等待指定时间
   */
  async wait(milliseconds: number): Promise<void> {
    await this.page.waitForTimeout(milliseconds);
  }
}
