import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';
import { SELECTORS } from '../utils/selectors';

/**
 * 属性面板页面对象
 * 负责右侧属性配置面板的所有操作
 */
export class PropertyPanelPage extends BasePage {
  // 属性面板主容器
  private readonly propertyPanel: Locator;
  private readonly propertyTitle: Locator;
  private readonly propertyForm: Locator;

  constructor(page: Page) {
    super(page);

    // 初始化主要定位器
    this.propertyPanel = page.locator(SELECTORS.PROPERTY_PANEL);
    this.propertyTitle = page.locator(SELECTORS.PROPERTY_TITLE);
    this.propertyForm = page.locator(SELECTORS.PROPERTY_FORM);
  }

  /**
   * 等待属性面板加载完成
   */
  async waitForLoad(): Promise<void> {
    await this.waitForVisible(this.propertyPanel);
  }

  /**
   * 检查属性面板是否可见
   */
  async isVisible(): Promise<boolean> {
    return await this.isElementVisible(this.propertyPanel);
  }

  /**
   * 检查属性面板标题是否显示
   */
  async isTitleVisible(): Promise<boolean> {
    return await this.isElementVisible(this.propertyTitle);
  }

  /**
   * 获取属性面板标题文本
   */
  async getTitle(): Promise<string> {
    return await this.getText(this.propertyTitle);
  }

  /**
   * 设置组件属性
   */
  async setProperty(propertyName: string, value: string): Promise<void> {
    const propertyInput = this.page.locator(`[data-testid="property-input-${propertyName}"]`);
    await this.waitForVisible(propertyInput);
    await this.fillAndWait(propertyInput, value);
  }

  /**
   * 获取组件属性值
   */
  async getProperty(propertyName: string): Promise<string> {
    const propertyInput = this.page.locator(`[data-testid="property-input-${propertyName}"]`);
    await this.waitForVisible(propertyInput);
    return await propertyInput.inputValue();
  }

  /**
   * 检查属性输入框是否存在
   */
  async hasProperty(propertyName: string): Promise<boolean> {
    const propertyInput = this.page.locator(`[data-testid="property-input-${propertyName}"]`);
    return await this.isElementVisible(propertyInput);
  }

  /**
   * 获取所有可见的属性输入框
   */
  async getVisibleProperties(): Promise<string[]> {
    const propertyInputs = this.page.locator(SELECTORS.PROPERTY_INPUT);
    const count = await propertyInputs.count();
    const properties: string[] = [];

    for (let i = 0; i < count; i++) {
      const input = propertyInputs.nth(i);
      const testId = await input.getAttribute('data-testid');
      if (testId && testId.startsWith('property-input-')) {
        const propertyName = testId.replace('property-input-', '');
        properties.push(propertyName);
      }
    }

    return properties;
  }

  /**
   * 设置文本属性
   */
  async setTextProperty(propertyName: string, value: string): Promise<void> {
    await this.setProperty(propertyName, value);
  }

  /**
   * 设置数字属性
   */
  async setNumberProperty(propertyName: string, value: number): Promise<void> {
    await this.setProperty(propertyName, value.toString());
  }

  /**
   * 设置布尔属性（复选框）
   */
  async setBooleanProperty(propertyName: string, checked: boolean): Promise<void> {
    const checkbox = this.page.locator(`[data-testid="property-checkbox-${propertyName}"]`);
    await this.waitForVisible(checkbox);
    
    const isChecked = await checkbox.isChecked();
    if (isChecked !== checked) {
      await checkbox.click();
    }
  }

  /**
   * 获取布尔属性值
   */
  async getBooleanProperty(propertyName: string): Promise<boolean> {
    const checkbox = this.page.locator(`[data-testid="property-checkbox-${propertyName}"]`);
    await this.waitForVisible(checkbox);
    return await checkbox.isChecked();
  }

  /**
   * 设置选择属性（下拉框）
   */
  async setSelectProperty(propertyName: string, value: string): Promise<void> {
    const select = this.page.locator(`[data-testid="property-select-${propertyName}"]`);
    await this.waitForVisible(select);
    await select.selectOption(value);
  }

  /**
   * 获取选择属性值
   */
  async getSelectProperty(propertyName: string): Promise<string> {
    const select = this.page.locator(`[data-testid="property-select-${propertyName}"]`);
    await this.waitForVisible(select);
    return await select.inputValue();
  }

  /**
   * 清空属性值
   */
  async clearProperty(propertyName: string): Promise<void> {
    const propertyInput = this.page.locator(`[data-testid="property-input-${propertyName}"]`);
    await this.waitForVisible(propertyInput);
    await propertyInput.clear();
  }

  /**
   * 检查属性是否为必填
   */
  async isPropertyRequired(propertyName: string): Promise<boolean> {
    const propertyInput = this.page.locator(`[data-testid="property-input-${propertyName}"]`);
    const required = await propertyInput.getAttribute('required');
    return required !== null;
  }

  /**
   * 获取属性的占位符文本
   */
  async getPropertyPlaceholder(propertyName: string): Promise<string | null> {
    const propertyInput = this.page.locator(`[data-testid="property-input-${propertyName}"]`);
    return await propertyInput.getAttribute('placeholder');
  }

  /**
   * 检查属性输入框是否禁用
   */
  async isPropertyDisabled(propertyName: string): Promise<boolean> {
    const propertyInput = this.page.locator(`[data-testid="property-input-${propertyName}"]`);
    const disabled = await propertyInput.getAttribute('disabled');
    return disabled !== null;
  }

  /**
   * 获取属性标签文本
   */
  async getPropertyLabel(propertyName: string): Promise<string> {
    const label = this.page.locator(`label[for*="${propertyName}"]`);
    return await this.getText(label);
  }

  /**
   * 等待属性面板更新
   */
  async waitForUpdate(): Promise<void> {
    await this.wait(500); // 等待属性面板更新
  }

  /**
   * 获取属性面板的完整状态
   */
  async getPanelState(): Promise<{
    isVisible: boolean;
    title: string;
    visibleProperties: string[];
  }> {
    return {
      isVisible: await this.isVisible(),
      title: await this.getTitle(),
      visibleProperties: await this.getVisibleProperties(),
    };
  }

  /**
   * 批量设置属性
   */
  async setProperties(properties: Record<string, any>): Promise<void> {
    for (const [name, value] of Object.entries(properties)) {
      if (typeof value === 'string') {
        await this.setTextProperty(name, value);
      } else if (typeof value === 'number') {
        await this.setNumberProperty(name, value);
      } else if (typeof value === 'boolean') {
        await this.setBooleanProperty(name, value);
      }
      await this.wait(200); // 短暂等待避免操作过快
    }
  }

  /**
   * 批量获取属性
   */
  async getProperties(propertyNames: string[]): Promise<Record<string, string>> {
    const properties: Record<string, string> = {};
    
    for (const name of propertyNames) {
      if (await this.hasProperty(name)) {
        properties[name] = await this.getProperty(name);
      }
    }
    
    return properties;
  }

  /**
   * 验证属性值
   */
  async validateProperty(propertyName: string, expectedValue: string): Promise<boolean> {
    const actualValue = await this.getProperty(propertyName);
    return actualValue === expectedValue;
  }

  /**
   * 重置所有属性到默认值
   */
  async resetProperties(): Promise<void> {
    const properties = await this.getVisibleProperties();
    for (const property of properties) {
      await this.clearProperty(property);
    }
  }
}
