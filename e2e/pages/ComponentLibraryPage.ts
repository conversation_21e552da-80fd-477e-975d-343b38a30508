import { Page, Locator } from '@playwright/test';
import { BasePage } from './BasePage';
import { SELECTORS, COMPONENT_TYPES, COMPONENT_GROUPS } from '../utils/selectors';

/**
 * 组件库页面对象
 * 负责左侧组件库的所有操作
 */
export class ComponentLibraryPage extends BasePage {
  // 组件库主容器
  private readonly componentLibrary: Locator;
  private readonly componentLibraryTitle: Locator;

  // 组件按钮映射
  private readonly componentButtons: Record<string, Locator>;

  // 布局控制按钮
  private readonly addColumnBtn: Locator;
  private readonly removeColumnBtn: Locator;

  constructor(page: Page) {
    super(page);

    // 初始化主要定位器
    this.componentLibrary = page.locator(SELECTORS.COMPONENT_LIBRARY);
    this.componentLibraryTitle = page.locator(SELECTORS.COMPONENT_LIBRARY_TITLE);

    // 初始化组件按钮映射
    this.componentButtons = {
      [COMPONENT_TYPES.INPUT]: page.locator(SELECTORS.COMPONENT_INPUT),
      [COMPONENT_TYPES.TEXTAREA]: page.locator(SELECTORS.COMPONENT_TEXTAREA),
      [COMPONENT_TYPES.SELECT]: page.locator(SELECTORS.COMPONENT_SELECT),
      [COMPONENT_TYPES.RADIO]: page.locator(SELECTORS.COMPONENT_RADIO),
      [COMPONENT_TYPES.CHECKBOX]: page.locator(SELECTORS.COMPONENT_CHECKBOX),
      [COMPONENT_TYPES.DATE]: page.locator(SELECTORS.COMPONENT_DATE),
      [COMPONENT_TYPES.EMPLOYEE]: page.locator(SELECTORS.COMPONENT_EMPLOYEE),
      [COMPONENT_TYPES.FILE]: page.locator(SELECTORS.COMPONENT_FILE),
      [COMPONENT_TYPES.IMAGE]: page.locator(SELECTORS.COMPONENT_IMAGE),
      [COMPONENT_TYPES.TABLE]: page.locator(SELECTORS.COMPONENT_TABLE),
    };

    // 布局控制按钮
    this.addColumnBtn = page.locator(SELECTORS.ADD_COLUMN);
    this.removeColumnBtn = page.locator(SELECTORS.REMOVE_COLUMN);
  }

  /**
   * 等待组件库加载完成
   */
  async waitForLoad(): Promise<void> {
    await this.waitForVisible(this.componentLibrary);
    await this.waitForVisible(this.componentLibraryTitle);
  }

  /**
   * 检查组件库是否可见
   */
  async isVisible(): Promise<boolean> {
    return await this.isElementVisible(this.componentLibrary);
  }

  /**
   * 获取指定类型的组件按钮
   */
  async getComponentButton(componentType: keyof typeof COMPONENT_TYPES): Promise<Locator> {
    const button = this.componentButtons[COMPONENT_TYPES[componentType]];
    if (!button) {
      throw new Error(`Component type ${componentType} not found`);
    }
    return button;
  }

  /**
   * 点击组件按钮添加到画布
   */
  async clickComponent(componentType: keyof typeof COMPONENT_TYPES): Promise<void> {
    const button = await this.getComponentButton(componentType);
    await this.waitAndClick(button);
  }

  /**
   * 检查组件按钮是否可见
   */
  async isComponentVisible(componentType: keyof typeof COMPONENT_TYPES): Promise<boolean> {
    const button = await this.getComponentButton(componentType);
    return await this.isElementVisible(button);
  }

  /**
   * 获取所有可见的组件按钮
   */
  async getVisibleComponents(): Promise<string[]> {
    const visibleComponents: string[] = [];
    
    for (const [type, locator] of Object.entries(this.componentButtons)) {
      if (await this.isElementVisible(locator)) {
        visibleComponents.push(type);
      }
    }
    
    return visibleComponents;
  }

  /**
   * 添加一列
   */
  async addColumn(): Promise<void> {
    await this.waitAndClick(this.addColumnBtn);
    await this.wait(500); // 等待布局更新
  }

  /**
   * 删除一列
   */
  async removeColumn(): Promise<void> {
    await this.waitAndClick(this.removeColumnBtn);
    await this.wait(500); // 等待布局更新
  }

  /**
   * 检查添加列按钮是否可用
   */
  async canAddColumn(): Promise<boolean> {
    return await this.isElementVisible(this.addColumnBtn);
  }

  /**
   * 检查删除列按钮是否可用
   */
  async canRemoveColumn(): Promise<boolean> {
    return await this.isElementVisible(this.removeColumnBtn);
  }

  /**
   * 获取组件库标题文本
   */
  async getTitle(): Promise<string> {
    return await this.getText(this.componentLibraryTitle);
  }

  /**
   * 获取组件按钮的文本
   */
  async getComponentButtonText(componentType: keyof typeof COMPONENT_TYPES): Promise<string> {
    const button = await this.getComponentButton(componentType);
    return await this.getText(button);
  }

  /**
   * 拖拽组件到指定位置
   */
  async dragComponentTo(componentType: keyof typeof COMPONENT_TYPES, target: Locator): Promise<void> {
    const sourceButton = await this.getComponentButton(componentType);
    await this.dragAndDrop(sourceButton, target);
  }

  /**
   * 检查组件按钮是否启用
   */
  async isComponentEnabled(componentType: keyof typeof COMPONENT_TYPES): Promise<boolean> {
    const button = await this.getComponentButton(componentType);
    const disabled = await button.getAttribute('disabled');
    return disabled === null;
  }

  /**
   * 获取组件按钮的样式类
   */
  async getComponentButtonClass(componentType: keyof typeof COMPONENT_TYPES): Promise<string | null> {
    const button = await this.getComponentButton(componentType);
    return await button.getAttribute('class');
  }

  /**
   * 等待组件按钮可点击
   */
  async waitForComponentClickable(componentType: keyof typeof COMPONENT_TYPES): Promise<void> {
    const button = await this.getComponentButton(componentType);
    await this.waitForVisible(button);
    await button.waitFor({ state: 'attached' });
  }

  /**
   * 获取组件库中的所有组件项
   */
  async getAllComponentItems(): Promise<Locator[]> {
    const items = this.page.locator(SELECTORS.COMPONENT_ITEM);
    const count = await items.count();
    const result: Locator[] = [];
    
    for (let i = 0; i < count; i++) {
      result.push(items.nth(i));
    }
    
    return result;
  }

  /**
   * 根据文本查找组件按钮
   */
  async findComponentByText(text: string): Promise<Locator | null> {
    const button = this.page.locator(`button:has-text("${text}")`);
    if (await this.isElementVisible(button)) {
      return button;
    }
    return null;
  }

  /**
   * 检查组件库是否包含指定组件
   */
  async hasComponent(componentType: keyof typeof COMPONENT_TYPES): Promise<boolean> {
    try {
      const button = await this.getComponentButton(componentType);
      return await this.isElementVisible(button);
    } catch {
      return false;
    }
  }

  /**
   * 获取组件库的完整状态
   */
  async getLibraryState(): Promise<{
    isVisible: boolean;
    title: string;
    visibleComponents: string[];
    canAddColumn: boolean;
    canRemoveColumn: boolean;
  }> {
    return {
      isVisible: await this.isVisible(),
      title: await this.getTitle(),
      visibleComponents: await this.getVisibleComponents(),
      canAddColumn: await this.canAddColumn(),
      canRemoveColumn: await this.canRemoveColumn(),
    };
  }
}
