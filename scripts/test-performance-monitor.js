#!/usr/bin/env node

/**
 * 测试性能监控脚本
 * 用于监控测试执行时间，识别性能瓶颈，生成性能报告
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// 性能基准配置
const PERFORMANCE_BENCHMARKS = {
  maxTotalTime: 10000,     // 10秒
  maxTestTime: 100,        // 100ms per test
  maxFileTime: 500,        // 500ms per file
  minSuccessRate: 0.98,    // 98%成功率
  maxMemoryUsage: 512,     // 512MB
};

// 性能监控类
class TestPerformanceMonitor {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      totalTime: 0,
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      fileResults: [],
      slowTests: [],
      memoryUsage: 0,
      warnings: [],
      recommendations: [],
    };
  }

  // 运行测试并收集性能数据
  async runTests(testPattern = '') {
    console.log('🚀 开始测试性能监控...');
    
    const startTime = Date.now();
    
    try {
      // 运行测试并捕获输出
      const command = testPattern 
        ? `yarn test ${testPattern} --run --reporter=verbose`
        : 'yarn test --run --reporter=verbose';
      
      console.log(`执行命令: ${command}`);
      
      const output = execSync(command, { 
        encoding: 'utf8',
        maxBuffer: 1024 * 1024 * 10, // 10MB buffer
      });
      
      this.results.totalTime = Date.now() - startTime;
      this.parseTestOutput(output);
      
    } catch (error) {
      this.results.totalTime = Date.now() - startTime;
      this.parseTestOutput(error.stdout || error.message);
      this.results.warnings.push('测试执行过程中出现错误');
    }
    
    this.analyzePerformance();
    this.generateRecommendations();
    
    return this.results;
  }

  // 解析测试输出
  parseTestOutput(output) {
    const lines = output.split('\n');
    
    // 解析总体统计
    const summaryMatch = output.match(/Test Files\s+(\d+)\s+passed.*Tests\s+(\d+)\s+passed/);
    if (summaryMatch) {
      this.results.passedTests = parseInt(summaryMatch[2]);
    }
    
    const failedMatch = output.match(/(\d+)\s+failed/);
    if (failedMatch) {
      this.results.failedTests = parseInt(failedMatch[1]);
    }
    
    this.results.totalTests = this.results.passedTests + this.results.failedTests;
    
    // 解析单个文件性能
    const filePattern = /✓\s+(.+\.tsx?)\s+\((\d+)\s+tests?\)\s+(\d+)ms/g;
    let match;
    
    while ((match = filePattern.exec(output)) !== null) {
      const [, fileName, testCount, duration] = match;
      const fileResult = {
        fileName: fileName.trim(),
        testCount: parseInt(testCount),
        duration: parseInt(duration),
        avgTimePerTest: parseInt(duration) / parseInt(testCount),
      };
      
      this.results.fileResults.push(fileResult);
      
      // 识别慢测试
      if (fileResult.duration > PERFORMANCE_BENCHMARKS.maxFileTime) {
        this.results.slowTests.push({
          ...fileResult,
          reason: 'File execution time exceeds benchmark',
        });
      }
      
      if (fileResult.avgTimePerTest > PERFORMANCE_BENCHMARKS.maxTestTime) {
        this.results.slowTests.push({
          ...fileResult,
          reason: 'Average test time exceeds benchmark',
        });
      }
    }
    
    // 解析内存使用（如果可用）
    const memoryMatch = output.match(/Memory usage:\s+(\d+)MB/);
    if (memoryMatch) {
      this.results.memoryUsage = parseInt(memoryMatch[1]);
    }
  }

  // 分析性能
  analyzePerformance() {
    const successRate = this.results.totalTests > 0 
      ? this.results.passedTests / this.results.totalTests 
      : 0;
    
    // 检查总执行时间
    if (this.results.totalTime > PERFORMANCE_BENCHMARKS.maxTotalTime) {
      this.results.warnings.push(
        `总执行时间 ${this.results.totalTime}ms 超过基准 ${PERFORMANCE_BENCHMARKS.maxTotalTime}ms`
      );
    }
    
    // 检查成功率
    if (successRate < PERFORMANCE_BENCHMARKS.minSuccessRate) {
      this.results.warnings.push(
        `测试成功率 ${(successRate * 100).toFixed(1)}% 低于基准 ${PERFORMANCE_BENCHMARKS.minSuccessRate * 100}%`
      );
    }
    
    // 检查内存使用
    if (this.results.memoryUsage > PERFORMANCE_BENCHMARKS.maxMemoryUsage) {
      this.results.warnings.push(
        `内存使用 ${this.results.memoryUsage}MB 超过基准 ${PERFORMANCE_BENCHMARKS.maxMemoryUsage}MB`
      );
    }
    
    // 计算平均测试时间
    const avgTestTime = this.results.totalTests > 0 
      ? this.results.totalTime / this.results.totalTests 
      : 0;
    
    if (avgTestTime > PERFORMANCE_BENCHMARKS.maxTestTime) {
      this.results.warnings.push(
        `平均测试时间 ${avgTestTime.toFixed(1)}ms 超过基准 ${PERFORMANCE_BENCHMARKS.maxTestTime}ms`
      );
    }
  }

  // 生成优化建议
  generateRecommendations() {
    if (this.results.slowTests.length > 0) {
      this.results.recommendations.push(
        `发现 ${this.results.slowTests.length} 个慢测试文件，建议优化Mock配置或拆分测试`
      );
    }
    
    if (this.results.totalTime > PERFORMANCE_BENCHMARKS.maxTotalTime) {
      this.results.recommendations.push(
        '总执行时间过长，建议启用并行执行或优化测试数据'
      );
    }
    
    if (this.results.failedTests > 0) {
      this.results.recommendations.push(
        '存在失败测试，建议修复Mock配置或依赖问题'
      );
    }
    
    if (this.results.memoryUsage > PERFORMANCE_BENCHMARKS.maxMemoryUsage) {
      this.results.recommendations.push(
        '内存使用过高，建议优化测试数据或增加内存清理'
      );
    }
  }

  // 生成性能报告
  generateReport() {
    const report = {
      summary: {
        timestamp: this.results.timestamp,
        totalTime: `${this.results.totalTime}ms`,
        totalTests: this.results.totalTests,
        successRate: `${((this.results.passedTests / this.results.totalTests) * 100).toFixed(1)}%`,
        avgTimePerTest: `${(this.results.totalTime / this.results.totalTests).toFixed(1)}ms`,
      },
      performance: {
        slowestFiles: this.results.fileResults
          .sort((a, b) => b.duration - a.duration)
          .slice(0, 5),
        fastestFiles: this.results.fileResults
          .sort((a, b) => a.duration - b.duration)
          .slice(0, 5),
      },
      issues: {
        warnings: this.results.warnings,
        recommendations: this.results.recommendations,
        slowTests: this.results.slowTests,
      },
      benchmarks: PERFORMANCE_BENCHMARKS,
    };
    
    return report;
  }

  // 保存报告到文件
  saveReport(outputPath = 'docs/dev-log') {
    const report = this.generateReport();
    const timestamp = new Date().toISOString().split('T')[0];
    const fileName = `${timestamp}-test-performance-report.json`;
    const filePath = path.join(outputPath, fileName);
    
    // 确保目录存在
    if (!fs.existsSync(outputPath)) {
      fs.mkdirSync(outputPath, { recursive: true });
    }
    
    fs.writeFileSync(filePath, JSON.stringify(report, null, 2));
    console.log(`📊 性能报告已保存到: ${filePath}`);
    
    return filePath;
  }

  // 打印控制台报告
  printReport() {
    const report = this.generateReport();
    
    console.log('\n📊 测试性能报告');
    console.log('='.repeat(50));
    
    console.log('\n📈 总体统计:');
    console.log(`  总执行时间: ${report.summary.totalTime}`);
    console.log(`  测试用例数: ${report.summary.totalTests}`);
    console.log(`  成功率: ${report.summary.successRate}`);
    console.log(`  平均时间/用例: ${report.summary.avgTimePerTest}`);
    
    if (report.performance.slowestFiles.length > 0) {
      console.log('\n🐌 最慢的测试文件:');
      report.performance.slowestFiles.forEach((file, index) => {
        console.log(`  ${index + 1}. ${file.fileName} - ${file.duration}ms (${file.testCount} tests)`);
      });
    }
    
    if (report.issues.warnings.length > 0) {
      console.log('\n⚠️  性能警告:');
      report.issues.warnings.forEach(warning => {
        console.log(`  • ${warning}`);
      });
    }
    
    if (report.issues.recommendations.length > 0) {
      console.log('\n💡 优化建议:');
      report.issues.recommendations.forEach(rec => {
        console.log(`  • ${rec}`);
      });
    }
    
    console.log('\n✅ 监控完成');
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const testPattern = args[0] || '';
  
  const monitor = new TestPerformanceMonitor();
  
  try {
    await monitor.runTests(testPattern);
    monitor.printReport();
    monitor.saveReport();
  } catch (error) {
    console.error('❌ 性能监控失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = TestPerformanceMonitor;
