#!/bin/bash

# E2E测试统一管理脚本
# 集成环境设置、环境切换、测试运行等功能

set -e

# 显示帮助信息
show_help() {
    echo "🎭 E2E测试管理工具"
    echo ""
    echo "用法: $0 <命令> [参数]"
    echo ""
    echo "命令:"
    echo "  setup           初始化E2E测试环境"
    echo "  env <环境名>    切换测试环境"
    echo "  current         显示当前环境"
    echo "  list            列出所有可用环境"
    echo "  test [选项]     运行E2E测试"
    echo "  verify          验证最近的测试结果"
    echo ""
    echo "环境名称:"
    echo "  test            测试环境 (推荐默认)"
    echo "  dev1-dev5       开发环境1-5"
    echo "  customer-*      客户环境"
    echo ""
    echo "测试选项:"
    echo "  --ui            使用UI模式"
    echo "  --debug         调试模式"
    echo "  --headed        有头模式"
    echo "  --project=<名>  指定浏览器项目"
    echo ""
    echo "示例:"
    echo "  $0 setup              # 初始化环境"
    echo "  $0 env test           # 切换到测试环境"
    echo "  $0 env dev3           # 切换到dev3环境"
    echo "  $0 current            # 显示当前环境"
    echo "  $0 test               # 运行所有测试"
    echo "  $0 test --ui          # UI模式运行测试"
    echo "  $0 test --project=chromium  # 只运行Chrome测试"
    echo "  $0 verify             # 验证测试结果"
}

# 初始化E2E环境
setup_environment() {
    echo "🚀 初始化E2E测试环境..."
    
    # 检查并安装依赖
    if [ ! -d "node_modules" ]; then
        echo "📦 安装项目依赖..."
        yarn install
    fi
    
    # 检查并安装Playwright
    if [ ! -d "node_modules/@playwright/test" ]; then
        echo "🎭 安装Playwright..."
        yarn add -D @playwright/test
    fi
    
    echo "🌐 安装Playwright浏览器..."
    npx playwright install
    
    # 设置默认环境
    if [ ! -L ".env.local" ]; then
        echo "⚙️  创建默认测试环境链接..."
        ln -sf .env.test .env.local
        echo "✅ 已创建 .env.local -> .env.test"
    else
        current_target=$(readlink .env.local)
        echo "ℹ️  .env.local 已存在，当前链接: $current_target"
    fi
    
    echo ""
    echo "🎉 E2E测试环境初始化完成！"
    echo ""
    echo "📋 快速开始:"
    echo "  $0 current            # 查看当前环境"
    echo "  $0 test               # 运行测试"
    echo "  $0 env dev3           # 切换环境"
}

# 显示当前环境
show_current() {
    if [ -L ".env.local" ]; then
        current_target=$(readlink .env.local)
        env_name=${current_target#.env.}
        echo "📍 当前环境: $env_name ($current_target)"
        
        if [ -f ".env.local" ]; then
            echo ""
            echo "🌐 环境配置:"
            grep "VITE_HOST=" .env.local 2>/dev/null | sed 's/^/  /' || echo "  未找到VITE_HOST配置"
            grep "VITE_OEM_ID=" .env.local 2>/dev/null | sed 's/^/  /' || echo "  未找到VITE_OEM_ID配置"
            grep "TEST_USERNAME=" .env.local 2>/dev/null | sed 's/^/  /' || echo "  未找到TEST_USERNAME配置"
        fi
    else
        echo "❌ .env.local 不是符号链接或不存在"
        echo "💡 运行 '$0 setup' 初始化环境"
    fi
}

# 列出所有环境
list_environments() {
    echo "📋 所有可用环境:"
    for env_file in .env.test .env.dev{1,2,3,4,5} .env.customer-* .env.{preview,staging}; do
        if [ -f "$env_file" ]; then
            env_name=${env_file#.env.}
            if [ -L ".env.local" ] && [ "$(readlink .env.local)" = "$env_file" ]; then
                echo "  $env_name (当前) ✓"
            else
                echo "  $env_name"
            fi
        fi
    done
}

# 切换环境
switch_environment() {
    local env_name="$1"
    
    if [ -z "$env_name" ]; then
        echo "❌ 请指定环境名称"
        echo ""
        list_environments
        return 1
    fi
    
    local env_file=".env.$env_name"
    
    if [ ! -f "$env_file" ]; then
        echo "❌ 环境文件 $env_file 不存在"
        echo ""
        list_environments
        return 1
    fi
    
    echo "🔄 切换到 $env_name 环境..."
    ln -sf "$env_file" .env.local
    
    if [ $? -eq 0 ]; then
        echo "✅ 已切换到 $env_name 环境"
        echo ""
        show_current
    else
        echo "❌ 切换失败"
        return 1
    fi
}

# 运行E2E测试
run_tests() {
    local test_args="$*"
    
    # 检查环境是否已设置
    if [ ! -L ".env.local" ]; then
        echo "❌ 环境未设置，请先运行: $0 setup"
        return 1
    fi
    
    echo "🧪 运行E2E测试..."
    show_current
    echo ""
    
    # 构建测试命令
    local cmd="yarn test:e2e"
    
    # 处理特殊参数
    for arg in $test_args; do
        case $arg in
            --ui)
                cmd="yarn test:e2e:ui"
                ;;
            --debug)
                cmd="yarn test:e2e:debug"
                ;;
            --headed)
                cmd="$cmd --headed"
                ;;
            --project=*)
                cmd="$cmd $arg"
                ;;
            *)
                cmd="$cmd $arg"
                ;;
        esac
    done
    
    echo "🚀 执行命令: $cmd"
    eval $cmd
}

# 主逻辑
case "${1:-}" in
    "setup")
        setup_environment
        ;;
    "env")
        switch_environment "$2"
        ;;
    "current")
        show_current
        ;;
    "list")
        list_environments
        ;;
    "test")
        shift
        run_tests "$@"
        ;;
    "verify")
        ./scripts/verify-e2e-results.sh
        ;;
    ""|"-h"|"--help"|"help")
        show_help
        ;;
    *)
        echo "❌ 未知命令: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
