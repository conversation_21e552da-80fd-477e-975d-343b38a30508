#!/bin/bash

# E2E测试结果验证脚本
# 用于验证测试是否全部通过

set -e

RESULTS_FILE="e2e/reports/test-results.json"

echo "🔍 验证E2E测试结果..."

# 检查测试结果文件是否存在
if [ ! -f "$RESULTS_FILE" ]; then
    echo "❌ 错误: 未找到测试结果文件 $RESULTS_FILE"
    echo "请先运行E2E测试: ./scripts/e2e.sh test"
    exit 1
fi

# 检查是否安装了jq
if ! command -v jq &> /dev/null; then
    echo "⚠️  警告: 未安装jq，使用简单的文本解析"
    
    # 简单的文本解析方式
    if grep -q '"unexpected": 0' "$RESULTS_FILE" && grep -q '"flaky": 0' "$RESULTS_FILE"; then
        EXPECTED=$(grep -o '"expected": [0-9]*' "$RESULTS_FILE" | grep -o '[0-9]*')
        echo "📊 测试结果统计:"
        echo "✅ 通过测试: $EXPECTED"
        echo "❌ 失败测试: 0"
        echo "⚠️ 不稳定测试: 0"
        
        if [ "$EXPECTED" -gt 0 ]; then
            echo "🎉 所有E2E测试通过! ($EXPECTED个测试)"
            exit 0
        else
            echo "❌ E2E测试失败: 没有通过的测试"
            exit 1
        fi
    else
        echo "❌ E2E测试失败: 存在失败或不稳定的测试"
        exit 1
    fi
else
    # 使用jq解析JSON
    EXPECTED=$(jq -r '.stats.expected // 0' "$RESULTS_FILE")
    UNEXPECTED=$(jq -r '.stats.unexpected // 0' "$RESULTS_FILE")
    FLAKY=$(jq -r '.stats.flaky // 0' "$RESULTS_FILE")
    SKIPPED=$(jq -r '.stats.skipped // 0' "$RESULTS_FILE")
    
    echo "📊 测试结果统计:"
    echo "✅ 通过测试: $EXPECTED"
    echo "❌ 失败测试: $UNEXPECTED"
    echo "⚠️ 不稳定测试: $FLAKY"
    echo "⏭️ 跳过测试: $SKIPPED"
    
    # 验证测试结果
    if [ "$UNEXPECTED" -gt 0 ]; then
        echo "❌ E2E测试失败: 存在 $UNEXPECTED 个失败的测试"
        
        # 显示失败的测试详情
        echo ""
        echo "🔍 失败测试详情:"
        jq -r '.suites[].specs[] | select(.tests[].results[].status == "failed") | "- " + .title + " (" + .file + ":" + (.line | tostring) + ")"' "$RESULTS_FILE" 2>/dev/null || echo "无法解析失败测试详情"
        exit 1
    fi
    
    if [ "$FLAKY" -gt 0 ]; then
        echo "⚠️ E2E测试警告: 存在 $FLAKY 个不稳定的测试"
        
        # 显示不稳定的测试详情
        echo ""
        echo "🔍 不稳定测试详情:"
        jq -r '.suites[].specs[] | select(.tests[].results[].status == "flaky") | "- " + .title + " (" + .file + ":" + (.line | tostring) + ")"' "$RESULTS_FILE" 2>/dev/null || echo "无法解析不稳定测试详情"
        
        # 根据环境决定是否失败
        if [ "$CI" = "true" ]; then
            echo "❌ CI环境中不允许不稳定测试"
            exit 1
        else
            echo "⚠️ 本地环境中允许不稳定测试，但建议修复"
        fi
    fi
    
    if [ "$EXPECTED" -eq 0 ]; then
        echo "❌ E2E测试失败: 没有通过的测试"
        exit 1
    fi
    
    echo "🎉 所有E2E测试通过! ($EXPECTED个测试)"
    
    # 显示测试执行时间
    DURATION=$(jq -r '.stats.duration // 0' "$RESULTS_FILE")
    DURATION_SEC=$(echo "scale=1; $DURATION / 1000" | bc 2>/dev/null || echo "N/A")
    echo "⏱️ 测试执行时间: ${DURATION_SEC}秒"
fi

echo ""
echo "📋 下一步:"
echo "  查看详细报告: yarn playwright show-report e2e/reports/html-report"
echo "  运行更多测试: ./scripts/e2e.sh test"
