# 作业票表单模板组件库测试标准

## 一、测试标准概述

### 1.1 目标

本标准旨在为作业票动态表单系统的组件库层建立统一、可复用、高质量的测试规范，确保组件的稳定性、可维护性和业务逻辑的正确性。

### 1.2 适用范围

- **目标组件**: `src/pages/ticket/components/formItem/lib/` 目录下的所有组件
- **核心组件**: formTable.tsx, cellActionPanel.tsx, disposeForm.tsx, eventCover/index.tsx
- **测试框架**: Vitest + React Testing Library
- **Mock策略**: Semi UI + Jotai + Context 统一Mock

### 1.3 质量目标

- **行覆盖率**: 60%+ (从简化测试的0%提升)
- **分支覆盖率**: 50%+
- **函数覆盖率**: 80%+
- **测试通过率**: 100%

## 二、测试分级标准

### 2.1 Level 1: 基础结构测试

**目标**: 验证组件的基本导入和结构完整性

**测试内容**:
- 组件/函数成功导入
- 导出的常量和工具函数验证
- 基本数据结构验证

**示例**:
```typescript
describe("Basic Structure", () => {
  it("should import component successfully", () => {
    expect(ComponentName).toBeDefined();
    expect(typeof ComponentName).toBe("function");
  });

  it("should have correct default data structure", () => {
    expect(defaultData).toEqual(expectedStructure);
  });
});
```

### 2.2 Level 2: 属性验证测试

**目标**: 验证组件属性处理和数据结构

**测试内容**:
- 必需属性验证
- 可选属性处理
- 默认值设置
- 属性类型检查

**示例**:
```typescript
describe("Props Validation", () => {
  it("should handle required props", () => {
    const requiredProps = { prop1: "value1", prop2: "value2" };
    expect(() => processProps(requiredProps)).not.toThrow();
  });

  it("should apply default values for optional props", () => {
    const result = processProps({});
    expect(result.optionalProp).toBe(defaultValue);
  });
});
```

### 2.3 Level 3: 交互逻辑测试

**目标**: 验证用户交互和事件处理

**测试内容**:
- 用户交互响应
- 事件处理逻辑
- 状态变化验证
- 回调函数调用

**示例**:
```typescript
describe("User Interactions", () => {
  it("should handle user interactions", () => {
    const mockCallback = vi.fn();
    renderComponent({ onAction: mockCallback });
    
    fireEvent.click(screen.getByRole("button"));
    expect(mockCallback).toHaveBeenCalled();
  });
});
```

### 2.4 Level 4: 业务逻辑测试

**目标**: 验证核心业务逻辑和数据处理

**测试内容**:
- 核心业务逻辑验证
- 数据转换和处理
- 边界情况处理
- 错误处理机制

**示例**:
```typescript
describe("Business Logic", () => {
  it("should process business data correctly", () => {
    const inputData = createMockData();
    const result = processBusinessLogic(inputData);
    expect(result).toMatchExpectedOutput();
  });

  it("should handle edge cases", () => {
    const edgeCaseData = createEdgeCaseData();
    expect(() => processBusinessLogic(edgeCaseData)).not.toThrow();
  });
});
```

## 三、Mock策略标准

### 3.1 Semi UI组件Mock

```typescript
const createSemiUIMocks = () => ({
  Button: ({ children, onClick, disabled, type, ...props }: any) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      data-type={type}
      {...props}
    >
      {children}
    </button>
  ),
  
  Select: ({ value, onChange, options, placeholder, ...props }: any) => (
    <select 
      value={value} 
      onChange={(e) => onChange?.(e.target.value)} 
      {...props}
    >
      <option value="">{placeholder}</option>
      {options?.map((opt: any) => (
        <option key={opt.value} value={opt.value}>{opt.label}</option>
      ))}
    </select>
  ),
  
  Form: ({ children, ...props }: any) => (
    <form {...props}>{children}</form>
  ),
  
  FormItem: ({ children, label, ...props }: any) => (
    <div data-testid="form-item" {...props}>
      {label && <label>{label}</label>}
      {children}
    </div>
  ),
});
```

### 3.2 Jotai状态管理Mock

```typescript
const createJotaiMocks = (initialState = {}) => ({
  useAtom: vi.fn(() => [initialState, vi.fn()]),
  useAtomValue: vi.fn(() => initialState),
  useSetAtom: vi.fn(() => vi.fn()),
  atom: vi.fn(() => ({})),
  Provider: ({ children }: any) => children,
});
```

### 3.3 Context Mock

```typescript
const createContextMocks = () => ({
  FormContext: {
    Provider: ({ children }: any) => children,
    Consumer: ({ children }: any) => children({
      containerState: {},
      setContainerState: vi.fn(),
    }),
  },
});
```

## 四、测试文件结构标准

### 4.1 文件命名规范

- **测试文件**: `componentName.test.tsx`
- **位置**: 与被测试组件同级的 `__tests__` 目录
- **Mock文件**: `testUtils.tsx` (共享Mock工具)

### 4.2 文件结构模板

```typescript
// 1. 导入声明
import { beforeEach, describe, expect, it, vi } from "vitest";
import { ComponentName } from "../componentName";

// 2. Mock声明
vi.mock("@douyinfe/semi-ui", () => createSemiUIMocks());
vi.mock("jotai", () => createJotaiMocks());

// 3. 测试数据工厂
const createMockData = (overrides = {}) => ({
  defaultProp: "defaultValue",
  ...overrides,
});

// 4. 主要测试套件
describe("ComponentName", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  // Level 1: 基础结构测试
  describe("Basic Structure", () => {
    // 测试用例
  });

  // Level 2: 属性验证测试
  describe("Props Validation", () => {
    // 测试用例
  });

  // Level 3: 交互逻辑测试
  describe("User Interactions", () => {
    // 测试用例
  });

  // Level 4: 业务逻辑测试
  describe("Business Logic", () => {
    // 测试用例
  });
});
```

## 五、实施计划

### 5.1 优先级排序

1. **formTable.tsx** ✅ 已完成 (12个测试用例)
2. **cellActionPanel.tsx** - 交互逻辑复杂，优先升级
3. **disposeForm.tsx** - 表单处理核心组件
4. **eventCover/index.tsx** - 事件处理组件

### 5.2 实施步骤

**阶段一**: 核心组件升级 (1-2周)
- 升级cellActionPanel.tsx测试
- 升级disposeForm.tsx测试
- 建立完整的Mock策略库

**阶段二**: 全面覆盖 (2-3周)
- 升级eventCover/index.tsx测试
- 覆盖其他辅助组件
- 达到整体60%+覆盖率目标

**阶段三**: 优化完善 (1周)
- 性能优化和稳定性测试
- 文档完善和团队培训
- 建立持续集成检查

### 5.3 成功标准

- **测试覆盖率**: 组件库层平均覆盖率达到60%+
- **测试质量**: 所有测试用例100%通过
- **可维护性**: 建立标准化的测试流程和文档
- **团队采用**: 团队成员能够按照标准独立编写测试

## 六、最佳实践

### 6.1 测试编写原则

1. **简化优先**: 避免复杂的组件渲染，专注核心逻辑
2. **分层测试**: 从简单到复杂的渐进式测试
3. **Mock最小化**: 只Mock必要的依赖，保持测试的真实性
4. **可读性**: 测试用例名称清晰，测试逻辑简洁

### 6.2 常见问题解决

**依赖复杂**: 使用简化Mock策略，专注数据结构和业务逻辑
**状态管理**: 使用Jotai Mock模板，避免复杂的状态初始化
**组件渲染**: 优先测试导出的函数和常量，减少组件渲染测试

### 6.3 质量保证

- **代码审查**: 所有测试代码必须经过代码审查
- **持续集成**: 集成到CI/CD流程，确保测试持续通过
- **定期维护**: 定期更新测试用例，保持与业务逻辑同步

---

**文档版本**: v1.0  
**创建日期**: 2025-07-27  
**最后更新**: 2025-07-27  
**维护者**: 动态表单测试团队
