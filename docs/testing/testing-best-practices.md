# 测试最佳实践指南

## 📋 目录

1. [测试策略](#测试策略)
2. [测试分层](#测试分层)
3. [Mock策略](#mock策略)
4. [性能优化](#性能优化)
5. [质量检查](#质量检查)
6. [维护流程](#维护流程)

## 🎯 测试策略

### 测试金字塔原则

```
    🔺 E2E Tests (5%)
   🔺🔺 Integration Tests (15%)
  🔺🔺🔺 Unit Tests (80%)
```

- **单元测试 (80%)**: 测试单个函数、组件的核心逻辑
- **集成测试 (15%)**: 测试模块间的交互和数据流
- **端到端测试 (5%)**: 测试完整的用户场景

### 覆盖率目标

| 层级 | 行覆盖率 | 分支覆盖率 | 函数覆盖率 |
|------|----------|------------|------------|
| 核心工具函数 | ≥90% | ≥95% | 100% |
| 业务组件 | ≥80% | ≥85% | ≥90% |
| 页面组件 | ≥70% | ≥75% | ≥80% |
| 全局平均 | ≥80% | ≥85% | ≥90% |

## 🏗️ 测试分层

### 1. 单元测试 (Unit Tests)

**适用场景**: 工具函数、纯组件、业务逻辑

```typescript
// ✅ 好的单元测试示例
describe('formConverter', () => {
  it('应该正确转换employeePicker数据', () => {
    const input = {
      formData: { employeePicker: '[{"name":"张三","id":"001"}]' },
      itemId: 'employee-001'
    };
    
    const result = convertForm([input]);
    
    expect(result).toEqual({
      'employee-001': [{ name: '张三', id: '001' }]
    });
  });
});
```

**关键原则**:
- 测试单一职责
- 输入输出明确
- 无外部依赖
- 执行速度快 (<10ms)

### 2. 集成测试 (Integration Tests)

**适用场景**: 组件间交互、API集成、状态管理

```typescript
// ✅ 好的集成测试示例
describe('CreateTicketPage Integration', () => {
  it('应该正确处理表单提交流程', async () => {
    const mockApi = vi.fn().mockResolvedValue({ success: true });
    
    render(<CreateTicketPage />);
    
    // 填写表单
    await userEvent.type(screen.getByLabelText('作业内容'), '测试作业');
    await userEvent.click(screen.getByText('提交'));
    
    // 验证API调用
    expect(mockApi).toHaveBeenCalledWith({
      workContent: '测试作业'
    });
  });
});
```

### 3. 组件测试 (Component Tests)

**适用场景**: React组件的渲染、交互、状态变化

```typescript
// ✅ 好的组件测试示例
describe('UserCard Component', () => {
  it('应该正确渲染用户信息', () => {
    const user = { name: '张三', email: '<EMAIL>' };
    
    render(<UserCard user={user} />);
    
    expect(screen.getByText('张三')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });
  
  it('应该处理编辑按钮点击', async () => {
    const onEdit = vi.fn();
    const user = { name: '张三', email: '<EMAIL>' };
    
    render(<UserCard user={user} onEdit={onEdit} />);
    
    await userEvent.click(screen.getByText('编辑'));
    
    expect(onEdit).toHaveBeenCalledWith(user);
  });
});
```

## 🎭 Mock策略

### Mock分层原则

```
应用层 ─── Mock API调用、路由、全局状态
组件层 ─── Mock子组件、第三方库
工具层 ─── Mock外部依赖、环境变量
```

### 1. API Mock

```typescript
// ✅ 推荐：使用MSW进行API Mock
import { rest } from 'msw';
import { setupServer } from 'msw/node';

const server = setupServer(
  rest.get('/api/users', (req, res, ctx) => {
    return res(ctx.json([
      { id: '1', name: '张三' },
      { id: '2', name: '李四' }
    ]));
  })
);

beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());
```

### 2. 组件Mock

```typescript
// ✅ Semi UI组件Mock模板
vi.mock('@douyinfe/semi-ui', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    Form: {
      Input: ({ field, ...props }: any) => (
        <input data-testid={`form-input-${field}`} {...props} />
      ),
      Select: ({ field, children, ...props }: any) => (
        <select data-testid={`form-select-${field}`} {...props}>
          {children}
        </select>
      ),
      // 添加其他需要的组件
    },
    Toast: {
      success: vi.fn(),
      error: vi.fn(),
      info: vi.fn(),
    },
  };
});
```

### 3. 状态管理Mock

```typescript
// ✅ Jotai状态Mock
vi.mock('jotai', () => ({
  useAtom: vi.fn(() => [
    { visible: false }, // 当前值
    vi.fn(),           // setter函数
  ]),
  atom: vi.fn((initialValue) => ({ init: initialValue })),
}));
```

## ⚡ 性能优化

### 1. 测试执行优化

```typescript
// vitest.config.ts 性能配置
export default defineConfig({
  test: {
    // 并行执行
    threads: true,
    maxThreads: 4,
    
    // 超时配置
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // 隔离配置
    isolate: true,
    
    // 线程池配置
    pool: 'threads',
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true,
      }
    }
  }
});
```

### 2. Mock优化

```typescript
// ✅ 轻量级Mock，避免重复创建
const createMockUser = () => ({ id: '1', name: 'Test User' });

// ❌ 避免：每次测试都创建复杂对象
const heavyMockData = {
  users: Array.from({ length: 1000 }, (_, i) => ({
    id: i,
    name: `User ${i}`,
    // ... 大量数据
  }))
};
```

### 3. 测试数据优化

```typescript
// ✅ 使用工厂函数生成测试数据
const createTestTicket = (overrides = {}) => ({
  id: 'test-ticket-1',
  title: '测试作业票',
  status: 'pending',
  ...overrides
});

// 使用
const ticket = createTestTicket({ status: 'approved' });
```

## 🔍 质量检查

### 1. 测试质量指标

```typescript
// 测试质量检查清单
const QUALITY_CHECKLIST = {
  coverage: {
    lines: 80,
    branches: 85,
    functions: 90,
  },
  performance: {
    maxTestTime: 100,      // ms
    maxFileTime: 500,      // ms
    maxTotalTime: 10000,   // ms
  },
  reliability: {
    minSuccessRate: 0.98,  // 98%
    maxFlakiness: 0.02,    // 2%
  }
};
```

### 2. 代码审查检查点

- [ ] 测试覆盖了所有主要分支
- [ ] Mock配置合理，不过度Mock
- [ ] 测试数据简洁明确
- [ ] 断言具体且有意义
- [ ] 测试名称描述清晰
- [ ] 无重复或冗余测试
- [ ] 性能符合基准要求

### 3. 自动化质量检查

```bash
# package.json scripts
{
  "test:quality": "yarn test --coverage --reporter=verbose",
  "test:performance": "node scripts/test-performance-monitor.js",
  "test:ci": "yarn test:quality && yarn test:performance"
}
```

## 🔄 维护流程

### 1. 日常维护

**每日检查**:
- [ ] CI测试通过率 >98%
- [ ] 覆盖率达标
- [ ] 无超时测试

**每周检查**:
- [ ] 性能基准对比
- [ ] 失败测试分析
- [ ] Mock配置更新

### 2. 版本维护

**每次发布前**:
- [ ] 运行完整测试套件
- [ ] 检查覆盖率报告
- [ ] 更新测试文档

**每月维护**:
- [ ] 清理过时测试
- [ ] 优化慢测试
- [ ] 更新Mock模板

### 3. 问题处理流程

```mermaid
graph TD
    A[测试失败] --> B{是否为环境问题?}
    B -->|是| C[修复环境配置]
    B -->|否| D{是否为Mock问题?}
    D -->|是| E[更新Mock配置]
    D -->|否| F{是否为代码变更?}
    F -->|是| G[更新测试用例]
    F -->|否| H[深入调试分析]
    
    C --> I[验证修复]
    E --> I
    G --> I
    H --> I
    
    I --> J{修复成功?}
    J -->|是| K[关闭问题]
    J -->|否| L[升级处理]
```

## 📚 参考资源

### 工具链

- **测试框架**: Vitest
- **测试库**: @testing-library/react
- **Mock库**: MSW (API), vi.mock (模块)
- **覆盖率**: V8 Coverage
- **性能监控**: 自定义脚本

### 最佳实践参考

- [Testing Library Best Practices](https://testing-library.com/docs/guiding-principles)
- [Vitest Best Practices](https://vitest.dev/guide/best-practices.html)
- [React Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)

### 团队规范

- 测试文件命名: `*.test.tsx` 或 `*.spec.tsx`
- 测试目录结构: `__tests__` 目录
- Mock文件位置: `src/mocks/` 或测试文件内
- 覆盖率报告: `coverage/` 目录

---

*最后更新: 2025-01-06*  
*维护者: 开发团队*
