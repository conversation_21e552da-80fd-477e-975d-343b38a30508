# Mock 模板库使用指南

## 📋 概述

Mock模板库提供了标准化、可复用的Mock配置，覆盖UI组件、状态管理、API等主流技术栈，确保测试的一致性和可维护性。

## 📁 模板库结构

```
src/mocks/templates/
├── semi-ui.ts           # Semi UI 组件 Mock 模板
├── state-management.ts  # 状态管理 Mock 模板
├── api.ts              # API Mock 模板
└── index.ts            # 统一导出文件
```

## 🎨 Semi UI Mock 模板

### 基础使用

```typescript
import { createSemiUIMock } from '@/mocks/templates/semi-ui';

// 完整 Mock (包含所有组件)
vi.mock('@douyinfe/semi-ui', createSemiUIMock);

// 轻量级 Mock (只包含常用组件)
import { createLightSemiUIMock } from '@/mocks/templates/semi-ui';
vi.mock('@douyinfe/semi-ui', createLightSemiUIMock);

// 自定义 Mock (只包含指定组件)
import { createCustomSemiUIMock } from '@/mocks/templates/semi-ui';
vi.mock('@douyinfe/semi-ui', () => 
  createCustomSemiUIMock(['Form', 'Button', 'Toast'])
);
```

### 表单组件测试

```typescript
import { createFormMocks } from '@/mocks/templates/semi-ui';

describe('表单组件测试', () => {
  beforeEach(() => {
    vi.mock('@douyinfe/semi-ui', () => createFormMocks());
  });

  it('应该正确渲染表单输入', () => {
    render(<FormComponent />);
    
    // 使用标准的 data-testid 查找元素
    const input = screen.getByTestId('form-input-username');
    expect(input).toBeInTheDocument();
    
    // 测试输入事件
    fireEvent.change(input, { target: { value: 'test' } });
    expect(input).toHaveValue('test');
  });
  
  it('应该处理表单提交', async () => {
    const onSubmit = vi.fn();
    render(<FormComponent onSubmit={onSubmit} />);
    
    // 填写表单
    const input = screen.getByTestId('form-input-username');
    fireEvent.change(input, { target: { value: 'testuser' } });
    
    // 提交表单
    const submitButton = screen.getByTestId('button');
    fireEvent.click(submitButton);
    
    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({ username: 'testuser' });
    });
  });
});
```

### 反馈组件测试

```typescript
import { createFeedbackMocks } from '@/mocks/templates/semi-ui';

describe('反馈组件测试', () => {
  beforeEach(() => {
    vi.mock('@douyinfe/semi-ui', () => createFeedbackMocks());
  });

  it('应该显示成功提示', async () => {
    const { Toast } = await import('@douyinfe/semi-ui');
    
    render(<SuccessComponent />);
    
    const button = screen.getByText('显示成功');
    fireEvent.click(button);
    
    expect(Toast.success).toHaveBeenCalledWith('操作成功');
  });
});
```

## 🔄 状态管理 Mock 模板

### Jotai Mock

```typescript
import { createJotaiMock, createAtomMocks } from '@/mocks/templates/state-management';

// 基础 Jotai Mock
vi.mock('jotai', () => createJotaiMock({
  userAtom: { id: '1', name: 'Test User' },
  themeAtom: 'light',
}));

// 预定义原子 Mock
const atoms = createAtomMocks({
  currentUser: { id: '1', name: 'Test User' },
  isAuthenticated: true,
  theme: 'light',
});

vi.mock('atoms', () => atoms);

// 测试示例
describe('Jotai 状态测试', () => {
  it('应该正确读取原子值', () => {
    const { useAtomValue } = require('jotai');
    const TestComponent = () => {
      const user = useAtomValue(atoms.currentUser);
      return <div>{user.name}</div>;
    };
    
    render(<TestComponent />);
    expect(screen.getByText('Test User')).toBeInTheDocument();
  });
});
```

### React Query Mock

```typescript
import { createReactQueryMock } from '@/mocks/templates/state-management';

vi.mock('@tanstack/react-query', () => createReactQueryMock({
  users: [
    { id: '1', name: '张三' },
    { id: '2', name: '李四' },
  ],
}));

describe('React Query 测试', () => {
  it('应该正确获取查询数据', () => {
    const { useQuery } = require('@tanstack/react-query');
    
    const TestComponent = () => {
      const { data } = useQuery(['users']);
      return <div>{data?.users?.length} users</div>;
    };
    
    render(<TestComponent />);
    expect(screen.getByText('2 users')).toBeInTheDocument();
  });
});
```

## 🌐 API Mock 模板

### 基础 API Mock

```typescript
import { createProjectApiMock, createApiResponse } from '@/mocks/templates/api';

// 使用项目 API Mock
vi.mock('api', () => createProjectApiMock());

describe('API 测试', () => {
  it('应该正确获取用户列表', async () => {
    const { getUserList } = await import('api');
    
    const result = await getUserList();
    
    expect(result).toEqual(createApiResponse([
      { id: '1', name: '张三', email: '<EMAIL>' },
      { id: '2', name: '李四', email: '<EMAIL>' },
    ]));
  });
});
```

### 自定义 API Mock

```typescript
import { createApiMockSuite } from '@/mocks/templates/api';

const apiMock = createApiMockSuite({
  baseURL: '/api/v1',
  responses: {
    'GET /api/v1/users': createApiResponse([
      { id: '1', name: 'Custom User' },
    ]),
  },
  enableDelay: true,
  delayMs: 100,
});

vi.mock('api', () => apiMock.api);

describe('自定义 API 测试', () => {
  it('应该处理延迟响应', async () => {
    const start = Date.now();
    await apiMock.api.getUserList();
    const duration = Date.now() - start;
    
    expect(duration).toBeGreaterThanOrEqual(100);
  });
});
```

### 错误处理测试

```typescript
import { createApiErrorMock } from '@/mocks/templates/api';

const errorApi = createApiErrorMock();

describe('API 错误处理', () => {
  it('应该处理网络错误', async () => {
    vi.mock('api', () => ({
      getUserList: errorApi.networkError,
    }));
    
    const { getUserList } = await import('api');
    
    await expect(getUserList()).rejects.toThrow('Network Error');
  });
  
  it('应该处理服务器错误', async () => {
    vi.mock('api', () => ({
      getUserList: errorApi.serverError,
    }));
    
    const { getUserList } = await import('api');
    const result = await getUserList();
    
    expect(result.code).toBe(500);
    expect(result.message).toBe('Internal Server Error');
  });
});
```

## 🔧 高级用法

### 组合 Mock

```typescript
import { createStateMockBundle } from '@/mocks/templates/state-management';
import { createSemiUIMock } from '@/mocks/templates/semi-ui';
import { createProjectApiMock } from '@/mocks/templates/api';

// 组合多个 Mock
const setupTestMocks = () => {
  // UI 组件 Mock
  vi.mock('@douyinfe/semi-ui', createSemiUIMock);
  
  // 状态管理 Mock
  const stateMocks = createStateMockBundle({
    jotai: { currentUser: { id: '1', name: 'Test User' } },
    reactQuery: { users: [] },
  });
  
  vi.mock('jotai', () => stateMocks.jotai);
  vi.mock('@tanstack/react-query', () => stateMocks['@tanstack/react-query']);
  
  // API Mock
  vi.mock('api', () => createProjectApiMock());
};

describe('集成测试', () => {
  beforeEach(() => {
    setupTestMocks();
  });
  
  it('应该正确处理完整的用户流程', async () => {
    // 测试完整的用户交互流程
    render(<UserManagementPage />);
    
    // 验证初始状态
    expect(screen.getByText('Test User')).toBeInTheDocument();
    
    // 模拟用户操作
    const addButton = screen.getByTestId('button');
    fireEvent.click(addButton);
    
    // 验证结果
    await waitFor(() => {
      expect(screen.getByText('用户添加成功')).toBeInTheDocument();
    });
  });
});
```

### 动态 Mock

```typescript
import { createStateTestUtils } from '@/mocks/templates/state-management';

describe('动态状态测试', () => {
  it('应该正确处理状态变化', () => {
    const { createMockSetter } = createStateTestUtils();
    
    // 创建动态状态
    const mockSetter = createMockSetter({ count: 0 });
    
    // 模拟状态更新
    mockSetter({ count: 1 });
    expect(mockSetter.getValue()).toEqual({ count: 1 });
    
    // 模拟函数式更新
    mockSetter((prev: any) => ({ count: prev.count + 1 }));
    expect(mockSetter.getValue()).toEqual({ count: 2 });
  });
});
```

## 📋 最佳实践

### 1. Mock 选择原则

```typescript
// ✅ 推荐：使用轻量级 Mock
import { createLightSemiUIMock } from '@/mocks/templates/semi-ui';
vi.mock('@douyinfe/semi-ui', createLightSemiUIMock);

// ❌ 避免：过度 Mock
vi.mock('@douyinfe/semi-ui', () => ({
  // 大量不必要的 Mock 配置
}));
```

### 2. Mock 复用

```typescript
// ✅ 推荐：创建可复用的 Mock 配置
const createTestSetup = (overrides = {}) => ({
  ...createSemiUIMock(),
  ...overrides,
});

// 在多个测试中复用
vi.mock('@douyinfe/semi-ui', () => createTestSetup());
```

### 3. Mock 验证

```typescript
// ✅ 推荐：验证 Mock 调用
it('应该调用正确的 API', async () => {
  const mockApi = vi.fn();
  vi.mock('api', () => ({ getUserList: mockApi }));
  
  render(<UserList />);
  
  await waitFor(() => {
    expect(mockApi).toHaveBeenCalledTimes(1);
  });
});
```

### 4. Mock 清理

```typescript
describe('测试套件', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });
  
  afterAll(() => {
    vi.restoreAllMocks();
  });
});
```

## 🔍 故障排除

### 常见问题

1. **Mock 不生效**
   ```typescript
   // 确保 Mock 在导入之前设置
   vi.mock('@douyinfe/semi-ui', createSemiUIMock);
   // 然后再导入组件
   import { MyComponent } from './MyComponent';
   ```

2. **TypeScript 类型错误**
   ```typescript
   // 使用类型断言
   const mockApi = vi.mocked(api.getUserList);
   ```

3. **Mock 数据不匹配**
   ```typescript
   // 确保 Mock 数据结构与实际 API 一致
   const mockData = createApiResponse(data); // 使用模板函数
   ```

## 📚 参考资源

- [Vitest Mock 文档](https://vitest.dev/guide/mocking.html)
- [Testing Library 最佳实践](https://testing-library.com/docs/guiding-principles)
- [Semi UI 组件文档](https://semi.design/)

---

*最后更新: 2025-01-06*  
*版本: v1.0*  
*维护者: 开发团队*
