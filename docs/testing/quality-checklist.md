# 测试质量检查清单

## 📋 概述

本清单用于确保测试代码的质量和一致性，适用于代码审查、CI/CD检查和日常开发。

## 🎯 覆盖率检查

### 基本覆盖率要求

- [ ] **行覆盖率** ≥ 80% (核心模块 ≥ 90%)
- [ ] **分支覆盖率** ≥ 85% (核心模块 ≥ 95%)
- [ ] **函数覆盖率** ≥ 90% (核心模块 100%)
- [ ] **语句覆盖率** ≥ 80%

### 覆盖率质量检查

- [ ] 覆盖率数据真实反映代码执行情况
- [ ] 没有通过过度Mock绕过实际代码执行
- [ ] 关键业务逻辑分支都被覆盖
- [ ] 错误处理路径被适当测试

## 🧪 测试代码质量

### 测试结构

- [ ] **测试文件命名规范**: `*.test.tsx` 或 `*.spec.tsx`
- [ ] **测试目录结构**: 使用 `__tests__` 目录或同级测试文件
- [ ] **测试分组合理**: 使用 `describe` 进行逻辑分组
- [ ] **测试名称清晰**: 描述测试的具体行为和预期结果

### 测试内容

- [ ] **单一职责**: 每个测试只验证一个具体行为
- [ ] **独立性**: 测试之间没有依赖关系
- [ ] **可重复性**: 测试结果稳定，不受执行顺序影响
- [ ] **断言明确**: 使用具体的断言，避免模糊验证

### 测试数据

- [ ] **测试数据最小化**: 只包含测试所需的最少数据
- [ ] **数据工厂**: 使用工厂函数生成测试数据
- [ ] **边界条件**: 测试边界值和异常情况
- [ ] **数据清理**: 测试后正确清理状态

## 🎭 Mock策略检查

### Mock合理性

- [ ] **Mock最小化**: 只Mock必要的外部依赖
- [ ] **Mock真实性**: Mock行为接近真实实现
- [ ] **Mock一致性**: 同类型Mock在项目中保持一致
- [ ] **Mock文档**: 复杂Mock有清晰的说明

### Mock配置

- [ ] **API Mock**: 使用MSW或类似工具Mock API调用
- [ ] **组件Mock**: 第三方组件Mock配置正确
- [ ] **状态Mock**: 状态管理Mock不影响测试逻辑
- [ ] **工具Mock**: 工具函数Mock保持简单

### Mock模板使用

- [ ] **Semi UI Mock**: 使用标准的Semi UI Mock模板
- [ ] **Jotai Mock**: 状态管理Mock配置正确
- [ ] **API Mock**: 使用项目统一的API Mock策略
- [ ] **自定义Mock**: 项目特定Mock有文档说明

## ⚡ 性能检查

### 执行性能

- [ ] **单个测试时间** < 100ms
- [ ] **单个文件时间** < 500ms
- [ ] **总执行时间** < 10秒 (完整测试套件)
- [ ] **内存使用** < 512MB

### 性能优化

- [ ] **并行执行**: 启用测试并行执行
- [ ] **资源清理**: 测试后正确清理资源
- [ ] **Mock轻量化**: 避免创建重型Mock对象
- [ ] **数据优化**: 测试数据大小合理

## 🔧 技术规范

### 代码风格

- [ ] **TypeScript**: 所有测试文件使用TypeScript
- [ ] **类型安全**: Mock和测试数据有正确的类型定义
- [ ] **ESLint**: 通过ESLint检查
- [ ] **Prettier**: 代码格式化一致

### 测试工具

- [ ] **Vitest**: 使用Vitest作为测试运行器
- [ ] **Testing Library**: 使用@testing-library/react进行组件测试
- [ ] **用户事件**: 使用@testing-library/user-event模拟用户交互
- [ ] **覆盖率工具**: 使用V8 Coverage收集覆盖率

### 配置检查

- [ ] **vitest.config.ts**: 配置文件正确设置
- [ ] **测试环境**: jsdom环境配置正确
- [ ] **全局设置**: 全局Mock和设置合理
- [ ] **覆盖率配置**: 覆盖率收集配置正确

## 🚀 CI/CD集成

### 自动化检查

- [ ] **测试执行**: CI中自动运行所有测试
- [ ] **覆盖率检查**: 自动检查覆盖率达标
- [ ] **性能监控**: 监控测试执行时间
- [ ] **质量门禁**: 测试失败阻止合并

### 报告生成

- [ ] **覆盖率报告**: 生成详细的覆盖率报告
- [ ] **性能报告**: 生成测试性能报告
- [ ] **失败分析**: 测试失败时提供详细信息
- [ ] **趋势分析**: 跟踪测试质量趋势

## 📊 质量指标

### 核心指标

| 指标         | 目标值     | 当前值 | 状态 |
| ------------ | ---------- | ------ | ---- |
| 行覆盖率     | ≥80%       | \_\_\_ | ⚪   |
| 分支覆盖率   | ≥85%       | \_\_\_ | ⚪   |
| 函数覆盖率   | ≥90%       | \_\_\_ | ⚪   |
| 测试通过率   | ≥98%       | \_\_\_ | ⚪   |
| 平均执行时间 | <15ms/test | \_\_\_ | ⚪   |

### 质量等级

- 🟢 **优秀**: 所有指标达标，无质量问题
- 🟡 **良好**: 主要指标达标，有轻微问题
- 🟠 **一般**: 部分指标达标，需要改进
- 🔴 **差**: 多项指标不达标，需要重点关注

## 🔍 代码审查检查点

### 审查者检查清单

- [ ] **测试逻辑正确**: 测试确实验证了预期行为
- [ ] **边界条件覆盖**: 包含了重要的边界情况测试
- [ ] **错误处理**: 测试了错误和异常情况
- [ ] **性能影响**: 新测试不会显著影响执行时间
- [ ] **维护性**: 测试代码易于理解和维护

### 常见问题检查

- [ ] **过度Mock**: 避免Mock过多导致测试失去意义
- [ ] **测试重复**: 避免重复测试相同的功能
- [ ] **断言不足**: 确保有足够的断言验证行为
- [ ] **测试脆弱**: 避免依赖实现细节的测试
- [ ] **资源泄漏**: 确保测试后正确清理资源

## 📝 文档要求

### 测试文档

- [ ] **README更新**: 测试相关的README信息是最新的
- [ ] **Mock文档**: 复杂Mock有使用说明
- [ ] **测试策略**: 项目测试策略文档完整
- [ ] **故障排除**: 常见测试问题的解决方案

### 注释要求

- [ ] **复杂测试**: 复杂测试逻辑有清晰注释
- [ ] **Mock说明**: 特殊Mock配置有说明注释
- [ ] **业务逻辑**: 业务相关测试有背景说明
- [ ] **性能考虑**: 性能相关的测试有说明

## 🔄 持续改进

### 定期检查

- [ ] **每周**: 检查测试通过率和覆盖率趋势
- [ ] **每月**: 分析慢测试和失败测试
- [ ] **每季度**: 评估测试策略和工具选择
- [ ] **每年**: 全面审查测试架构

### 改进建议

- [ ] **工具升级**: 定期评估和升级测试工具
- [ ] **最佳实践**: 学习和应用新的测试最佳实践
- [ ] **团队培训**: 定期进行测试相关的团队培训
- [ ] **经验分享**: 分享测试相关的经验和教训

---

## 📋 检查清单使用说明

### 使用场景

1. **代码审查**: 审查者使用此清单检查测试代码质量
2. **CI/CD**: 自动化流程中的质量门禁检查
3. **开发自检**: 开发者提交前的自我检查
4. **定期审计**: 项目质量定期审计

### 检查方式

- ✅ **通过**: 该项检查通过
- ❌ **失败**: 该项检查失败，需要修复
- ⚠️ **警告**: 该项有问题但不阻塞，建议改进
- ⚪ **跳过**: 该项不适用于当前检查

### 优先级

- 🔴 **P0**: 必须修复，阻塞发布
- 🟠 **P1**: 重要问题，尽快修复
- 🟡 **P2**: 一般问题，计划修复
- 🟢 **P3**: 优化建议，有时间时修复

---

_最后更新: 2025-01-06_
_版本: v1.0_
_维护者: 开发团队_
