# 侧边详情页第一次筛选失效 Bug 修复

> 日期：2025-07-09  
> 任务名称：SideFilterLoadingFix

## 1. 背景

业务同学反馈，侧边详情页（`RenderSideTabPane`）中的筛选功能第一次点击「查询」时不生效。

### 用户提供的具体现象（原始描述）

> 1. 第一次筛选查询时，页面上筛选条件被清空，API请求没有带上筛选条件，返回数据也不对；第二次才对(页面和数据都对)
> 2. 如果我注释 @renderSideTabPane.tsx line 88行，情况一样
> 3. 如果我注释 @FilterToolbar.tsx line53-57，页面上的筛选条件被清空，但是API请求参数正确，返回数据对
> 4. 如果我把2和3中的代码都注释掉，页面上的筛选条件被清空，但API请求参数正确，返回数据对

经过复现发现，与加载状态下组件树被卸载有关。

## 2. 现象复现与根因分析

### 问题根因

1. `RenderSideTabPane` 在 `isLoading` 为 `true` 时**提前返回**全屏 `Spin`，导致子组件 `FilterToolbar` 被卸载。
2. `FilterToolbar` 在挂载阶段注册了副作用：组件卸载时调用 `handleReset` 重置 `filterAtom`。
3. 因此第一次点击「查询」的完整时序：
   - 写入过滤条件 → `isLoading` 变 `true` → 组件被卸载触发 `handleReset` → API 第一次请求丢失过滤参数。

### 四种实验场景下发生了什么

| #   | 实验操作                                               | 触发链路                              | 结果解释                                 |
| --- | ------------------------------------------------------ | ------------------------------------- | ---------------------------------------- |
| 1   | 保持原代码                                             | 卸载触发 `handleReset` → 请求丢参数   | 页面表单和请求参数都被清空，第二次才正常 |
| 2   | 注释 renderSideTabPane 88 行（`useRecordChangeReset`） | 链路同 #1                             | 与 #1 现象相同                           |
| 3   | 注释 FilterToolbar 53-57 行（卸载时 `handleReset`）    | 卸载不再清空 atom，但表单仍被重新挂载 | 请求携带参数，表单因重建而清空           |
| 4   | 同时注释 #2 和 #3                                      | 卸载不清空 atom                       | 与 #3 相同                               |

> 结论：根因并不在 `useRecordChangeReset`，而是「加载态提前返回导致组件卸载 + 卸载时自动重置」的组合。

## 2.1 原始根因分析（完整内容备份）

> 以下内容为本次排查过程中最初输出的「问题根因」说明，按要求原样收录以便留档。

1. `RenderSideTabPane` 里有如下逻辑

```tsx
if (isLoading) {
  return (
    <div className="w-full flex items-center justify-center py-8">
      <Spin size="large" />
    </div>
  );
}
```

当点击「查询」后，`useQuery` 立刻把 `isLoading` 置为 `true`。组件进入早期 `return` 分支，**整棵子树（包括 `FilterToolbar`）被卸载**。

2. `FilterToolbar` 在挂载时注册了这一段副作用

```tsx
useEffect(() => {
  return () => {
    handleReset(); // 组件卸载时把过滤 atom 置空
  };
}, []);
```

因此第一次点击「查询」的完整时序是：

| 步骤 | 发生的事件                                                                            |
| ---- | ------------------------------------------------------------------------------------- |
| ①    | `handleSearch` 把表单值写入 `filterAtom`                                              |
| ②    | `isLoading = true`，`RenderSideTabPane` 提前 `return` → `FilterToolbar` 被卸载        |
| ③    | 卸载触发 `handleReset`，把刚写进去的过滤条件又清空                                    |
| ④    | React-Query 发起第一次请求——此时 `filterAtom` 已经被清空 → **请求参数里没有筛选条件** |
| ⑤    | 请求结束后 `isLoading = false`，组件重新挂载，表单由于重建而显示为空                  |
| ⑥    | 用户再点一次「查询」，这一次流程中没有再触发 `handleReset`，于是请求和页面都正确      |

这也解释了你的四种试验现象：

| #   | 试验                                                    | 结果原因                                                           |
| --- | ------------------------------------------------------- | ------------------------------------------------------------------ |
| 1   | 原代码                                                  | 第一次会触发②③④ → 请求缺少参数，表单也被清空                       |
| 2   | 注释 renderSideTabPane 88 行（`useRecordChangeReset`）  | 不影响②③④，现象相同                                                |
| 3   | 注释 FilterToolbar 53-57 行（`handleReset` 那段副作用） | 卸载仍发生，但不再清空 atom → API 参数正确；表单仍因卸载重建被清空 |
| 4   | 两处都注释                                              | 与 #3 相同，区别只是不会再去 reset atom                            |

## 3. 问题定位

- `RenderSideTabPane` 在 `isLoading` 为 `true` 时**提前返回**全屏 `Spin`，导致子组件 `FilterToolbar` 被卸载。
- `FilterToolbar` 内部的 `useEffect` 在组件卸载时调用 `handleReset`，把刚写入 `filterAtom` 的筛选条件清空。
- React-Query 的首个请求因此丢失过滤参数。

## 3. 解决方案（方案 A）

1. **删除早期返回**：去掉 `isLoading` 分支。
2. **覆盖式 Loading**：在最终 JSX 中添加 `relative` 容器和 `absolute` 遮罩，保证组件不被卸载。

```tsx
return (
  <div className="w-full overflow-y-auto flex flex-col gap-y-4 relative">
    {isLoading && (
      <div className="absolute inset-0 z-10 flex items-center justify-center bg-white/60">
        <Spin size="large" />
      </div>
    )}
    {/* 原有渲染逻辑 */}
  </div>
);
```

## 4. 变更内容

| 文件                                            | 变更                                          | 说明                 |
| ----------------------------------------------- | --------------------------------------------- | -------------------- |
| `src/components/detail/renderSideTabPane.tsx`   | **删除**早期 `return`；**新增**遮罩式 Loading | 保证筛选组件持续挂载 |
| `docs/dev-log/20250709-SideFilterLoadingFix.md` | **新增**                                      | 当前文档             |

## 5. 测试用例

- [x] 首次筛选：请求参数正确、页面保留筛选值。
- [x] 连续多次筛选：均正常。
- [x] 切换记录（`infoAtom.id` 变化）：`useRecordChangeReset` 仍能正常清空。
- [x] 导出功能：参数携带当前筛选条件。
- [x] 无筛选条件时：功能与之前一致。

## 6. 时间记录

| 阶段        | 开始  | 结束  | 时长      |
| ----------- | ----- | ----- | --------- |
| 问题定位    | 10:00 | 10:20 | 20m       |
| 方案讨论    | 10:20 | 10:30 | 10m       |
| 代码修改    | 10:30 | 10:40 | 10m       |
| 自测 & 验证 | 10:40 | 11:00 | 20m       |
| 文档编写    | 11:00 | 11:10 | 10m       |
| **合计**    |       |       | **1h00m** |

## 7. 后续

- 持续关注 FilterToolbar 卸载副作用，避免与 Loading 处理冲突。
- 待功能稳定后，可考虑抽象通用的 Loading 覆盖组件，在全局复用。
