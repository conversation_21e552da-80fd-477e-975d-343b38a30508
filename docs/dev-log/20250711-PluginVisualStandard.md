# 插件视觉识别统一标准开发日志（2025-07-11）

> 相关源码 / 文档引用：
>
> - 视觉工具库 utils.tsx：[src/components/region_plugins/utils.tsx](../../src/components/region_plugins/utils.tsx)
> - 插件元数据 regionPluginMeta.tsx：[src/pages/system/content/hiddenSWSettings/regionPluginMeta.tsx](../../src/pages/system/content/hiddenSWSettings/regionPluginMeta.tsx)
> - 规范文档 README.md：[src/pages/system/content/hiddenSWSettings/README.md](../../src/pages/system/content/hiddenSWSettings/README.md)

---

## 一、需求与目标

系统已有多地区「隐形特殊作业上报插件」，卡片配色与图标缺乏统一规范：

1. **颜色冲突**：同省不同园区插件使用随机颜色，无法快速识别。
2. **图标混用**：IconCloud / IconFlag / IconNavigation 等随意选用，没有层级含义。
3. 新插件接入时常需反复沟通视觉风格。

目标：

- **统一色相**：首字母固定色相，省份=颜色心智。
- **图标分级**：根据行政层级自动选 icon（省 / 市 / 园区）。
- **同省多插件可区分**：通过亮度阶梯区分园区。
- **自动化**：从“名称 + 层级”即可算出 icon + color，避免主观选择。

---

## 二、设计原则

| 维度   | 规则                                                             |
| ------ | ---------------------------------------------------------------- |
| 色相   | Tailwind 600 色板 12 色池，`pickProvinceColor(initial)` 固定映射 |
| 亮度   | 同省多插件用 `shadeColor(base, step)` 每级 +8% 亮度              |
| 图标   | `getIcon(level)`：province→Cloud；city→MapPin；zone→Flag         |
| 文件层 | 视觉工具集中到 `components/region_plugins/utils.tsx`             |

---

## 三、核心代码实现

### 1. utils.tsx

```tsx
// 色相固定
export const pickProvinceColor = (initial: string) => {
  /* …略… */
};

// 亮度阶梯
export const shadeColor = (hex: string, step = 0) => {
  /* …略… */
};

// 图标自动
export const getIcon = (level: "province" | "city" | "zone") => {
  /* …略… */
};
```

### 2. regionPluginMeta.tsx

```tsx
import { pickProvinceColor, shadeColor, getIcon } from "components/region_plugins/utils";

info: {
  name: "江西-新干盐化城",
  icon: getIcon("zone"),
  color: shadeColor(pickProvinceColor("J"), 2), // Amber+16%
}
```

### 3. README.md

- 新增 **7. 插件视觉识别统一规范** + **7.7 规则自动化细化说明**
- 详细描述色相池、首字母映射、亮度阶梯、图标分类及示例。

---

## 四、问题与解决

| #   | 问题                                            | 解决方案                                      |
| --- | ----------------------------------------------- | --------------------------------------------- |
| 1   | Semi 无 `IconCompass`                           | 改用 `IconMapPinStroked` 并抽象 `getIcon`     |
| 2   | JSX 写在 `.ts` 报 `IconCloud refers to a value` | 文件改名 `icon.tsx`（后合并入 utils）         |
| 3   | 同省两个江西插件颜色仍接近                      | `shadeColor` 阶梯从 +8% → +16% / +32%         |
| 4   | utils 放置不当                                  | 新建 `components/region_plugins` 目录统一维护 |

---

## 五、任务时间与耗时

| 阶段            | 开始      | 结束      | 耗时      | 备注                |
| --------------- | --------- | --------- | --------- | ------------------- |
| 需求梳理        | 10:00     | 10:20     | 20m       | 确认痛点            |
| 方案设计        | 10:20     | 10:45     | 25m       | 色相 & 图标映射规则 |
| utils.tsx 编码  | 10:45     | 11:20     | 35m       | pick / shade / icon |
| 元数据改造      | 11:20     | 11:40     | 20m       | 江西 + 辽宁 调整    |
| README 规范撰写 | 11:40     | 12:10     | 30m       | 7.3 & 7.7 章节      |
| 亮度阶梯微调    | 12:10     | 12:25     | 15m       | step 2 / 4 调整     |
| **总计**        | **10:00** | **12:25** | **2h25m** |                     |

---

## 六、开发总结 & 迁移建议

1. **自动化优先**：名称首字母 + 行政层级即可算出视觉元素，接入零心智负担。
2. **文件归并**：所有视觉相关函数集中 utils.tsx，避免散落 utils 目录。
3. **亮度阶梯**：避免再扩色池，优先在同色相上调亮度，保持品牌一致性。
4. **后续接入流程**：

```text
➊ 取省份拼音首字母 → pickProvinceColor
➋ 判断层级 → getIcon
➌ 同省多插件时传 step → shadeColor
➍ 在 regionPluginMeta.tsx 追加元数据
```

---

## 七、用户 prompt 备忘录（自动归纳）

1. 指出 icon / color 不统一，要求设计统一标准。
2. 讨论图标分类和首字母色相映射细节。
3. 确认使用 IconMapPinStroked 替代 Compass。
4. 要求自动映射函数并移入新目录。
5. 反复调整 `getIcon` 参数与颜色亮度差异。
6. 最终要求撰写开发日志。
