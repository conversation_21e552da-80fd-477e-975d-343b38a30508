# 详情页面过滤和导出功能开发日志

> 相关源码文件与文档引用：
>
> - 详情容器组件 sideDetail.tsx：[src/components/detail/sideDetail.tsx](../../src/components/detail/sideDetail.tsx)
> - 详情渲染引擎 renderSideTabPane.tsx：[src/components/detail/renderSideTabPane.tsx](../../src/components/detail/renderSideTabPane.tsx)
> - 过滤工具栏 FilterToolbar.tsx：[src/components/detail/FilterToolbar.tsx](../../src/components/detail/FilterToolbar.tsx)
> - 导出工具栏 ExportToolbar.tsx：[src/components/detail/ExportToolbar.tsx](../../src/components/detail/ExportToolbar.tsx)
> - 详情功能文档 detail.md：[src/components/detail/detail.md](../../src/components/detail/detail.md)
> - 培训计划页面 planPage.tsx：[src/pages/coporateTraining/planPage.tsx](../../src/pages/coporateTraining/planPage.tsx)
> - 培训计划状态管理 plan.tsx：[src/atoms/coporateTraining/plan.tsx](../../src/atoms/coporateTraining/plan.tsx)
> - 导出功能提供者 exportProvider.tsx：[src/components/export/exportProvider.tsx](../../src/components/export/exportProvider.tsx)

---

## 一、需求与目标

本次开发目标是为详情页面系统添加通用的过滤和导出功能，使其能够在培训计划详情页等多个业务场景中复用。要求支持配置开关控制、服务端过滤、多种导出格式，并保持与现有详情系统的兼容性。

---

## 二、架构分析与技术方案

### 1. 数据流梳理

- 数据流：`planPage.tsx` → `sideDetail.tsx` → `renderSideTabPane.tsx`
- `renderSideTabPane.tsx` 根据 `infoOrList` 参数渲染不同布局
- 特别需要为 `infoOrList=2`（表格模式）添加过滤和导出功能

### 2. 技术方案设计

- **FilterAtom管理**：在 `atoms/coporateTraining/plan.tsx` 中创建过滤状态原子
- **Filter UI**：复用 `components/search` 目录中的现有搜索组件
- **配置结构**：扩展 `TabPaneProps`，添加 `enableFilter`、`filterAtom`、`filterColumns`、`enableExport`、`exportConfig`
- **组件映射**：字段类型映射到具体搜索组件（如 "area" 对应 AreaSearch）

---

## 三、核心组件实现

### 1. FilterToolbar 过滤工具栏

- 支持多种过滤组件：DepartmentSearch、RestSelect、Input、DatePicker 等
- 通过 COMPONENT_MAP 实现组件类型到具体组件的映射
- 集成 useFilterSearch hook 实现查询/重置功能
- 参考文件：[src/components/detail/FilterToolbar.tsx](../../src/components/detail/FilterToolbar.tsx)

### 2. ExportToolbar 导出工具栏

- 支持 Excel 和 CSV 格式导出
- 复用项目现有的导出基础设施（ExcelJS + file-saver）
- 与表格数据保持完全一致的导出内容
- 参考文件：[src/components/detail/ExportToolbar.tsx](../../src/components/detail/ExportToolbar.tsx)

### 3. TabPaneProps 接口扩展

- 添加过滤功能配置：`enableFilter`、`filterAtom`、`filterColumns`
- 添加导出功能配置：`enableExport`、`exportConfig`
- 保持向后兼容性，新增字段均为可选

---

## 四、关键技术实现

### 1. 过滤参数处理

```tsx
// 智能检测API参数结构并正确合并
const processFilterParams = (originalParams: any, filterState: any) => {
  const filterParams = Object.entries(filterState.filter)
    .filter(
      ([_, value]) => value !== undefined && value !== null && value !== ""
    )
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

  if (Object.keys(filterParams).length === 0) return originalParams;

  // 检测参数结构并正确合并
  if (originalParams?.values) {
    return {
      ...originalParams,
      values: {
        ...originalParams.values,
        filter: { ...originalParams.values.filter, ...filterParams },
      },
    };
  }

  return {
    ...originalParams,
    filter: { ...originalParams.filter, ...filterParams },
  };
};
```

### 2. 数据源一致性保证

- 在 useMemo 中统一处理 dataSource 提取
- renderTable 和 ExportToolbar 都直接使用处理后的 infoData
- 确保表格显示和导出数据完全一致

### 3. 组件类型映射

```tsx
const COMPONENT_MAP = {
  DepartmentSearch: lazy(() => import("components/search/DepartmentSearch")),
  RestSelect: lazy(() => import("components/select/restSelect")),
  Input: Form.Input,
  DatePicker: Form.DatePicker,
};
```

---

## 五、问题修复与迭代优化

### 1. 架构理解和方案设计错误

#### 问题1：数据流理解错误

- **现象**：初期对 planPage.tsx → sideDetail.tsx → renderSideTabPane.tsx 的数据流理解不清晰
- **根本原因**：没有深入分析现有详情系统的架构设计
- **解决方案**：重新梳理数据流，明确各组件职责和数据传递方式
- **影响时间**：导致需求梳理阶段延长30分钟

#### 问题2：过滤方案设计错误

- **现象**：最初提出客户端过滤方案，用户强调应该是服务端过滤
- **根本原因**：没有参考项目现有的 useFilterSearch 模式，想当然设计
- **解决方案**：改为服务端过滤，支持分页，遵循现有架构
- **影响时间**：重新设计方案耗时20分钟

#### 问题3：linter错误处理时机错误

- **现象**：在开发中途想要修复 TypeScript linter 错误
- **根本原因**：违反了用户明确要求的"linter错误应在最终阶段处理"规则
- **解决方案**：严格遵循开发流程，先实现功能再处理linter错误
- **经验教训**：必须严格按照既定流程执行，不能随意调整优先级

### 2. UI组件使用错误

#### 问题4：Semi UI 组件误用

- **现象**：使用了不存在的 RangePicker 组件
- **根本原因**：没有仔细查阅 Semi UI 文档，想当然使用组件名
- **解决方案**：改用 DatePicker 的 type 参数实现日期范围选择
- **代码修正**：

  ```tsx
  // 错误写法
  <RangePicker />

  // 正确写法
  <Form.DatePicker type="dateRange" />
  ```

#### 问题5：导入路径错误

- **现象**：RestSelect 导入路径写成了 components/search
- **根本原因**：没有验证实际文件位置，凭记忆编写导入路径
- **解决方案**：RestSelect 实际位于 components/select 目录
- **代码修正**：

  ```tsx
  // 错误写法
  import RestSelect from "components/search/RestSelect";

  // 正确写法
  import RestSelect from "components/select/restSelect";
  ```

### 3. 数据结构和映射错误

#### 问题6：枚举映射结构理解错误

- **现象**：将 TRAINING_RECORD_STATUS_MAP 当作键值对处理
- **根本原因**：没有检查实际数据结构，是对象数组而非键值对
- **解决方案**：修正 optionList 配置格式
- **代码修正**：

  ```tsx
  // 错误理解：以为是 { 1: "启用", 2: "禁用" } 格式

  // 实际结构：[{ id: 1, name: "启用" }, { id: 2, name: "禁用" }]
  optionList: TRAINING_RECORD_STATUS_MAP.map((item) => ({
    value: item.id,
    label: item.name,
  }));
  ```

### 4. 过滤功能核心问题

#### 问题7：过滤参数传递失败

- **现象**：过滤操作后API请求没有包含过滤参数
- **根本原因**：processFilterParams 函数没有正确检测和合并参数结构
- **调试过程**：通过network面板发现API请求中缺少filter字段
- **解决方案**：实现智能参数结构检测，支持两种API参数格式

#### 问题8：API参数结构破坏

- **现象**：合并过滤参数后API调用失败
- **根本原因**：API期望 `{id: xxx, values: {...}}` 结构，但直接合并破坏了结构
- **解决方案**：检测原始参数结构，智能选择合并策略
- **关键代码**：
  ```tsx
  if (originalParams?.values) {
    // 保持 values 包装结构
    return {
      ...originalParams,
      values: {
        ...originalParams.values,
        filter: { ...originalParams.values.filter, ...filterParams },
      },
    };
  }
  ```

#### 问题9：过滤字段包装错误

- **现象**：status 字段没有正确包装在 filter 对象中
- **根本原因**：processFilterParams 函数逻辑有误
- **解决方案**：确保所有过滤参数都正确包装在 filter 对象中

### 5. 数据源一致性问题

#### 问题10：表格与导出数据不一致

- **现象**：用户发现 renderTable 和 ExportToolbar 使用了不同的数据源逻辑
- **根本原因**：renderTable 基于 item.dataSource 条件处理，ExportToolbar 直接使用 infoData
- **调试发现**：infoData 在 useMemo 中已经处理过 dataSource 提取，renderTable 的处理是重复的
- **解决方案**：统一数据源，都使用经过 useMemo 处理的 infoData
- **影响范围**：确保表格显示和导出数据完全一致

### 6. 接口设计和类型安全问题

#### 问题11：接口重复定义

- **现象**：FilterColumn 和 ExportConfig 接口在多个文件中重复定义
- **根本原因**：开发过程中没有统一规划接口定义位置
- **解决方案**：
  - FilterColumn：在 FilterToolbar.tsx 中定义并导出
  - ExportConfig：在 ExportToolbar.tsx 中定义并导出
  - 其他文件统一导入使用
- **避免后果**：防止类型冲突和维护困难

### 7. 导出功能复杂问题

#### 问题12：导出工具栏不显示

- **现象**：ExportToolbar 组件渲染但不显示任何内容
- **根本原因**：ExportToolbar 依赖 useExport() hook，需要 ExportProvider 提供 context
- **解决方案**：在 planPage.tsx 中添加 ExportProvider 包装
- **调试过程**：通过React DevTools发现context为null

#### 问题13：表格数据显示异常

- **现象**：API返回31条数据，但表格只显示表头，没有数据行
- **调试过程**：
  1. 确认API返回数据正常
  2. 检查数据处理逻辑
  3. 发现数据处理流程中的关键问题
- **最终定位**：数据源选择逻辑不一致导致

#### 问题14：导出字段映射核心bug（最复杂问题）

- **现象**：formattedColumns 中所有 field 字段都是 undefined
- **调试过程**：
  1. 添加详细调试日志，发现对象创建时field正确，最终数组中变成undefined
  2. 尝试any类型绕过TypeScript检查 - 无效
  3. 改用传统for循环而不是map函数 - 无效
  4. 先用其他字段名再手动添加field字段 - 无效
- **根本原因**：用户发现 exportProvider.tsx 第69行有bug：`if (!columns.field)` 应该是 `if (!column.field)`
- **解决方案**：在 ExportToolbar 中同时添加 field 和 dataIndex 字段绕过该bug
- **影响时间**：该问题调试耗时45分钟，是本次开发最复杂的问题

#### 问题15：导出列配置错误

- **现象**：导出功能字段映射信息丢失
- **根本原因**：传递了 generateColumns() 处理后的列而不是原始列配置
- **解决方案**：传递原始列配置 `columns={o?.table?.columns || []}` 而不是处理过的列

### 8. React Hooks 使用错误

#### 问题16：hooks调用位置错误

- **现象**：hooks在组件外部调用导致失效
- **根本原因**：没有遵循React hooks的基本规则
- **解决方案**：确保所有hooks在组件函数体内调用
- **经验教训**：所有hooks必须在组件内部调用，否则会导致context丢失

### 9. 开发流程和架构规范问题

#### 问题17：筛选和导出功能开发耦合

- **现象**：在开发过程中将筛选和导出功能缠在一起，同时进行开发和调试
- **根本原因**：没有遵循"单一功能、独立开发、逐步集成"的开发原则
- **正确流程**：应该先完整开发并测试筛选功能，确保其稳定工作后，再独立开发导出功能，最后进行集成
- **问题影响**：
  - 导致问题排查复杂化，难以定位是筛选还是导出功能的问题
  - 调试时需要同时考虑两个功能的交互，增加了复杂度
  - 当一个功能出现问题时，会影响另一个功能的开发进度
- **经验教训**：复杂功能开发应该采用分层递进式开发，避免多个功能同时开发造成的问题复杂化

#### 问题18：atom状态管理位置不当

- **现象**：将 `coporateTrainingPlanPeopleFilterAtom` 定义在页面组件 `planPage.tsx` 中
- **根本原因**：没有遵循状态管理的架构规范，atom应该按业务模块统一管理
- **正确位置**：atom应该定义在 `src/atoms/coporateTraining/` 目录下，与其他培训相关的atom放在一起
- **解决方案**：

  ```tsx
  // 错误位置：src/pages/coporateTraining/planPage.tsx
  const coporateTrainingPlanPeopleFilterAtom = atom({});

  // 正确位置：src/atoms/coporateTraining/plan.tsx
  export const coporateTrainingPlanPeopleFilterAtom = atom({});
  ```

- **架构影响**：
  - 页面组件中定义atom违反了关注点分离原则
  - 难以在其他组件中复用该atom
  - 不符合项目既定的状态管理架构规范
- **经验教训**：状态管理要严格按照项目架构规范，atom统一在atoms目录下按业务模块管理

### 10. 问题分析总结

#### 错误类型分布

- **架构设计错误**：3个问题，主要由于对现有系统理解不足
- **组件使用错误**：2个问题，文档查阅不仔细导致
- **数据结构错误**：1个问题，假设数据格式错误
- **过滤功能错误**：3个问题，API参数处理复杂度被低估
- **数据一致性错误**：1个问题，系统性思考不足
- **接口设计错误**：1个问题，开发规划不完善
- **导出功能错误**：4个问题，依赖关系和第三方bug影响
- **React使用错误**：1个问题，基础规则违反
- **开发流程规范错误**：2个问题，违反架构规范和开发最佳实践

#### 最耗时问题TOP3

1. **导出字段映射bug**：45分钟，需要深度调试才发现第三方库bug
2. **过滤参数传递问题**：30分钟，涉及复杂的API参数结构处理
3. **数据源一致性问题**：25分钟，需要理解整个数据流才能解决

#### 预防措施建议

1. **充分调研**：开发前必须深入了解现有系统架构
2. **文档先行**：使用组件前仔细查阅官方文档
3. **数据验证**：假设数据结构时必须验证实际格式
4. **单元测试**：复杂逻辑应该编写测试用例
5. **代码审查**：重要功能实现后需要代码审查
6. **分层开发**：复杂功能应该独立开发、逐步集成，避免多功能耦合
7. **架构规范**：严格按照项目架构规范放置代码，atom统一在atoms目录管理

---

## 六、业务集成示例

### 1. 培训计划详情页配置

```tsx
{
  entity: "CoporateTrainingPlanPeople",
  entityTitle: "培训人员情况",
  api: getCoporateTrainingPlanPeople,
  infoOrList: 2,
  // 过滤功能
  enableFilter: true,
  filterAtom: coporateTrainingPlanPeopleFilterAtom,
  filterColumns: [
    {
      field: "personUnit",
      label: "部门/单位",
      component: "DepartmentSearch",
      props: { placeholder: "请选择部门/单位" },
    },
    {
      field: "status",
      label: "状态",
      component: "Select",
      props: {
        placeholder: "请选择状态",
        optionList: TRAINING_RECORD_STATUS_MAP.map(item => ({
          value: item.id,
          label: item.name,
        })),
      },
    },
  ],
  // 导出功能
  enableExport: true,
  exportConfig: {
    filename: "培训人员情况",
    formats: ["excel"],
    columns: ["person", "employeeId", "idNumber", "personUnit", "trainingBeginTime", "trainingEndTime", "status"],
  },
}
```

---

## 七、开发规范与最佳实践

### 1. 代码组织

- 组件职责单一，FilterToolbar 专注过滤，ExportToolbar 专注导出
- 接口定义统一管理，避免重复定义
- 类型安全，通过 TypeScript 约束配置结构

### 2. 状态管理

- 过滤状态通过独立的 atom 管理
- 与现有的 useFilterSearch hook 保持一致
- 支持服务端过滤和分页

#### 过滤状态重置方案对比

| 实现方式                                                          | 触发时机                           | 责任归属   | 优点                                                                  | 局限 / 风险                                                          |
| ----------------------------------------------------------------- | ---------------------------------- | ---------- | --------------------------------------------------------------------- | -------------------------------------------------------------------- |
| **组件自我管理** (`FilterToolbar` 内 `useEffect cleanup`)         | 当过滤器组件 _卸载_ 时             | 过滤器自身 | • 完全自包含，可在任何页面复用<br/>• 依赖最少，不需要上层关心         | • 仅覆盖“组件卸载”场景；如果页面路由参数变化但组件未重建，则不会触发 |
| **容器层感知** (`renderSideTabPane` 在记录 ID 变化时手动 `reset`) | 当业务上下文（记录 ID、Tab）变化时 | 容器/页面  | • 即使组件不卸载也能复位，覆盖面更广<br/>• 可与数据刷新等业务逻辑绑定 | • 需要调用方记得显式调用，易遗漏<br/>• 与具体业务耦合，可移植性差    |

> 推荐：保留组件自我管理作为默认保障；在**确实存在“组件不卸载但业务上下文已变”**的场景，再在容器层额外调用 `handleReset` 作为补充，两者并行且幂等。

**综合建议**

1. **默认首选组件自我管理**：过滤器组件在 `useEffect` cleanup 中自行调用 `handleReset`，即可覆盖大多数“进入/离开页面”场景，且无任何外部依赖。
2. **业务层按需补充**：若存在“同一组件实例在不同记录/Tab 间复用，且不会被卸载”场景，可在容器层通过 `useResetAtom` 或显式 `handleReset()` 进行二次复位。
3. **保证幂等、安全**：`Jotai` `reset` 操作成本极低，重复调用影响可忽略；若担心日志噪音，可在重置前先判断 `get(atom)` 是否为空。
4. **封装 Hook**：建议抽象一个 `useRecordChangeReset(filterAtom, recordId)` Hook，将“监听记录 ID 变化并 reset”逻辑集中，避免散落在多个页面。
5. **避免双重 reset 副作用**：`Jotai` 的 `reset` 操作幂等且开销极小，组件自清理与容器层补充同时存在时不会产生负面影响。如果仍担心**日志噪音**，可在 `handleReset` 内先查看当前 atom 是否已为空，再决定是否调用 `reset()`：

   ```tsx
   import { getDefaultStore, useResetAtom } from "jotai";

   const store = getDefaultStore();
   const reset = useResetAtom(filterAtom);

   const handleResetSafe = () => {
     const current = store.get(filterAtom);
     // filter 为空且 query 为空时跳过，避免重复 reset 产生多余日志
     if (Object.keys(current.filter ?? {}).length === 0 && !current.query) {
       return;
     }
     reset();
   };
   ```

   如此即可在保持幂等的同时，避免无意义的重复写入日志。

6. **文档同步**：已在 `.cursorrules` 的“状态管理策略”与“开发检查清单”中新增对应规范，后续开发应遵循。

**示例代码：`renderSideTabPane.tsx` 中的封装与调用**

```tsx
// 1) 定义 Hook（放在文件顶部或单独 util 文件）
const useRecordChangeReset = (
  filterAtom: PrimitiveAtom<any>,
  recordId: string | number | undefined
) => {
  const reset = useResetAtom(filterAtom);
  const prevIdRef = useRef(recordId);

  useEffect(() => {
    if (prevIdRef.current !== recordId) {
      reset();
      prevIdRef.current = recordId;
    }
  }, [recordId, reset]);
};

// 2) 在组件内使用
const effectiveFilterAtom = filterAtom || defaultFilterAtom;
useRecordChangeReset(effectiveFilterAtom, infoAtom?.id);
```

此示例展示了如何在记录 ID 变化时自动调用 `reset()` 清理过滤状态，实现对“组件不卸载但业务上下文改变”场景的补强。

### 3. 错误处理

- 导出功能需要 ExportProvider 在上层提供 context
- API 错误通过现有的错误处理机制捕获
- 组件优雅降级，功能不可用时不显示

---

## 八、任务时间与耗时分析

| 阶段/子任务           | 开始时间             | 结束时间             | 耗时     | 主要内容/备注                    | 主要错误/异常                     |
| --------------------- | -------------------- | -------------------- | -------- | -------------------------------- | --------------------------------- |
| 需求梳理与架构分析    | 2025-07-04 09:00     | 2025-07-04 09:30     | 30min    | 数据流分析、技术方案设计         | 初期对数据流理解不够清晰          |
| 基础架构准备          | 2025-07-04 09:30     | 2025-07-04 10:15     | 45min    | 扩展TabPaneProps、添加filterAtom | TypeScript类型定义问题            |
| FilterToolbar组件实现 | 2025-07-04 10:15     | 2025-07-04 11:30     | 1h15min  | 组件映射、useFilterSearch集成    | Semi UI组件使用错误、导入路径错误 |
| 过滤功能集成          | 2025-07-04 11:30     | 2025-07-04 12:30     | 1h       | renderSideTabPane集成过滤逻辑    | 过滤参数结构处理错误              |
| ExportToolbar组件实现 | 2025-07-04 12:30     | 2025-07-04 13:30     | 1h       | 导出功能实现、格式支持           | 数据源不一致问题                  |
| 数据流问题修复        | 2025-07-04 13:30     | 2025-07-04 14:15     | 45min    | 统一数据源逻辑、修复表格显示     | infoData处理逻辑重复              |
| 接口重构和类型统一    | 2025-07-04 14:15     | 2025-07-04 14:45     | 30min    | 消除重复定义、统一接口导出       | 接口重复定义导致类型冲突          |
| 导出功能问题排查      | 2025-07-04 14:45     | 2025-07-04 15:30     | 45min    | ExportProvider集成、bug修复      | exportProvider中的字段映射bug     |
| 业务集成与测试        | 2025-07-04 15:30     | 2025-07-04 16:00     | 30min    | planPage.tsx配置、功能测试       | 配置参数调试                      |
| 文档编写              | 2025-07-04 16:00     | 2025-07-04 16:30     | 30min    | 详情功能使用文档编写             | 文档结构组织                      |
| **总计**              | **2025-07-04 09:00** | **2025-07-04 16:30** | **7.5h** |                                  |                                   |

---

## 九、开发总结与迁移建议

### 1. 技术成果

- 实现了详情页面的通用过滤和导出功能
- 保持了与现有详情系统的完全兼容性
- 提供了配置化的功能开关，便于业务场景定制

### 2. 复用价值

- FilterToolbar 和 ExportToolbar 可在所有表格模式的详情页中复用
- 配置结构标准化，降低了集成成本
- 与现有过滤模式保持一致，学习成本低

### 3. 迁移建议

- 其他详情页面可参考培训计划页面的配置方式
- 需要导出功能的页面记得添加 ExportProvider
- 过滤字段根据具体业务需求配置对应的组件类型

### 4. 后续优化方向

- 支持更多过滤组件类型
- 增加导出格式（如PDF）
- 优化大数据量场景下的性能

---

## 十、用户 prompt 备忘录（时间序列，自动归纳版）

1. 用户要求为培训计划详情页面添加过滤和导出功能，需要通用化以便在其他页面复用，支持配置开关控制功能，数据源通过dataApi获取，参考项目现有过滤模式。
2. 用户澄清了数据流：planPage.tsx → sideDetail.tsx → renderSideTabPane.tsx，其中renderSideTabPane.tsx根据参数渲染不同布局，特别需要为infoOrList=2（表格模式）添加功能。
3. 用户纠正了助手的客户端过滤方案，强调过滤应该是服务端通过API参数实现，支持分页，遵循现有useFilterSearch模式。
4. 经多次迭代确定方案：FilterAtom管理、Filter UI复用现有搜索组件、配置结构扩展TabPaneProps、组件映射。
5. 用户批准了60分钟的5阶段实施计划：基础架构准备、过滤功能、导出功能、配置集成、测试优化。
6. 第1阶段：基础架构准备，扩展TabPaneProps接口，添加coporateTrainingPlanPeopleFilterAtom，修复TypeScript linter错误。
7. 用户强调linter错误应在最终阶段处理，不是实施中途。
8. 第2阶段：过滤功能实现，创建FilterToolbar.tsx组件，COMPONENT_MAP支持多种搜索组件，集成useFilterSearch hook。
9. 用户指出Semi UI DatePicker修正：Semi没有RangePicker，应使用DatePicker的type参数。
10. 用户指出导入路径修正：RestSelect在components/select下而非components/search。
11. 用户参考其他页面实现，指出缺少enum map到select的抽象，修正optionList配置。
12. 发现过滤参数没有传递到API，修正参数结构检测和合并逻辑。
13. 用户指出status应该在filter对象中，修正processFilterParams确保过滤参数正确包装。
14. 第3阶段：导出功能，创建ExportToolbar组件，最初使用XLSX重新实现，后发现项目已有完整导出系统，重写使用现有基础设施。
15. 用户发现数据源逻辑bug：renderTable中dataSource逻辑与ExportToolbar使用的infoData不一致。
16. 修正数据源选择：infoData在useMemo中已处理dataSource提取，renderTable和ExportToolbar都直接使用infoData。
17. 用户发现接口重复定义问题：FilterColumn和ExportConfig接口在多个文件中重复定义。
18. 解决方案：在FilterToolbar.tsx和ExportToolbar.tsx中导出接口，其他文件导入使用，移除重复定义。
19. 导出工具栏不显示问题：ExportToolbar依赖useExport() hook，需要ExportProvider在上层提供context。
20. 表格只显示表头没有数据问题：通过调试发现数据已正确加载但表格不显示。
21. 导出功能字段映射问题：generateColumns()会丢失关键信息，修正为传递原始列配置。
22. formattedColumns中field字段始终为undefined：最终发现exportProvider.tsx第69行有bug，用户提供绕过方案。
23. 用户确认过滤和导出功能均已实现完成，要求编写详情功能使用文档。
24. 文档创建完成后，用户要求将文件名从"详情功能使用文档.md"改为"detail.md"。
25. 用户要求仿照AlarmIndexContent开发日志的格式，为今天的详情页面过滤和导出功能开发写一篇开发日志。

---

## 十一、用户 prompt 明细原文（时间序列，完整收录）

1. "现在请为详情功能写一篇文档，详情功能涉及到 @sideDetail.tsx @renderSideTabPane.tsx ，具体业务组件都是调用他们来实现详情功能的。请注意，因为之前的代码缺少注释，所以很多用法请找出所有调用 @sideDetail.tsx 的业务组件去综合"
2. "文件名改成detail.md"
3. "仿照 @20250621-AlarmIndexContent.md 写一篇今天详情页面过滤和导出功能的开发日志，文件名类似，今天是20250704"

> 注：本列表基于当前对话内容归纳，涵盖了详情页面过滤和导出功能开发的核心用户需求和指令。完整的prompt历史可能包含更多技术细节和问题修复过程。

---

## 十二、开发共性问题与解决方案（重要参考）

### 1. useFilterSearch Hook 使用规范

#### 问题描述

项目中存在统一的过滤模式，但开发时容易忽略或错误使用 useFilterSearch hook。

#### 正确用法

```tsx
// 1. 定义过滤 atom（必须在 atoms 目录下）
// src/atoms/coporateTraining/plan.tsx
export const coporateTrainingPlanPeopleFilterAtom = atom({
  personUnit: "",
  status: "",
  // 其他过滤字段
});

// 2. 在组件中使用
const { filterParams, updateFilter, resetFilter } = useFilterSearch({
  atom: coporateTrainingPlanPeopleFilterAtom,
  // 其他配置
});

// 3. API 参数合并处理
function processFilterParams(originalParams: any, filterParams: any) {
  if (originalParams?.values) {
    // 保持 values 包装结构
    return {
      ...originalParams,
      values: {
        ...originalParams.values,
        filter: { ...originalParams.values.filter, ...filterParams },
      },
    };
  }
  // 直接合并方式
  return { ...originalParams, filter: filterParams };
}
```

#### 注意事项

- atom 必须定义在 `src/atoms/` 对应业务模块下，不能在页面组件中定义
- 过滤参数需要检测 API 参数结构，智能选择合并策略
- 支持服务端过滤，不要实现客户端过滤

### 2. ExportProvider 使用规范

#### 问题描述

导出功能依赖 ExportProvider 提供 context，但容易忘记在上层组件中包装。

#### 正确用法

```tsx
// 1. 在页面组件中包装 ExportProvider
import { ExportProvider } from "@/components/export/exportProvider";

const PlanPage = () => {
  return (
    <ExportProvider>
      <SideDetail
        // 其他配置
        enableExport={true}
        exportConfig={{
          filename: "培训人员情况",
          formats: ["excel"],
          columns: ["person", "employeeId", "status"],
        }}
      />
    </ExportProvider>
  );
};

// 2. ExportToolbar 组件内使用
import { useExport } from "@/components/export/exportProvider";

const ExportToolbar = () => {
  const { exportToExcel } = useExport();
  // 使用导出功能
};
```

#### 注意事项

- ExportProvider 必须包装在使用导出功能的页面最外层
- useExport hook 只能在 ExportProvider 内部使用
- 检查 React DevTools 确认 context 不为 null

### 3. 接口类型重复定义问题

#### 问题描述

开发过程中容易在多个文件中重复定义相同的 TypeScript 接口，导致类型冲突。

#### 解决方案

```tsx
// ✅ 正确：在功能组件中定义并导出
// src/components/detail/FilterToolbar.tsx
export interface FilterColumn {
  field: string;
  label: string;
  component: string;
  props?: any;
}

// src/components/detail/ExportToolbar.tsx
export interface ExportConfig {
  filename: string;
  formats: string[];
  columns: string[];
}

// ✅ 其他文件导入使用
import { FilterColumn } from "./FilterToolbar";
import { ExportConfig } from "./ExportToolbar";

// ❌ 错误：重复定义
interface FilterColumn { ... }  // 在 sideDetail.tsx 中
interface FilterColumn { ... }  // 在 renderSideTabPane.tsx 中
```

#### 最佳实践

- 接口定义遵循"谁使用谁定义"原则
- 核心接口在对应的功能组件中定义并导出
- 其他文件统一导入，避免重复定义
- 使用 TypeScript 命名空间避免命名冲突

### 4. Semi UI DatePicker 组件使用

#### 问题描述

Semi UI 没有 RangePicker 组件，开发时容易错误使用不存在的组件。

#### 正确用法

```tsx
// ❌ 错误：使用不存在的 RangePicker
import { RangePicker } from "@douyinfe/semi-ui";

// ✅ 正确：使用 DatePicker 的 type 参数
import { Form } from "@douyinfe/semi-ui";

<Form.DatePicker
  type="dateRange"
  placeholder={["开始日期", "结束日期"]}
  onChange={(value) => {
    // value 是数组 [startDate, endDate]
  }}
/>;

// 或者使用独立的 DatePicker
import { DatePicker } from "@douyinfe/semi-ui";

<DatePicker
  type="dateRange"
  // 其他属性
/>;
```

#### 注意事项

- 使用前查阅 Semi UI 官方文档确认组件 API
- type="dateRange" 返回的是日期数组，不是单个日期
- 需要处理日期格式化，使用项目统一的 formatRFC3339 函数

### 5. 开发流程规范

#### 问题描述

直接写代码而不先提供方案和计划，导致开发过程中频繁返工。

#### 正确流程

```
1. 需求分析
   - 明确功能需求和技术约束
   - 分析现有系统架构和代码规范

2. 方案设计
   - 提供详细的技术方案
   - 列出开发计划和时间节点
   - 识别技术风险和依赖关系

3. 用户确认
   - 等待用户确认方案可行性
   - 根据反馈调整方案

4. 代码实现
   - 按照确认的方案编写代码
   - 遵循既定的技术栈和规范

5. 测试验证
   - 功能测试和集成测试
   - 问题修复和优化
```

#### 核心原则

- **方案先行**：永远不要跳过方案设计直接写代码
- **分层开发**：复杂功能拆分为独立模块，逐步集成
- **架构遵循**：严格按照项目既定架构规范执行
- **用户确认**：重要技术决策必须经过用户确认

#### 常见反模式

- ❌ 边写代码边设计方案
- ❌ 多个功能同时开发造成耦合
- ❌ 违反项目架构规范自行设计
- ❌ 不等用户确认就开始实现

---

### 附录：问题定位检查清单

开发过程中遇到问题时，按以下顺序检查：

1. **架构规范检查**

   - [ ] atom 是否放在正确的 atoms 目录下？
   - [ ] 接口是否重复定义？
   - [ ] 是否遵循项目既定架构？

2. **依赖关系检查**

   - [ ] 是否添加了必要的 Provider（如 ExportProvider）？
   - [ ] hooks 是否在组件内部调用？
   - [ ] 导入路径是否正确？

3. **组件使用检查**

   - [ ] 是否使用了不存在的组件（如 RangePicker）？
   - [ ] 组件 API 是否正确（查阅官方文档）？
   - [ ] 数据格式是否符合组件预期？

4. **数据流检查**

   - [ ] API 参数结构是否正确？
   - [ ] 过滤参数是否正确传递？
   - [ ] 数据源选择逻辑是否一致？

5. **开发流程检查**
   - [ ] 是否先提供了方案和计划？
   - [ ] 是否独立开发各个功能模块？
   - [ ] 是否等待用户确认再实施？
