# 隐蔽作业票插件体系重构开发日志（HiddenSWSettings）

> 相关源码文件与文档引用：
>
> - 插件元数据 `regionPluginMeta.tsx`：[src/pages/system/content/hiddenSWSettings/regionPluginMeta.tsx](../../src/pages/system/content/hiddenSWSettings/regionPluginMeta.tsx)
> - 地区表单组件集合 `regionPluginConfigForms.tsx`：[src/pages/system/content/hiddenSWSettings/regionPluginConfigForms.tsx](../../src/pages/system/content/hiddenSWSettings/regionPluginConfigForms.tsx)
> - 主页面内容 `content.tsx`：[src/pages/system/content/hiddenSWSettings/content.tsx](../../src/pages/system/content/hiddenSWSettings/content.tsx)
> - 插件卡片 UI `RegionPluginCard.tsx`：[src/pages/system/content/hiddenSWSettings/RegionPluginCard.tsx](../../src/pages/system/content/hiddenSWSettings/RegionPluginCard.tsx)

---

## 一、需求与目标

1. **消除魔法数字**：所有插件信息及缓存逻辑不再硬编码 `1 / 2 …`，完全基于配置生成。
2. **统一数据源**：集中维护 `REGION_PLUGIN_META`，包含 `id / info / Form`，增删插件“一处修改”。
3. **缓存与渲染自动化**：`content.tsx` 动态初始化缓存、合并提交数据，卡片与表单渲染均基于清单遍历。
4. **类型与样式修复**：补齐 `values`、`pluginId` 类型；移除 `styled-jsx`，使用原生 `<style>` + Tailwind。

---

## 二、核心改动概览

| 模块                          | 关键改动                                                | 备注                       |
| ----------------------------- | ------------------------------------------------------- | -------------------------- |
| `regionPluginMeta.tsx`        | 新增 & 重构 → `info` + 必填 `Form` 字段                 | 插件单一数据源             |
| `regionPluginConfigForms.tsx` | 初期过渡层 → **后由用户手动重组**，内联三个地区表单组件 | 未来可继续拆分公共 Section |
| `content.tsx`                 | 动态 `initialCache`、`mergedValues`、卡片/表单渲染      | 完全删除魔法数字           |
| `RegionPluginCard.tsx`        | 仅负责 UI，读取 `info`                                  | 解除循环依赖               |

---

## 三、未完成 / 后续计划

| 待办项                                      | 说明                                                                                                                               | 优先级 |
| ------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------- | ------ |
| **抽取公共表单 Section**                    | 三个地区组件存在大量重复（基础设置、开关设置、其他设置）<br>计划提炼如 `BasicSettingsSection` / `ReportSwitchSection` 等可复用组件 | ⭐⭐⭐ |
| **移除旧 Hook** `useRegionPluginConfigList` | 当前 `regionPluginConfigForms.tsx` 仍占用；需彻底迁移后删除                                                                        | ⭐⭐   |
| **严格类型化 FormValues**                   | 现为 `Record<string, any>`；待接口最终敲定后细化                                                                                   | ⭐⭐   |
| **E2E 插件切换测试**                        | 确保缓存恢复、动画、提交数据在多插件场景无缺失                                                                                     | ⭐     |
| **使用全局 hide-scrollbar 类**              | 已就地 `<style>` 解决；后续可提到 `global.css`                                                                                     | ⭐     |

---

## 四、如何新增一个插件（操作指南）

> 假设需要新增 **“贵州-赤水化工园区”** 插件，reportType = 4。

1. **在 `regionPluginConfigForms.tsx` 新增表单组件**

```tsx
export const GuizhouChishuiConfigContent: React.FC = () => {
  const formState = useFormState();
  if (formState?.values.reportType !== 4) return null;

  // TODO: 复制云南模板修改字段 & 配色 `#FF5722`
  return <div>…</div>;
};
```

2. **在 `regionPluginMeta.tsx` 注册元数据**

```tsx
import { IconFlag } from "@douyinfe/semi-icons";
import { GuizhouChishuiConfigContent } from "./regionPluginConfigForms";

export const REGION_PLUGIN_META = [
  // …已有 1,2,3
  {
    id: 4,
    info: {
      name: "贵州-赤水化工园区",
      description: "配置贵州赤水化工园区特有的上报参数",
      icon: <IconFlag />,
      color: "#FF5722",
      version: "v1.0.0",
      lastUpdate: "2025-07-10",
      enabled: true,
    },
    Form: GuizhouChishuiConfigContent,
  },
] as const;
```

3. **无需其他改动**

   - `content.tsx` 会自动生成缓存键 `4`、渲染卡片 & 表单、合并提交数据。
   - 若有特殊字段，需要在表单组件内自行补充。

4. **可选：添加色板 / icon**
   - UI 设计层如需扩展，可在 `REGION_PLUGIN_META` 的 `color` / `icon` 字段自定义。

---

## 五、时间线与耗时统计

| 阶段                        | 开始  | 结束  | 耗时   | 说明                               |
| --------------------------- | ----- | ----- | ------ | ---------------------------------- |
| 需求梳理                    | 20:00 | 20:20 | 20min  | 明确目标、制定拆分计划             |
| `regionPluginMeta.tsx` 建立 | 20:20 | 20:40 | 20min  | 初版 & 二次结构调整                |
| 过渡层重命名                | 20:40 | 21:00 | 20min  | 创建 `regionPluginConfigForms.tsx` |
| `content.tsx` 大改          | 21:00 | 22:10 | 70min  | 动态缓存、合并、渲染重构           |
| Linter 修复                 | 22:10 | 22:25 | 15min  | 类型声明 & 样式标签                |
| 表单组件重组（手动）        | 22:25 | 23:00 | 35min  | 用户手动优化云南/江西3组件         |
| **总计**                    | 20:00 | 23:00 | **3h** |                                    |

---

## 六、用户 prompt 备忘（时间序列，自动归纳版）

1. 需求：作业票上报配置页面数据结构混乱，需优化魔法数字、类型错误
2. 用户指出缓存写死 `1/2` 不便维护，需动态生成
3. 用户确认 `Form` 字段必填，要求供表单渲染
4. 用户同意重命名并精简过渡层
5. 用户批准修复 linter 错误并移除 `<style jsx>`
6. 用户要求彻底删除旧 `useRegionPluginConfigList` 依赖
7. 用户手动重组 `regionPluginConfigForms.tsx`，要求检查现状
8. 用户要求编写本开发日志，并关注“未完成计划 & 新增插件指南”

> _注：此列表自动摘录本次对话关键指令供回溯。_

---

## 七、插件使用文档（整合自 `README.md`）

> 以下内容原位于 `src/pages/system/content/hiddenSWSettings/README.md`，此处汇总方便单文档查阅。

### 7.1 模块结构 & 关键源码

| 角色         | 文件                                                                                                         | 描述                                                     |
| ------------ | ------------------------------------------------------------------------------------------------------------ | -------------------------------------------------------- |
| 插件清单     | [`regionPluginMeta.tsx`](../../src/pages/system/content/hiddenSWSettings/regionPluginMeta.tsx)               | 单一数据源，集中维护所有插件元数据（`id / info / Form`） |
| 表单组件集合 | [`regionPluginConfigForms.tsx`](../../src/pages/system/content/hiddenSWSettings/regionPluginConfigForms.tsx) | 各地区上报表单实现（云南 / 江西…）                       |
| 卡片 UI      | [`RegionPluginCard.tsx`](../../src/pages/system/content/hiddenSWSettings/RegionPluginCard.tsx)               | 插件选择卡片，仅负责展示                                 |
| 页面容器     | [`content.tsx`](../../src/pages/system/content/hiddenSWSettings/content.tsx)                                 | 渲染卡片 + 表单，处理缓存与提交                          |

### 7.2 数据流概览

```mermaid
flowchart LR
  REGION_PLUGIN_META-- 遍历 -->Card[RegionPluginCard list]
  REGION_PLUGIN_META-- 遍历 -->Forms[各地区 Form]
  Card-- 点击切换 -->content.tsx
  content.tsx-- setValues/缓存 -->Forms
```

### 7.3 `RegionPluginMeta` 字段说明

| 字段               | 类型        | 说明                                 |
| ------------------ | ----------- | ------------------------------------ |
| `id`               | `number`    | reportType，与后端保持一致，必须唯一 |
| `info.name`        | `string`    | 卡片标题                             |
| `info.description` | `string`    | 鼠标悬浮/详情描述                    |
| `info.icon`        | `ReactNode` | 卡片左侧图标（Semi Icons）           |
| `info.color`       | `string`    | 主题色（HEX/RGB），用于边框 / 选中态 |
| `info.version`     | `string`    | 插件版本号                           |
| `info.lastUpdate`  | `string`    | 最近一次更新日期                     |
| `info.enabled`     | `boolean?`  | 是否启用，控制灰显 / 绿点            |
| `Form`             | `React.FC`  | 该地区专属表单组件                   |

### 7.4 新增插件步骤（示例）

1. 在 `regionPluginConfigForms.tsx` 新增表单组件 `GuizhouChishuiConfigContent`，判断 `reportType`。
2. 在 `regionPluginMeta.tsx` 注册元数据：`id`, `info`, `Form`。
3. 无需其他改动，页面即自动支持新插件。

### 7.5 修改 / 禁用插件

- **调整字段**：编辑对应 `*ConfigContent`。
- **更新版本 / 颜色**：修改 `info.version` / `info.color`。
- **禁用**：`info.enabled = false`，卡片视觉弱化。

### 7.6 常见问题 & 未来优化

| 问题           | 建议                                            |
| -------------- | ----------------------------------------------- |
| 卡片点击无反应 | 检查 `id` 唯一性、`Form` 引入是否正确           |
| 提交缺字段     | 确认表单字段名称与后端一致，且 `rules` 配置正确 |
| 颜色或图标无效 | 颜色需 6 位 HEX；图标为合法 JSX                 |

**未来优化**：公共 Section 抽取、更严格类型、E2E 测试覆盖。
