<!doctype html>
<html lang="en" class="antialiased text-black" data-theme="vrtheme">

<head>
  <link rel="stylesheet" href="/cesium/Widgets/widgets.css">
  <script src="/cesium/Cesium.js"></script>

  <meta charset="UTF-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="referrer" content="origin" />
  <link rel="apple-touch-icon" href="/images/apple-touch-icon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Vitamin" />
  <meta name="theme-color" content="#42b883" />
  <style>/*  */

    .vr-loading {
      width: 30px;
      height: 30px;
      border: 3px solid #3b82f6;
      border-top-color: transparent;
      border-radius: 100%;
      animation: circle infinite 0.75s linear;
    }

    @keyframes circle {
      0% {
        transform: rotate(0);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .vr-loading-warp {
      width: 100vw;
      height: 80vh;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  </style>
  <script type="text/javascript"
    src="https://api.tianditu.gov.cn/api?v=4.0&tk=f662c465df02ee66294ca599ac027235"></script>
  <script type="module" crossorigin src="/index-80f7a2ee.js"></script>
  <link rel="stylesheet" href="/assets/index-8f65b422.css">
</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root">
    <div class="vr-loading-warp">
      <div class="vr-loading"></div>
    </div>
  </div>
  
</body>

</html>