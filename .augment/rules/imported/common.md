---
type: "always_apply"
---

# AI 开发规则

下面是一套开发规则（rules），适用于 **同时开发 Go 和 React 项目** 的中文程序员身份。这些规则旨在帮助统一编码风格、提高效率、减少低级错误，也方便团队协作。

---

## 基本原则

- 除非不得已（比如名词准确性需要，或者需要和其他语言的开发人员合作），请主要使用中文和我交流
- 在实际编写代码之前，请永远先列出方案和计划，以便审核。方案中给出伪代码即可

1. 保持礼貌和专业

- 使用恰当的语言和礼貌用语
- 保持专业的交流态度
- 避免使用不当或冒犯性的语言

2. 清晰的沟通

- 提供明确和具体的问题描述
- 使用准确的技术术语
- 如有需要请提供相关的代码示例或错误信息

3. 遵守道德准则

- 不要要求生成有害或违法的内容
- 尊重知识产权和版权
- 不分享敏感或私人信息

4. 最佳实践

- 保存重要对话内容
- 定期检查更新的功能和规则
- 合理使用系统资源

5. 经验总结

- 每次的chat都是一个独立的实现，可能是一个业务需求，也可能是一个技术方案，或者其它。所以这其中的对话内容非常重要，代表了开发过程，和其中经历的一些经验和错误。需要记录下来。
- 这其中，重要的开发内容，比如通用组件/库/类，需要同时生成对应的文档，这个文档可以放到源码同级目录下
- 在chat的最后，也就是任务完成之后，需要写一篇markdown格式的开发日志，放置于`docs/dev-log`目录下，名称为`${日期}-${任务名称}.md`
- 开发日志里应记录开始和结束事件，来回溯本次任务的时长；还有每次子任务的时长，以及其中的错误和异常。这样可以分析出到底哪些部分是耗时最长的，哪些部分是出错最多的。
- 特别的，每次的chat中，我的完整prompt列表也需要记录下来，作为开发日志的最后部分。
- 同时，也应该将本次chat的所有内容导出，并生成文档放到相同目录下，名称为`${日期}-${任务名称}-detail.md`

---

## 💻 通用代码规则（Go & React 都适用）

### 架构设计

#### 内容

- 请首先提供完整的代码生成方案，明确写出详细的计划（最好先用列表的形式进行总结）
- 请提供清晰的架构设计方案, 说明改动的模块划分和功能实现
- 请说明使用的技术栈和框架
- 请说明使用的设计模式和最佳实践
- 请说明生成代码的用法和示例
- 将以上内容记录并生成相关的设计文档或原型

### 原则

- 在设计过程中，请参考整个项目的代码，尽量复用已有的代码，比如参考已有的组件、库、类以及类似实现。
- 譬如参考React项目的package.json文件中的依赖库，或者Go项目的go.mod文件中的依赖库，或者Python项目的requirements.txt文件中的依赖库，或者Java项目的pom.xml文件中的依赖库。
- 新生成的文件，请根据项目之前的惯例，放置于对应的目录下。
- 修改的时候，保持最小化，尤其是已有的底层组件/库/类，如无必要，不要修改；确实需要修改的话，请保持兼容性，不要破坏原有功能
- 文件的修改，不要直接覆盖之前的内容，而是在之前的内容后面添加。

### 编辑器设置（建议放入 `.editorconfig` 或 编辑器 配置）

```ini
root = true

[*]
indent_style = space
indent_size = 2
charset = utf-8
end_of_line = lf
trim_trailing_whitespace = true
insert_final_newline = true
```

### 代码规范

- 请遵守代码规范，保持代码的一致性和可读性
- 请在代码生成过程中，注意代码的质量和性能
- 避免使用过多的魔法数字或字符串
- 生成的代码，不要有行尾空格；特别是一个空行不应该有任何字符

#### 代码结构

- 请保持代码结构的清晰和简洁
- 避免过长的函数或类
- 合理使用缩进和空格
- 避免使用过多的嵌套和条件语句
- 避免使用全局变量

#### 命名规范

- 使用小驼峰（`camelCase`）命名变量、函数
- 使用大驼峰（`PascalCase`）命名组件（React）或结构体（Go）
- 避免拼音命名，必须要中文才能表达清楚的，用注释
- 所有缩写统一风格，如用 `HTML` 不用 `Html`

#### 注释风格

- 中文项目注释统一用**中文**
- Go 中结构体字段、导出函数必须用完整句子的注释
- React 组件中注释推荐使用 `//` 行注释，注重说明“为什么”而不是“做什么”
- 注释清晰易懂，解释代码的意图和作用
