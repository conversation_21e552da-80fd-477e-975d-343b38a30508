---
description:
---


everytime you make an output please start it with "hello, cyberyoung"


# AI 开发规则
下面是一套开发规则（rules），适用于 **同时开发 Go 和 React 项目** 的中文程序员身份。这些规则旨在帮助统一编码风格、提高效率、减少低级错误，也方便团队协作。

---

## 基本原则
* 除非不得已（比如名词准确性需要，或者需要和其他语言的开发人员合作），请主要使用中文和我交流
* 在实际编写代码之前，请永远先列出方案和计划，以便审核。方案中给出伪代码即可


1. 保持礼貌和专业
* 使用恰当的语言和礼貌用语
* 保持专业的交流态度
* 避免使用不当或冒犯性的语言

2. 清晰的沟通
* 提供明确和具体的问题描述
* 使用准确的技术术语
* 如有需要请提供相关的代码示例或错误信息

3. 遵守道德准则
* 不要要求生成有害或违法的内容
* 尊重知识产权和版权
* 不分享敏感或私人信息

4. 最佳实践
* 保存重要对话内容
* 定期检查更新的功能和规则
* 合理使用系统资源

5. 经验总结
* 每次的chat都是一个独立的实现，可能是一个业务需求，也可能是一个技术方案，或者其它。所以这其中的对话内容非常重要，代表了开发过程，和其中经历的一些经验和错误。需要记录下来。
* 这其中，重要的开发内容，比如通用组件/库/类，需要同时生成对应的文档，这个文档可以放到源码同级目录下
* 在chat的最后，也就是任务完成之后，需要写一篇markdown格式的开发日志，放置于`docs/dev-log`目录下，名称为`${日期}-${任务名称}.md`
* 开发日志里应记录开始和结束事件，来回溯本次任务的时长；还有每次子任务的时长，以及其中的错误和异常。这样可以分析出到底哪些部分是耗时最长的，哪些部分是出错最多的。
* 特别的，每次的chat中，我的完整prompt列表也需要记录下来，作为开发日志的最后部分。
* 同时，也应该将本次chat的所有内容导出，并生成文档放到相同目录下，名称为`${日期}-${任务名称}-detail.md`

---

## 💻 通用代码规则（Go & React 都适用）

### 架构设计

#### 内容
* 请首先提供完整的代码生成方案，明确写出详细的计划（最好先用列表的形式进行总结）
* 请提供清晰的架构设计方案, 说明改动的模块划分和功能实现
* 请说明使用的技术栈和框架
* 请说明使用的设计模式和最佳实践
* 请说明生成代码的用法和示例
* 将以上内容记录并生成相关的设计文档或原型

### 原则
* 在设计过程中，请参考整个项目的代码，尽量复用已有的代码，比如参考已有的组件、库、类以及类似实现。
* 譬如参考React项目的package.json文件中的依赖库，或者Go项目的go.mod文件中的依赖库，或者Python项目的requirements.txt文件中的依赖库，或者Java项目的pom.xml文件中的依赖库。
* 新生成的文件，请根据项目之前的惯例，放置于对应的目录下。
* 修改的时候，保持最小化，尤其是已有的底层组件/库/类，如无必要，不要修改；确实需要修改的话，请保持兼容性，不要破坏原有功能
* 文件的修改，不要直接覆盖之前的内容，而是在之前的内容后面添加。

### 编辑器设置（建议放入 `.editorconfig` 或 编辑器 配置）

```ini
root = true

[*]
indent_style = space
indent_size = 2
charset = utf-8
end_of_line = lf
trim_trailing_whitespace = true
insert_final_newline = true
```

### 代码规范
* 请遵守代码规范，保持代码的一致性和可读性
* 请在代码生成过程中，注意代码的质量和性能
* 避免使用过多的魔法数字或字符串
* 生成的代码，不要有行尾空格；特别是一个空行不应该有任何字符

#### 代码结构
* 请保持代码结构的清晰和简洁
* 避免过长的函数或类
* 合理使用缩进和空格
* 避免使用过多的嵌套和条件语句
* 避免使用全局变量

#### 命名规范

* 使用小驼峰（`camelCase`）命名变量、函数
* 使用大驼峰（`PascalCase`）命名组件（React）或结构体（Go）
* 避免拼音命名，必须要中文才能表达清楚的，用注释
* 所有缩写统一风格，如用 `HTML` 不用 `Html`

#### 注释风格

* 中文项目注释统一用**中文**
* Go 中结构体字段、导出函数必须用完整句子的注释
* React 组件中注释推荐使用 `//` 行注释，注重说明“为什么”而不是“做什么”
* 注释清晰易懂，解释代码的意图和作用

---

# ⚛️ React + TypeScript 开发规范

> 基于 TypeScript + Hooks 的现代 React 开发最佳实践

## 🎯 核心原则

### 技术栈要求

我们是以下技术的专家：**Solidity, TypeScript, Node.js, Next.js 14 App Router, React, Vite, Viem v2, Wagmi v2, Semi UI, Shadcn UI, Radix UI, Tailwind Aria**

- **语言**: TypeScript (严禁使用 any)
- **框架**: React 18+ with Hooks
- **构建工具**: Vite
- **样式系统**: Tailwind CSS + Semi UI + Tailwind Aria
- **状态管理**: Jotai (原子化状态管理)
- **表单处理**: React Hook Form + Zod
- **动画库**: Framer Motion
- **Web3**: Viem v2, Wagmi v2 (如适用)
- **区块链**: Solidity (如适用)

### 开发理念

- **函数式编程**: 优先使用函数式组件，避免类组件
- **声明式编程**: 使用声明式 JSX，避免命令式 DOM 操作
- **模块化设计**: 组件职责单一，易于复用和测试
- **类型安全**: 所有代码必须有明确的类型定义
- **早期返回**: 优先处理错误和边界情况
- **RORO 模式**: 使用 Receive an Object, Return an Object 模式
- **RSC 优先**: 最小化 'use client', 'useEffect', 'setState'，优先使用 RSC
- **命名导出**: 优先使用命名导出而非默认导出
- **迭代优化**: 优先考虑迭代和模块化而非重复
- **描述性命名**: 使用带助动词的描述性变量名
- **简洁技术响应**: 编写简洁、技术性的响应，提供准确的 TypeScript 示例

### 开发协作规范

**⚠️ 重要：Linter 错误处理规范**
- 按用户要求去改代码，严格遵循用户的具体指示
- 遇到 linter 错误时，提出来让用户选择是否修改
- **绝对禁止**擅自修改 linter 错误，必须先征得用户同意
- 超过3次同一文件的 linter 错误修复循环时，停止并向用户汇报情况

---

## 📁 项目结构规范

### 目录命名规则

```bash
# 目录命名使用 kebab-case
components/auth-wizard/
pages/user-management/

# 组件目录使用 PascalCase
UserCard/
DataTable/
```

### 推荐文件结构

```
src/
├── components/           # 通用组件
│   └── UserCard/
│       ├── index.tsx    # 导出文件
│       ├── UserCard.tsx # 主组件
│       └── types.ts     # 类型定义
├── pages/               # 页面组件
├── api/                 # API 接口封装
├── atoms/               # Jotai 状态原子
├── hooks/               # 自定义 Hooks
├── utils/               # 工具函数
└── types/               # 全局类型定义
```

---

## 🏗️ TypeScript 代码规范

### 类型定义规则

```typescript
// ✅ 优先使用 interface
interface UserProps {
  id: string;
  name: string;
  isActive: boolean;
}

// ✅ 使用对象映射替代 enum
const USER_STATUS = {
  ACTIVE: "active",
  INACTIVE: "inactive",
  PENDING: "pending",
} as const;

type UserStatus = (typeof USER_STATUS)[keyof typeof USER_STATUS];

// ❌ 避免使用 any
function handleData(data: any) {
  /* 错误 */
}

// ✅ 明确类型定义
function handleData(data: User[]): ProcessedData {
  return data.map(processUser);
}
```

### 命名规范

| 类型      | 规则             | 示例                           |
| --------- | ---------------- | ------------------------------ |
| 组件      | PascalCase       | `UserCard`, `DataTable`        |
| 变量/函数 | camelCase        | `userName`, `handleClick`      |
| 常量      | UPPER_SNAKE_CASE | `API_BASE_URL`                 |
| 布尔值    | 助动词前缀       | `isLoading`, `hasError`        |
| 事件处理  | handle 前缀      | `handleSubmit`, `handleChange` |

### 接口类型管理规范

#### 避免重复定义问题

开发过程中容易在多个文件中重复定义相同的 TypeScript 接口，导致类型冲突。

#### 解决方案

```typescript
// ✅ 正确：在功能组件中定义并导出
// src/components/detail/FilterToolbar.tsx
export interface FilterColumn {
  field: string;
  label: string;
  component: string;
  props?: any;
}

// src/components/detail/ExportToolbar.tsx  
export interface ExportConfig {
  filename: string;
  formats: string[];
  columns: string[];
}

// ✅ 其他文件导入使用
import { FilterColumn } from "./FilterToolbar";
import { ExportConfig } from "./ExportToolbar";

// ❌ 错误：重复定义
interface FilterColumn { ... }  // 在 sideDetail.tsx 中
interface FilterColumn { ... }  // 在 renderSideTabPane.tsx 中
```

#### 最佳实践

- 接口定义遵循"谁使用谁定义"原则
- 核心接口在对应的功能组件中定义并导出
- 其他文件统一导入，避免重复定义
- 使用 TypeScript 命名空间避免命名冲突

---

## ⚛️ React 组件规范

### 标准组件模板

```typescript
// 1. 导入声明
import { useState, useCallback } from "react";
import { Button } from "@douyinfe/semi-ui";

// 2. 类型定义
interface UserCardProps {
  user: User;
  onEdit?: (user: User) => void;
  className?: string;
}

// 3. 组件实现
const UserCard = ({ user, onEdit, className }: UserCardProps) => {
  // 4. 状态管理
  const [isEditing, setIsEditing] = useState(false);

  // 5. 事件处理
  const handleEdit = useCallback(() => {
    setIsEditing(true);
    onEdit?.(user);
  }, [user, onEdit]);

  // 6. 早期返回处理边界情况
  if (!user) return null;

  // 7. 主渲染逻辑
  return (
    <div className={`p-4 border rounded-lg ${className}`}>
      <h3 className="text-lg font-medium">{user.name}</h3>
      <p className="text-gray-600">{user.email}</p>

      {isEditing ? (
        <div>编辑模式</div>
      ) : (
        <Button onClick={handleEdit} variant="outline">
          编辑用户
        </Button>
      )}
    </div>
  );
};

// 8. 导出
export { UserCard };
export type { UserCardProps };
```

### 函数定义规范

```typescript
// ✅ 组件使用 const 和箭头函数，省略分号
const UserList = ({ users }: UserListProps) => {
  return <div>{/* 组件内容 */}</div>;
};

// ✅ 工具函数优先使用 function，省略分号
function calculateTotal(items: Item[]): number {
  return items.reduce((sum, item) => sum + item.price, 0);
}

// ✅ 简单情况（< 3 条指令）可使用箭头函数，省略分号
const formatCurrency = (amount: number) => `¥${amount.toFixed(2)}`;
const isValidEmail = (email: string) =>
  /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);

// ✅ 复杂逻辑使用命名函数，省略分号
function processComplexData(data: ComplexData[]): ProcessedData[] {
  const filtered = data.filter((item) => item.isValid);
  const mapped = filtered.map((item) => transformItem(item));
  return mapped.sort((a, b) => a.priority - b.priority);
}

// ❌ 避免 function 声明组件
function UserList({ users }: UserListProps) {
  // 不推荐
}
```

### 条件语句规范

```typescript
// ✅ 单行条件语句省略大括号，省略分号
if (condition) doSomething();

// ✅ 避免不必要的 else 语句，使用早期返回
function processUser(user: User) {
  if (!user) return null;
  if (!user.isActive) return null;

  // 主逻辑
  return processActiveUser(user);
}

// ✅ 使用守卫子句处理前置条件
function validateInput(input: string) {
  if (!input) throw new Error("输入不能为空");
  if (input.length < 3) throw new Error("输入太短");

  return input.trim();
}
```

### 文件结构规范

每个文件应按以下顺序组织：

1. 导入语句
2. 导出的组件
3. 子组件
4. 工具函数
5. 静态内容（将静态内容变量放在渲染函数外部）
6. 类型定义（将静态内容和接口放在文件末尾）
7. 导出语句（统一导出）

```typescript
// ✅ 正确的文件结构示例
import { useState } from "react";
import { Button } from "@/components/ui/button";

// 2. 导出的组件
const UserDashboard = ({ users }: UserDashboardProps) => {
  const [filter, setFilter] = useState("");

  return (
    <div>
      {FILTER_OPTIONS.map((option) => (
        <FilterButton key={option.value} {...option} />
      ))}
      {users
        .filter((u) => u.name.includes(filter))
        .map((user) => (
          <UserCard key={user.id} user={user} />
        ))}
    </div>
  );
};

// 3. 子组件
const FilterButton = ({ label, value, onClick }: FilterButtonProps) => {
  return <Button onClick={() => onClick(value)}>{label}</Button>;
};

// 4. 工具函数
function filterUsers(users: User[], filter: string): User[] {
  return users.filter((user) =>
    user.name.toLowerCase().includes(filter.toLowerCase())
  );
}

// 5. 静态内容（放在文件末尾）
const FILTER_OPTIONS = [
  { label: "全部", value: "" },
  { label: "活跃", value: "active" },
  { label: "非活跃", value: "inactive" },
] as const;

// 6. 类型定义（放在文件末尾）
interface UserDashboardProps {
  users: User[];
}

interface FilterButtonProps {
  label: string;
  value: string;
  onClick: (value: string) => void;
}

// 7. 导出语句（统一导出）
export { UserDashboard, FilterButton };
export type { UserDashboardProps, FilterButtonProps };
```

### 函数逻辑规范

```typescript
// ✅ 保持函数简短且单一职责（< 20 行）
function calculateUserScore(user: User): number {
  if (!user.activities) return 0;

  return user.activities
    .filter((activity) => activity.isCompleted)
    .reduce((total, activity) => total + activity.points, 0);
}

// ✅ 使用高阶函数简化逻辑
function processUserData(users: User[]) {
  return users
    .filter((user) => user.isActive)
    .map((user) => ({
      ...user,
      displayName: formatUserName(user),
      score: calculateUserScore(user),
    }))
    .sort((a, b) => b.score - a.score);
}

// ✅ 使用默认参数而非 null/undefined 检查
function createUser(name: string, options: UserOptions = {}) {
  const { isActive = true, role = "user", permissions = [] } = options;

  return {
    name,
    isActive,
    role,
    permissions,
  };
}

// ✅ 提取逻辑到工具函数，避免深度嵌套
function validateUserForm(formData: UserFormData) {
  if (!isValidEmail(formData.email)) {
    return { isValid: false, error: "邮箱格式无效" };
  }

  if (!isValidPassword(formData.password)) {
    return { isValid: false, error: "密码强度不足" };
  }

  return { isValid: true };
}

function isValidEmail(email: string): boolean {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

function isValidPassword(password: string): boolean {
  return (
    password.length >= 8 && /[A-Z]/.test(password) && /[0-9]/.test(password)
  );
}
```

### 数据处理规范

```typescript
// ✅ 避免过度使用原始类型，封装为复合类型
interface User {
  readonly id: string;
  readonly name: string;
  readonly email: string;
  readonly createdAt: Date;
  readonly permissions: readonly Permission[];
}

interface Permission {
  readonly action: string;
  readonly resource: string;
}

// ✅ 使用 readonly 确保不可变性
type UserConfig = {
  readonly theme: "light" | "dark";
  readonly language: "zh" | "en";
  readonly notifications: readonly NotificationSettings[];
};

// ✅ 使用 as const 确保字面量类型
const USER_ROLES = ["admin", "user", "guest"] as const;
type UserRole = (typeof USER_ROLES)[number];

const API_ENDPOINTS = {
  USERS: "/api/users",
  POSTS: "/api/posts",
  COMMENTS: "/api/comments",
} as const;

// ✅ 避免在函数中进行验证，使用类进行内部验证
class UserEmail {
  private readonly value: string;

  constructor(email: string) {
    if (!this.isValid(email)) {
      throw new Error("Invalid email format");
    }
    this.value = email;
  }

  private isValid(email: string): boolean {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  }

  toString(): string {
    return this.value;
  }

  getDomain(): string {
    return this.value.split("@")[1];
  }
}

// ✅ 使用类型化的数据创建函数
function createUser(userData: {
  name: string;
  email: string;
  role: UserRole;
}): User {
  return {
    id: generateId(),
    name: userData.name,
    email: userData.email,
    createdAt: new Date(),
    permissions: getPermissionsByRole(userData.role),
  };
}

// ✅ 优先使用不可变数据操作
function updateUserPermissions(
  user: User,
  newPermissions: readonly Permission[]
): User {
  return {
    ...user,
    permissions: [...newPermissions],
  };
}
```

---

## 🎨 样式开发规范

### CSS 框架使用

- **主样式**: Tailwind CSS（禁止内联样式）
- **UI 组件库**: Semi UI（不混用其他库）
- **设计原则**: Mobile-first 响应式设计

### 样式编写规范

```typescript
// ✅ 推荐：使用 Tailwind 类名
const Alert = ({ type, children }: AlertProps) => {
  const baseStyles = "p-4 rounded-md border";
  const typeStyles = {
    success: "bg-green-50 border-green-200 text-green-800",
    error: "bg-red-50 border-red-200 text-red-800",
    warning: "bg-yellow-50 border-yellow-200 text-yellow-800",
  };

  return <div className={`${baseStyles} ${typeStyles[type]}`}>{children}</div>;
};

// ❌ 禁止：内联样式
function Alert({ children }) {
  return (
    <div style={{ padding: "16px", backgroundColor: "red" }}>{children}</div>
  );
}
```

### 高级 React 模式

```typescript
// ✅ 使用 Suspense 包装客户端组件
function App() {
  return (
    <Suspense fallback={<Loading />}>
      <DynamicComponent />
    </Suspense>
  );
}

// ✅ 动态加载非关键组件
const HeavyComponent = lazy(() => import("./HeavyComponent"));

// ✅ 使用 useActionState 处理表单状态
function ContactForm() {
  const [state, formAction] = useActionState(submitForm, null);

  return (
    <form action={formAction}>
      {state?.error && <div className="error">{state.error}</div>}
      <input name="email" type="email" required />
      <button type="submit">提交</button>
    </form>
  );
}

// ✅ 将静态内容变量放在渲染函数外
const FORM_FIELDS = [
  { name: "email", type: "email", label: "邮箱" },
  { name: "name", type: "text", label: "姓名" },
] as const;

function UserForm() {
  return (
    <form>
      {FORM_FIELDS.map((field) => (
        <FormField key={field.name} {...field} />
      ))}
    </form>
  );
}
```

### 响应式设计

```typescript
// ✅ Mobile-first 响应式设计
const ResponsiveGrid = ({ children }: GridProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {children}
    </div>
  );
};
```

---

## 🔄 状态管理规范

### 状态管理策略

```typescript
// 1. 组件内部状态 - useState
function UserForm() {
  const [formData, setFormData] = useState<UserFormData>({
    name: "",
    email: "",
  });

  return <form>{/* 表单内容 */}</form>;
}

// 2. 全局状态 - Jotai atoms
// atoms/user.ts
import { atom } from "jotai";

export const currentUserAtom = atom<User | null>(null);
export const userListAtom = atom<User[]>([]);

// 使用 atoms
function UserProfile() {
  const [currentUser, setCurrentUser] = useAtom(currentUserAtom);

  return <div>{currentUser?.name}</div>;
}

// 3. 服务器状态 - TanStack Query
function useUsers() {
  return useQuery({
    queryKey: ["users"],
    queryFn: fetchUsers,
    staleTime: 5 * 60 * 1000, // 5 分钟
  });
}
```

### 表单处理规范

```typescript
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// 1. 定义验证 schema
const userSchema = z.object({
  name: z.string().min(2, "姓名至少2个字符").trim(),
  email: z.string().email("请输入有效邮箱").trim(),
  age: z.number().min(18, "年龄必须大于18岁"),
});

type UserFormData = z.infer<typeof userSchema>;

// 2. 表单组件
const UserForm = ({ onSubmit }: UserFormProps) => {
  const form = useForm<UserFormData>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: "",
      email: "",
      age: 18,
    },
  });

  const handleSubmit = form.handleSubmit(async (data) => {
    try {
      await onSubmit(data);
      form.reset();
    } catch (error) {
      console.error("提交失败:", error);
    }
  });

  return (
    <form onSubmit={handleSubmit}>
      <div>
        <label htmlFor="name">姓名</label>
        <input
          {...form.register("name")}
          className="border rounded px-2 py-1"
        />
        {form.formState.errors.name && (
          <span className="text-red-500">
            {form.formState.errors.name.message}
          </span>
        )}
      </div>

      <button type="submit" disabled={form.formState.isSubmitting}>
        {form.formState.isSubmitting ? "提交中..." : "提交"}
      </button>
    </form>
  );
};
```

- **过滤器组件状态管理**: 过滤器组件应当自主管理其内部状态，并在组件卸载（unmount）时自动重置（例如在 useEffect 的 cleanup 中重置 Jotai atom 或本地 state），以防状态在不同记录或页面之间泄漏。

---

## 🌐 API 集成规范

### 目录结构和文件规范

```
src/api/
├── index.ts              # 统一导出文件
├── request.ts            # 请求封装（建议改为 .ts）
├── contractor.ts         # 承包商相关 API（建议改为 .ts）
├── contractor_training.ts # 培训相关 API（建议改为 .ts）
└── types.ts              # 共享类型定义
```

**重要规则**：

- **文件扩展名**: API 文件应使用 `.ts` 扩展名（目前使用 `.tsx` 需要优化）
- **业务分离**: 按业务模块分文件组织，避免单个文件过大
- **统一导出**: 从 `index.ts` 统一导出，便于管理

### 请求封装优化

基于现有的 `request.tsx`，建议的改进方向：

```typescript
// api/request.ts (建议重命名)
import { api_url } from "@/config";
import { authNameInLocalStorage } from "@/constants";

interface RequestOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE";
  body?: object;
  noAuth?: boolean;
}

interface ApiResponse<T = any> {
  code: number;
  message?: string;
  data?: T;
}

// ✅ 改进：添加类型泛型支持
export const get = <T = any>(
  url: string,
  noAuth?: boolean
): Promise<ApiResponse<T>> => request<T>(url, { noAuth });

export const post = <T = any>(
  url: string,
  body?: object,
  noAuth?: boolean
): Promise<ApiResponse<T>> => request<T>(url, { method: "POST", body, noAuth });

export const put = <T = any>(
  url: string,
  body?: object,
  noAuth?: boolean
): Promise<ApiResponse<T>> => request<T>(url, { method: "PUT", body, noAuth });

export const del = <T = any>(
  url: string,
  body?: object,
  noAuth?: boolean
): Promise<ApiResponse<T>> =>
  request<T>(url, { method: "DELETE", body, noAuth });
```

### 类型定义规范

结合现有代码和最佳实践：

```typescript
// api/types.ts
import { z } from "zod";

// ✅ 基础类型定义
export interface LoginParams {
  username: string;
  password: string;
  captchaId: string;
  captchaCode: string;
}

export interface Contractor {
  id: string;
  name: string;
  status: number;
  createdAt: string;
  updatedAt: string;
}

// ✅ 使用 Zod 进行数据验证
export const ContractorSchema = z.object({
  id: z.string(),
  name: z.string(),
  status: z.number(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

export type ContractorValidated = z.infer<typeof ContractorSchema>;

// ✅ 参数结构规范化
export interface UpdateContractorParams {
  id: string;
  data: Partial<Omit<Contractor, "id" | "createdAt">>;
}

export interface CreateEmployeeParams {
  contractorId: string;
  employee: {
    name: string;
    idCard: string;
    phone: string;
    position: string;
  };
}
```

### API 函数规范化

基于现有代码，标准化改进：

```typescript
// api/contractor.ts
import { get, post, put } from "./request";
import { formatRFC3339 } from "@/utils/time";
import type {
  LoginParams,
  Contractor,
  UpdateContractorParams,
  CreateEmployeeParams,
} from "./types";

// ✅ 登录相关 - 添加类型安全
export const postContractorUserLogin = async (params: LoginParams) => {
  try {
    const res = await post<{ token: string; user: Contractor }>(
      "/contractor_user/coperate_training/login",
      params
    );

    if (res.code !== 200) {
      throw new Error(res.message || "登录失败");
    }

    return res.data;
  } catch (error) {
    // 统一错误处理，抛出用户友好错误
    if (error.response?.status === 401) {
      throw new Error("用户名或密码错误");
    }
    if (error.response?.status === 429) {
      throw new Error("请求过于频繁，请稍后重试");
    }
    throw new Error("登录失败，请检查网络连接");
  }
};

// ✅ CRUD 操作 - 改进参数结构
export const getContractor = async (id: string): Promise<Contractor> => {
  try {
    const res = await get<Contractor>(
      `/contractor_user/basic_info_management/contractor/${id}`
    );

    if (res.code !== 200) {
      throw new Error(res.message || "获取承包商信息失败");
    }

    // ✅ 时间格式化处理
    return {
      ...res.data,
      createdAt: formatRFC3339(res.data.createdAt),
      updatedAt: formatRFC3339(res.data.updatedAt),
    };
  } catch (error) {
    if (error.response?.status === 404) {
      throw new Error("承包商信息不存在");
    }
    throw new Error("获取承包商信息失败");
  }
};

// ✅ 参数结构规范化
export const updateContractor = async ({
  id,
  data,
}: UpdateContractorParams) => {
  try {
    const res = await put(
      `/contractor_user/basic_info_management/contractor/${id}`,
      data
    );

    if (res.code !== 200) {
      throw new Error(res.message || "更新承包商信息失败");
    }

    return res.data;
  } catch (error) {
    if (error.response?.status === 403) {
      throw new Error("没有权限修改此承包商信息");
    }
    throw new Error("更新承包商信息失败");
  }
};

// ❌ 当前代码问题：参数结构不清晰
// export const postContractorEmployeeList = async (params) => {
//   const res = await post(
//     `/contractor_user/basic_info_management/contractor/${params?.id}/contractor_employee`,
//     params.values
//   )
//   return res
// }

// ✅ 改进：清晰的参数结构
export const createContractorEmployee = async ({
  contractorId,
  employee,
}: CreateEmployeeParams) => {
  try {
    const res = await post(
      `/contractor_user/basic_info_management/contractor/${contractorId}/contractor_employee`,
      employee
    );

    if (res.code !== 200) {
      throw new Error(res.message || "创建员工信息失败");
    }

    return res.data;
  } catch (error) {
    if (error.response?.status === 409) {
      throw new Error("员工信息已存在");
    }
    throw new Error("创建员工信息失败");
  }
};
```

### 数据验证和错误处理

```typescript
// ✅ 结合 Zod 验证和错误处理
import { ContractorSchema } from "./types";

export const getContractorWithValidation = async (id: string) => {
  try {
    const res = await get<Contractor>(
      `/contractor_user/basic_info_management/contractor/${id}`
    );

    if (res.code !== 200) {
      throw new Error(res.message || "获取数据失败");
    }

    // ✅ 数据验证
    const validatedData = ContractorSchema.parse(res.data);

    return {
      ...validatedData,
      createdAt: formatRFC3339(validatedData.createdAt),
      updatedAt: formatRFC3339(validatedData.updatedAt),
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new Error("数据格式错误，请联系技术支持");
    }
    throw error;
  }
};
```

### 组件中的使用

```typescript
// ✅ 组件中使用 TanStack Query
const ContractorDetail = ({ contractorId }: { contractorId: string }) => {
  const {
    data: contractor,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["contractor", contractorId],
    queryFn: () => getContractor(contractorId),
    retry: 2,
    retryDelay: 1000,
    // ✅ 错误必须能被捕获和显示
    onError: (error) => {
      toast.error(error.message);
    },
  });

  if (error) {
    return (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <p className="text-red-600">错误: {error.message}</p>
      </div>
    );
  }

  if (isLoading) {
    return <div className="animate-pulse">加载中...</div>;
  }

  return <div>{/* 承包商详情 */}</div>;
};
```

### 统一导出规范

```typescript
// api/index.ts
// ✅ 统一导出，避免命名冲突
export * from "./types";

// 选择性导出，便于管理
export {
  postContractorUserLogin,
  postBasicInformationManagementLogin,
  getContractor,
  updateContractor,
  createContractorEmployee,
} from "./contractor";

export {
  postJoin as joinTraining,
  getTrainingRecord,
  postCourseView,
  getEntryInfo,
} from "./contractor_training";
```

### Toast 错误展示方案

#### 1. 全局错误处理设置

```typescript
// hooks/useGlobalErrorHandler.ts
import { Toast } from "@douyinfe/semi-ui";
import { useEffect } from "react";

export const useGlobalErrorHandler = () => {
  useEffect(() => {
    // 监听全局错误事件
    const handleGlobalError = (event: CustomEvent<string>) => {
      Toast.error({
        content: event.detail,
        duration: 5,
        showClose: true,
      });
    };

    window.addEventListener("api-error" as any, handleGlobalError);
    return () =>
      window.removeEventListener("api-error" as any, handleGlobalError);
  }, []);
};

// 全局错误触发函数
export const triggerGlobalError = (message: string) => {
  const event = new CustomEvent("api-error", { detail: message });
  window.dispatchEvent(event);
};
```

#### 2. 在根组件中启用

基于项目的 `main.tsx`，只需要添加全局错误处理：

```typescript
// main.tsx - 修改现有结构
import { useGlobalErrorHandler } from "@/hooks/useGlobalErrorHandler";
// ... 其他导入

// ✅ 创建一个包装组件来处理全局错误
const AppWithErrorHandler = () => {
  useGlobalErrorHandler();

  return (
    <QueryClientProvider>
      <AuthProvider store={store}>
        <RouterProvider router={router} />
      </AuthProvider>
    </QueryClientProvider>
  );
};

ReactDOM.createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <AppWithErrorHandler />
  </React.StrictMode>
);

// 或者在布局组件中启用（推荐方式）
// layout/index.tsx (LayoutBox)
import { useGlobalErrorHandler } from "@/hooks/useGlobalErrorHandler";
import { Outlet } from "react-router-dom";

export const LayoutBox = () => {
  // ✅ 在培训系统布局中启用错误处理
  useGlobalErrorHandler();

  return (
    <div className="min-h-screen bg-gray-50">
      <Outlet />
    </div>
  );
};

// layout/layout_contractorinfo_box.tsx
export const LayoutContractorInfoBox = () => {
  // ✅ 在承包商信息布局中也启用错误处理
  useGlobalErrorHandler();

  return (
    <div className="min-h-screen bg-white">
      <Outlet />
    </div>
  );
};
```

#### 3. API 层错误处理优化

```typescript
// api/request.ts
import { triggerGlobalError } from "@/hooks/useGlobalErrorHandler";

async function request<T>(
  url: string,
  options: RequestOptions = {}
): Promise<ApiResponse<T>> {
  try {
    const response = await fetch(`${api_url}${url}`, {
      method: options.method || "GET",
      headers: getHeaders(),
      body: options.body ? JSON.stringify(options.body) : undefined,
    });

    const data = await response.json();

    // ✅ 统一状态码错误处理
    if (response.status === 401) {
      const pathSegments = window.location.pathname.split("/");
      localStorage.removeItem("_auth");

      if (pathSegments.includes("contractor")) {
        window.location.replace("/contractor_info");
      } else {
        window.location.replace("/contractor_training");
      }
      return data;
    }

    // ✅ 业务错误处理
    if (data.code !== 200) {
      const errorMessage = data.message || "操作失败，请稍后重试";
      triggerGlobalError(errorMessage);
      throw new Error(errorMessage);
    }

    return data;
  } catch (error) {
    // ✅ 网络错误处理
    if (error instanceof TypeError) {
      const networkError = "网络连接失败，请检查网络";
      triggerGlobalError(networkError);
      throw new Error(networkError);
    }

    // 重新抛出已处理的错误
    throw error;
  }
}
```

#### 4. 组件层的三种错误处理方式

```typescript
// ✅ 方式1：数据展示页面 - TanStack Query 自动处理（推荐）
// 基于 ContractorBasicPage 的实际使用
const ContractorBasicPage = () => {
  const {
    data: contractor,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["contractor", "basic"],
    queryFn: () => getContractor("current"),
    retry: 2,
    retryDelay: 1000,
    // API 层已经触发了 toast，这里可以不重复处理
  });

  // 可以选择显示错误状态，或者完全依赖 toast
  if (error) {
    return (
      <div className="p-4 text-center text-gray-500">
        <p>承包商信息加载失败，请稍后重试</p>
        <Button
          onClick={() => window.location.reload()}
          variant="outline"
          className="mt-2"
        >
          刷新页面
        </Button>
      </div>
    );
  }

  if (isLoading) return <div className="animate-pulse">加载中...</div>;

  return (
    <div className="p-6">
      <h1 className="text-2xl font-bold mb-4">承包商基本信息</h1>
      {/* 承包商信息展示 */}
    </div>
  );
};

// ✅ 方式2：培训列表页面 - 基于 ListPage
const ListPage = () => {
  const { id } = useParams();
  const {
    data: trainingList,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["training", "list", id],
    queryFn: () => getTrainingList(id),
    enabled: !!id,
  });

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-gray-600">培训列表加载失败</p>
        <Button onClick={() => window.location.reload()} variant="outline">
          重新加载
        </Button>
      </div>
    );
  }

  if (isLoading) return <div className="animate-pulse p-4">加载中...</div>;

  return (
    <div className="p-6">
      <h1 className="text-xl font-semibold mb-4">培训计划列表</h1>
      {/* 培训列表内容 */}
    </div>
  );
};

// ✅ 方式3：表单操作页面 - 手动错误处理
const ContractorEmployeePage = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAddEmployee = async (employeeData: CreateEmployeeData) => {
    setIsSubmitting(true);
    try {
      await createContractorEmployee({
        contractorId: "current",
        employee: employeeData,
      });

      // ✅ 成功提示
      Toast.success({
        content: "员工信息已成功添加",
        duration: 3,
      });

      // 刷新员工列表或其他操作
    } catch (error) {
      // ✅ API 层已经处理了错误 toast，这里可以做额外处理
      console.error("添加员工失败:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-xl font-semibold mb-4">员工管理</h1>
      <form onSubmit={handleAddEmployee}>
        {/* 表单内容 */}
        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? "添加中..." : "添加员工"}
        </Button>
      </form>
    </div>
  );
};

// ✅ 方式4：培训详情页面 - 基于 DetailPage 的复杂交互
const DetailPage = () => {
  const { id } = useParams();
  const [isJoining, setIsJoining] = useState(false);

  // 获取培训详情
  const {
    data: trainingDetail,
    error,
    isLoading,
  } = useQuery({
    queryKey: ["training", "detail", id],
    queryFn: () => getTrainingRecord("current", id),
    enabled: !!id,
  });

  // 加入培训的特殊错误处理
  const handleJoinTraining = async () => {
    setIsJoining(true);
    try {
      await postJoin({
        uid: "current",
        pid: id,
        values: {},
      });

      // ✅ 成功提示
      Toast.success({
        content: "您已成功加入此培训计划",
        duration: 3,
      });
    } catch (error) {
      // ✅ 覆盖默认错误消息，提供更具体的错误信息
      if (error.message.includes("已加入")) {
        Toast.info({
          content: "您已经加入了这个培训计划",
          duration: 3,
        });
      } else if (error.message.includes("名额已满")) {
        Toast.error({
          content: "培训名额已满，请选择其他培训计划",
          duration: 5,
          showClose: true,
        });
      }
      // 其他错误已由 API 层处理
    } finally {
      setIsJoining(false);
    }
  };

  if (error) {
    return (
      <div className="p-4 text-center">
        <p className="text-gray-600">培训详情加载失败</p>
        <Button onClick={() => window.history.back()} variant="outline">
          返回
        </Button>
      </div>
    );
  }

  if (isLoading) return <div className="animate-pulse p-4">加载中...</div>;

  return (
    <div className="p-6">
      <h1 className="text-xl font-semibold mb-4">培训详情</h1>
      {/* 培训详情内容 */}
      <Button onClick={handleJoinTraining} disabled={isJoining}>
        {isJoining ? "加入中..." : "加入培训"}
      </Button>
    </div>
  );
};

// ✅ 方式5：资质申请页面 - 基于 ContractorEntryApplyPage
const ContractorEntryApplyPage = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmitApplication = async (applicationData: EntryApplyData) => {
    setIsSubmitting(true);
    try {
      await createContractorEntryApply({ params: applicationData });

      // ✅ 详细的成功反馈
      Toast.success({
        content: "您的入场申请已提交，预计3个工作日内审核完成",
        duration: 8, // 重要信息延长显示时间
        showClose: true,
      });
    } catch (error) {
      // ✅ 根据错误类型提供不同处理
      if (error.message.includes("重复申请")) {
        Toast.error({
          content: "您已有待审核的申请，请勿重复提交",
          duration: 5,
          showClose: true,
        });
      }
      // 其他错误由 API 层统一处理
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <h1 className="text-xl font-semibold mb-4">入场申请</h1>
      {/* 申请表单 */}
      <Button onClick={handleSubmitApplication} disabled={isSubmitting}>
        {isSubmitting ? "提交中..." : "提交申请"}
      </Button>
    </div>
  );
};
```

#### 5. 特殊错误类型的定制化处理

```typescript
// api/contractor.ts
export const getContractor = async (id: string): Promise<Contractor> => {
  try {
    const res = await get<Contractor>(
      `/contractor_user/basic_info_management/contractor/${id}`
    );

    if (res.code !== 200) {
      // ✅ 根据错误码提供不同的提示
      let errorMessage = res.message || "获取承包商信息失败";

      switch (res.code) {
        case 404:
          errorMessage = "承包商信息不存在，可能已被删除";
          break;
        case 403:
          errorMessage = "没有权限查看此承包商信息";
          break;
        case 500:
          errorMessage = "服务器错误，请联系技术支持";
          break;
      }

      triggerGlobalError(errorMessage);
      throw new Error(errorMessage);
    }

    return {
      ...res.data,
      createdAt: formatRFC3339(res.data.createdAt),
      updatedAt: formatRFC3339(res.data.updatedAt),
    };
  } catch (error) {
    // 网络错误已在 request 层处理
    throw error;
  }
};
```

#### 6. 离线状态和网络恢复处理

```typescript
// hooks/useNetworkStatus.ts
import { useEffect, useState } from "react";
import { Toast } from "@douyinfe/semi-ui";

export const useNetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      Toast.success({
        content: "网络已恢复，您可以继续正常操作",
        duration: 3,
      });
    };

    const handleOffline = () => {
      setIsOnline(false);
      Toast.error({
        content: "网络连接断开，请检查网络连接",
        duration: 0, // 不自动消失
        showClose: true,
      });
    };

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);

  return isOnline;
};
```

### 网络请求核心规范

- **错误处理**: 错误必须能被 toast/modal 捕获并显示用户友好信息
- **Toast 展示**: 使用全局错误处理机制，API 层统一触发 toast 提示
- **请求封装**: 禁止直接使用 fetch，必须使用封装的 get/post/put/del 方法
- **时间格式化**: 使用 `formatRFC3339` 格式化时间数据，函数位于 `src/utils` 目录
- **类型安全**: 所有函数参数和返回值都要有明确的 TypeScript 类型
- **数据验证**: 优先使用 Zod 进行数据验证，确保类型安全
- **参数规范**: 使用对象解构，参数名清晰明确，避免 `params?.values` 这种不清晰的结构
- **TanStack Query**: API 层应抛出用户友好错误，便于 TanStack Query 捕获和处理
- **返回值建模**: 将预期错误作为返回值处理，避免在 Server Actions 中使用 try/catch
- **错误分层**: API 层处理通用错误，组件层处理特殊业务错误

---

## ⚡ 性能优化规范

### 组件性能优化

```typescript
// 1. 使用 React.memo 优化重渲染
const UserCard = React.memo(({ user, onEdit }: UserCardProps) => {
  return (
    <div>
      <h3>{user.name}</h3>
      <button onClick={() => onEdit(user)}>编辑</button>
    </div>
  );
});

// 2. 使用 useMemo 缓存计算结果
const UserStatistics = ({ users }: UserStatisticsProps) => {
  const statistics = useMemo(() => {
    return {
      total: users.length,
      active: users.filter((u) => u.isActive).length,
      inactive: users.filter((u) => !u.isActive).length,
    };
  }, [users]);

  return <div>{/* 显示统计信息 */}</div>;
};

// 3. 使用 useCallback 缓存事件处理函数
const UserList = ({ users, onUserUpdate }: UserListProps) => {
  const handleUserEdit = useCallback(
    (user: User) => {
      onUserUpdate(user.id, { ...user, updatedAt: new Date().toISOString() });
    },
    [onUserUpdate]
  );

  return (
    <div>
      {users.map((user) => (
        <UserCard key={user.id} user={user} onEdit={handleUserEdit} />
      ))}
    </div>
  );
};
```

### 代码分割和懒加载

```typescript
// 1. 路由级别懒加载（基于项目实际结构）
const MyTrainingPage = lazy(() => import("pages/training/myTrainingPage"));
const TrainingRecordPage = lazy(() => import("pages/training/recordPage"));
const TrainingCoursePage = lazy(() => import("pages/training/coursePage"));
const ContractorBasicPage = lazy(() => import("pages/basicInfo/contractorPage"));

// ✅ 基于项目实际的路由配置方式
const createBaseRouter = () => [
  {
    path: "/login",
    element: <LoginPage />,
    name: "登录",
  },
  {
    path: "/training",
    element: <TrainingLayoutPage />,
    children: [
      {
        index: true,
        element: <MyTrainingPage />,
        name: "我的培训",
      },
      {
        path: "record",
        element: <TrainingRecordPage />,
        name: "培训记录",
      },
      {
        path: "course/:id",
        element: <TrainingCoursePage />,
        name: "培训课程",
      },
    ],
  },
  {
    path: "/",
    element: <LayoutBox />,
    children: [
      {
        path: "contractor",
        element: <ContractorBasicPage />,
        name: "承包商基础信息",
      },
    ],
  },
  {
    path: "*",
    element: <NotFound />,
  },
];

// ✅ 在根组件中使用 RouterProvider（与 App.tsx 一致）
export default function App(): ReactElement {
  return (
    <Auth
      onLogin={(user) => {
        // 登录后的重定向逻辑
        let replacePath = lastPath.get() ? lastPath.get() : "/";
        window.location.replace(replacePath);
      }}
      defaultMenu={RoleRouter}
    >
      <RouterProvider router={createBrowserRouter(createBaseRouter())} />
    </Auth>
  );
}

// 2. 组件级别动态导入
const UserProfile = ({ userId }: UserProfileProps) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [AdvancedSettings, setAdvancedSettings] =
    useState<React.ComponentType | null>(null);

  useEffect(() => {
    if (showAdvanced && !AdvancedSettings) {
      import("./AdvancedSettings").then((module) => {
        setAdvancedSettings(() => module.AdvancedSettings);
      });
    }
  }, [showAdvanced, AdvancedSettings]);

  return (
    <div>
      <button onClick={() => setShowAdvanced(true)}>显示高级设置</button>
      {showAdvanced && AdvancedSettings && <AdvancedSettings />}
    </div>
  );
};
```

### 图像优化规范

```typescript
// ✅ 推荐：优化图像加载
interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  priority?: boolean;
}

const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  priority = false,
}: OptimizedImageProps) => {
  return (
    <Image
      src={src}
      alt={alt}
      width={width}
      height={height}
      format="webp"
      loading={priority ? "eager" : "lazy"}
      className="object-cover"
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    />
  );
};

// ✅ 图像数据预加载
const imageData = {
  width: 400,
  height: 300,
  blurDataURL: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ...",
} as const;

const ProductCard = ({ product }: ProductCardProps) => {
  return (
    <div className="card">
      <OptimizedImage
        src={product.imageUrl}
        alt={product.name}
        width={imageData.width}
        height={imageData.height}
        priority={product.featured}
      />
    </div>
  );
};
```

---

## 🛡️ 错误处理规范

### 错误边界实现

```typescript
// components/ErrorBoundary.tsx
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
}

const ErrorBoundary = ({
  children,
  fallback: Fallback,
}: ErrorBoundaryProps) => {
  const [state, setState] = useState<ErrorBoundaryState>({
    hasError: false,
    error: null,
  });

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      setState({
        hasError: true,
        error: new Error(event.message),
      });
    };

    window.addEventListener("error", handleError);
    return () => window.removeEventListener("error", handleError);
  }, []);

  const retry = useCallback(() => {
    setState({ hasError: false, error: null });
  }, []);

  if (state.hasError && state.error) {
    return Fallback ? (
      <Fallback error={state.error} retry={retry} />
    ) : (
      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
        <h3 className="text-red-800 font-medium">出现错误</h3>
        <p className="text-red-600">{state.error.message}</p>
        <button
          onClick={retry}
          className="mt-2 px-3 py-1 bg-red-600 text-white rounded"
        >
          重试
        </button>
      </div>
    );
  }

  return <>{children}</>;
};
```

### 类型守卫

```typescript
// utils/typeGuards.ts
export function isValidUser(data: unknown): data is User {
  return (
    typeof data === "object" &&
    data !== null &&
    "id" in data &&
    "name" in data &&
    "email" in data &&
    typeof (data as any).id === "string" &&
    typeof (data as any).name === "string" &&
    typeof (data as any).email === "string"
  );
}

export function isApiError(
  error: unknown
): error is { message: string; status?: number } {
  return (
    typeof error === "object" &&
    error !== null &&
    "message" in error &&
    typeof (error as any).message === "string"
  );
}

// 使用类型守卫
function handleApiResponse(response: unknown) {
  if (isValidUser(response)) {
    // TypeScript 现在知道 response 是 User 类型
    console.log(response.name);
  } else {
    throw new Error("无效的用户数据");
  }
}
```

---

## ⚙️ 开发工具配置

### VSCode/Cursor 配置

```jsonc
// .vscode/settings.json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll": true,
  },
  "go.useLanguageServer": true,
  "go.formatTool": "gofumpt",
  "go.lintTool": "golangci-lint",
  "typescript.validate.enable": true,
  "typescript.format.enable": true,
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "editor.rulers": [80, 120],
  "editor.wordWrap": "wordWrapColumn",
  "editor.wordWrapColumn": 80,
  "eslint.validate": ["typescript", "typescriptreact"],
  "prettier.requireConfig": true,
}
```

### Prettier 配置

```json
// .prettierrc
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "es5",
  "printWidth": 80,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

### ESLint 配置

```json
// .eslintrc.json
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended"
  ],
  "rules": {
    "@typescript-eslint/no-any": "error",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "react/prop-types": "off",
    "react/react-in-jsx-scope": "off"
  }
}
```

---

## 📋 开发检查清单

### ✅ 必须遵守的规则

- [ ] 所有变量和函数都有明确的 TypeScript 类型
- [ ] 组件使用 `const` 和箭头函数声明，省略分号
- [ ] 表单输入自动 trim 空格
- [ ] API 文件使用 `.ts` 扩展名，函数参数有明确类型定义
- [ ] API 调用包含统一错误处理，使用封装的 request 方法
- [ ] 时间数据使用 `formatRFC3339` 格式化
- [ ] 根组件启用全局错误处理和 Toaster 组件
- [ ] API 错误统一使用 toast 展示给用户
- [ ] 使用 Tailwind CSS，禁止内联样式
- [ ] 组件单一职责，函数长度 < 20 行
- [ ] 使用早期返回处理边界情况
- [ ] 函数内不留空行，保持代码紧凑
- [ ] 静态内容变量放在渲染函数外部
- [ ] 一个文件一个导出
- [ ] 过滤器组件在卸载时自动重置其内部状态
- [ ] 容器层如有业务上下文切换，也必须调用 `useRecordChangeReset`
- [ ] **加载态覆盖式处理**：出现 `isLoading` / `fetching` 等加载状态时，**严禁通过早期 `return` 卸载整棵子树**，尤其是表单、过滤器等带业务状态的组件。正确做法是在父容器加 `relative`，再渲染 `absolute inset-0` 遮罩式 `Spin/Loading`，保证子组件保持挂载；避免 `useEffect` cleanup 中的 `handleReset` / `resetAtom` 被误触发，导致首次请求丢失状态。 特别是包含「卸载自动重置」逻辑的组件（如 FilterToolbar）或携带表单状态的组件，必须遵守此规则。
- [ ] **卸载副作用评估**：如业务确需在加载态卸载子组件，必须**显式评估并处理卸载副作用**。例如：暂时禁用自动 `handleReset`，或在重新挂载后恢复表单值 / 过滤条件。未经过评估和补偿措施，禁止在加载态卸载组件，以免引入"第一次查询不生效"类缺陷。


### ❌ 绝对禁止的行为

- [ ] 使用 `any` 类型
- [ ] 在组件内直接调用 API
- [ ] 混用不同的 UI 组件库
- [ ] 忽略 TypeScript 编译错误
- [ ] 在组件内部写复杂业务逻辑
- [ ] 使用魔法数字或字符串

### 🔍 代码审查要点

- **可读性**: 代码是否易于理解
- **可维护性**: 修改是否会影响其他部分
- **可测试性**: 是否容易编写单元测试
- **性能影响**: 是否存在性能瓶颈
- **类型安全**: 类型定义是否完整
- **错误处理**: 是否覆盖各种情况
- **代码风格**: 是否保持统一

---

## 🚀 最佳实践建议

### 开发流程

1. **设计先行**: 先定义接口和类型，再实现逻辑
2. **注释优先**: 先写注释说明意图，再写实现代码
3. **测试思维**: 考虑各种边界情况和错误状态
4. **性能意识**: 避免不必要的重渲染和计算
5. **用户体验**: 提供加载状态和错误反馈

### 代码审查建议

- **可读性**: 代码是否易于理解
- **可维护性**: 修改是否会影响其他部分
- **可测试性**: 是否容易编写单元测试
- **性能影响**: 是否存在性能瓶颈
- **类型安全**: 类型定义是否完整

### 数据流思考

- **数据来源**: 数据从哪里获取？
- **数据传递**: 如何在组件间传递数据？
- **数据更新**: 数据变化如何触发 UI 更新？
- **状态管理**: 状态应该放在哪一层管理？

---

## 🔧 特定工作流程

### 目录开发规范

#### 在 components 目录工作时:

- 始终使用 Tailwind 进行样式设计
- 使用 Semi UI 组件库
- 使用 Framer Motion 添加动画效果
- 遵循组件命名规范

#### 在 API 目录工作时:

- 使用 `.ts` 文件扩展名，不使用 `.tsx`（除非包含 JSX）
- 所有函数参数和返回值都要有明确 TypeScript 类型定义
- 使用封装的 get/post/put/del 方法，禁止直接使用 fetch
- 按业务模块组织文件，统一从 index.ts 导出
- 参数使用对象解构，结构清晰明确，避免 `params?.values` 模式
- 统一错误处理，抛出用户友好错误信息，便于 TanStack Query 捕获
- 优先使用 Zod 进行数据验证，确保类型安全
- 时间数据使用 `formatRFC3339` 格式化

#### React 组件应遵循以下布局:

- Props 接口定义在顶部
- 组件作为命名导出
- 样式定义在底部

### 应用分析流程

当我要求分析应用时:

- 运行开发服务器 `npm run dev`
- 从控制台获取日志
- 提出性能改进建议

### 文档生成辅助

帮助我起草文档时:

- 提取代码注释
- 分析 README.md
- 生成 markdown 文档，文件和源码同级目录，名字和源码文件相同，后缀为.md

### JSDoc 规范

```typescript
/**
 * 用户卡片组件，用于展示用户基本信息
 * @param user - 用户数据对象
 * @param onEdit - 编辑用户时的回调函数
 * @param className - 额外的 CSS 类名
 * @returns 渲染的用户卡片 JSX 元素
 */
const UserCard = ({ user, onEdit, className }: UserCardProps): JSX.Element => {
  // ✅ 不在函数内留空行，保持代码紧凑
  const [isEditing, setIsEditing] = useState(false);
  const handleEdit = () => onEdit?.(user);
  if (!user) return null;
  return <div className={className}>{/* 内容 */}</div>;
};

// ❌ 错误示例：函数内有空行
function BadExample() {
  const data = getData();

  const processed = processData(data); // 这个空行是不必要的

  return <div>{processed}</div>;
}

/**
 * 计算用户总积分的工具函数
 * @param activities - 用户活动数组
 * @returns 总积分数值
 */
function calculateTotalPoints(activities: Activity[]): number {
  return activities.reduce((total, activity) => total + activity.points, 0);
}
```

### 通用代码风格

```typescript
// ✅ 避免魔法数字，定义常量
const MAX_RETRY_ATTEMPTS = 3;
const DEFAULT_PAGE_SIZE = 20;
const ANIMATION_DURATION = 300;

// ✅ 一个文件一个导出
export { UserCard };

// ✅ 使用 JSDoc 记录公共类和方法
export class UserService {
  /**
   * 获取用户列表
   * @param filters - 过滤条件
   * @returns Promise<User[]> 用户列表
   */
  async getUsers(filters: UserFilters): Promise<User[]> {
    // 实现
  }
}

// ✅ 总是声明变量和函数的类型
function processUserData(data: unknown): User[] {
  if (!Array.isArray(data)) {
    throw new Error("Invalid data format");
  }

  return data.map((item: unknown) => validateUserData(item));
}

// ✅ 创建必要的类型
interface UserFilters {
  readonly status?: UserStatus;
  readonly role?: UserRole;
  readonly dateRange?: DateRange;
}
```

---

## 🧠 个人习惯建议

### 开发思维

- **注释先行**: 所有组件、函数都先写注释再写实现
- **数据流聚焦**: 把光标焦点放在"数据流"上：从哪里来、到哪里去、被谁使用
- **假装新人**: 写完功能后**假装你不是作者**审查一遍：这谁看得懂？

### 数据流思考框架

```typescript
// ✅ 思考数据流的模板
const ComponentName = ({ data }: Props) => {
  // 1. 数据来源：data 从父组件传入
  // 2. 数据处理：如何转换和验证数据
  // 3. 状态管理：哪些数据需要本地状态
  // 4. 数据传递：如何向子组件传递数据
  // 5. 数据更新：数据变化如何影响 UI

  const processedData = useMemo(() => {
    // 数据处理逻辑
    return transformData(data);
  }, [data]);

  return (
    <div>
      {/* 数据流清晰可见 */}
      <ChildComponent data={processedData} />
    </div>
  );
};
```

---

## 🔧 问题定位检查清单

开发过程中遇到问题时，按以下顺序检查：

### 1. 架构规范检查
- [ ] atom 是否放在正确的 atoms 目录下？
- [ ] 接口是否重复定义？
- [ ] 是否遵循项目既定架构？

### 2. 依赖关系检查  
- [ ] 是否添加了必要的 Provider（如 ExportProvider）？
- [ ] hooks 是否在组件内部调用？
- [ ] 导入路径是否正确？

### 3. 组件使用检查
- [ ] 是否使用了不存在的组件（如 RangePicker）？
- [ ] 组件 API 是否正确（查阅官方文档）？
- [ ] 数据格式是否符合组件预期？

### 4. 数据流检查
- [ ] API 参数结构是否正确？
- [ ] 过滤参数是否正确传递？
- [ ] 数据源选择逻辑是否一致？

### 5. 开发流程检查
- [ ] 是否先提供了方案和计划？
- [ ] 是否独立开发各个功能模块？
- [ ] 是否等待用户确认再实施？

---

_记住：优秀的代码不仅能运行，更要让下一个读代码的人（包括未来的自己）能快速理解。_
