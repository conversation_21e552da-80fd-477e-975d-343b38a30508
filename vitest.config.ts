import react from "@vitejs/plugin-react";
import path from "path";
import { defineConfig } from "vitest/config";

export default defineConfig({
  plugins: [react()],
  test: {
    environment: "jsdom",
    setupFiles: ["./src/test/setup.ts"],
    globals: true,
    css: true,

    // 排除E2E测试文件
    exclude: [
      "**/node_modules/**",
      "**/dist/**",
      "**/e2e/**",
      "**/*.e2e.{test,spec}.{js,ts}",
      "**/*.spec.ts", // 排除Playwright测试文件
    ],

    // 性能优化配置
    threads: true,
    maxThreads: 4,
    testTimeout: 30000, // 增加到30秒，解决复杂测试的超时问题
    hookTimeout: 30000, // 增加到30秒，解决setup/teardown超时问题
    isolate: true,

    // 并行执行配置
    pool: "threads",
    poolOptions: {
      threads: {
        singleThread: false,
        isolate: true,
        // 内存优化配置
        maxThreads: 2, // 减少线程数以降低内存使用
        minThreads: 1,
        useAtomics: true,
      },
    },

    // 内存优化配置
    maxConcurrency: 2, // 限制并发测试数量
    sequence: {
      concurrent: false, // 禁用并发执行以减少内存压力
    },

    coverage: {
      provider: "v8",
      reporter: ["text", "json", "html", "lcov"],
      exclude: [
        "node_modules/",
        "src/test/",
        "**/*.d.ts",
        "**/*.config.*",
        "**/coverage/**",
        "**/dist/**",
        "**/build/**",
      ],
      thresholds: {
        global: {
          branches: 75,
          functions: 85,
          lines: 80,
          statements: 80,
        },
        "./src/pages/ticket/": {
          branches: 80,
          functions: 90,
          lines: 85,
          statements: 85,
        },
      },
    },
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
      "@api": path.resolve(__dirname, "./src/api"),
      "@atoms": path.resolve(__dirname, "./src/atoms"),
      "@components": path.resolve(__dirname, "./src/components"),
      "@types": path.resolve(__dirname, "./src/types"),
      "@utils": path.resolve(__dirname, "./src/utils"),
      "@pages": path.resolve(__dirname, "./src/pages"),
      api: path.resolve(__dirname, "./src/api"),
      atoms: path.resolve(__dirname, "./src/atoms"),
      components: path.resolve(__dirname, "./src/components"),
      hooks: path.resolve(__dirname, "./src/hooks"),
      utils: path.resolve(__dirname, "./src/utils"),
      types: path.resolve(__dirname, "./src/types"),
      config: path.resolve(__dirname, "./src/config"),
      mocks: path.resolve(__dirname, "./src/mocks"),
      pages: path.resolve(__dirname, "./src/pages"),
      routes: path.resolve(__dirname, "./src/routes"),
      data: path.resolve(__dirname, "./src/data"),
    },
  },
});
